import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
} from '@ui/forms';
import { getReportFindingsCompletionCounter } from '../helpers/soc-form-completion-counters.helper';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../vendors-security-reviews-soc-supporting-content-component';

export const ReportFindingsAccordionField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { watch } = useFormContext();
    const formValues: SocReviewFormValuesType = watch();

    const { completed, total } = getReportFindingsCompletionCounter(formValues);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label="Report Findings"
            data-testid="ReportFindingsAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title="Report findings"
                    data-id={`${dataId}-accordion`}
                    data-testid="ReportFindingsAccordionField"
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            total={total}
                            completed={completed}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <UniversalFormField
                                name={`${name}.noFindingsChecked`}
                                formId={formId}
                                data-id={`${dataId}-no-findings-field`}
                            />

                            <UniversalFormField
                                name={`${name}.findings`}
                                formId={formId}
                                data-id={`${dataId}-findings-field`}
                            />

                            <UniversalFormField
                                name={`${name}.hasMaterialImpact`}
                                formId={formId}
                                data-id={`${dataId}-material-impact-field`}
                            />
                        </Stack>
                    }
                />
            )}
        />
    );
};
