import { z } from 'zod';
import { sharedControlsInfiniteListController } from '@controllers/controls';
import { sharedRisksInfiniteListController } from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    CustomTaskFullResponseDto,
    UserCardResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getFullName, getInitials } from '@helpers/formatters';
import {
    getDayOptions,
    getTaskCardinalOptions,
    getTaskEndsOptions,
    getTaskFrequencyOptions,
    getTaskRepeatOnOptions,
    getTaskShortWeekdaysOptions,
    getTasksObjectTypeOptions,
    getTasksWeekdaysOptions,
} from '@models/tasks';
import type { FormSchema } from '@ui/forms';

const mapUserToListItem = (
    value?: UserCardResponseDto | null,
): ListBoxItemData | undefined => {
    if (!value) {
        return;
    }

    return {
        id: String(value.id),
        entryId: value.entryId,
        label: getFullName(value.firstName, value.lastName),
        value: String(value.id),
        description: value.email,
        avatar: {
            fallbackText: getInitials(`${value.firstName} ${value.lastName}`),
            imgSrc: value.avatarUrl ?? undefined,
            imgAlt: getFullName(value.firstName, value.lastName),
        },
    };
};

/**
 * Creates the task form schema definition with precise typing.
 * This function is used to generate both the runtime schema and the TypeScript types.
 */
export const createTaskFormSchema = (
    taskData?: CustomTaskFullResponseDto | null,
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- The return type is defined as const so we can derive the schema type. Can't specify return type
) => {
    const TASK_CARDINAL_OPTIONS = getTaskCardinalOptions();
    const TASK_ENDS_OPTIONS = getTaskEndsOptions();
    const TASK_FREQUENCY_OPTIONS = getTaskFrequencyOptions();
    const TASK_OBJECT_TYPE_OPTIONS = getTasksObjectTypeOptions();
    const TASK_REPEAT_ON_OPTIONS = getTaskRepeatOnOptions();
    const TASK_SHORT_WEEKDAYS_OPTIONS = getTaskShortWeekdaysOptions();
    const TASK_WEEKDAYS_OPTIONS = getTasksWeekdaysOptions();

    // frequency types
    const WEEKLY = TASK_FREQUENCY_OPTIONS[0];
    const MONTHLY = TASK_FREQUENCY_OPTIONS[1];
    const YEARLY = TASK_FREQUENCY_OPTIONS[2];
    // monthly repeat types
    const DAY_OF_MONTH = TASK_REPEAT_ON_OPTIONS[0].value;
    const DAY_OF_WEEK = TASK_REPEAT_ON_OPTIONS[1].value;
    // endsOn options
    const NEVER = TASK_ENDS_OPTIONS[0].value;
    const ON_DATE = TASK_ENDS_OPTIONS[1].value;
    // task types
    const CONTROL = TASK_OBJECT_TYPE_OPTIONS[1].value;
    const RISK = TASK_OBJECT_TYPE_OPTIONS[2].value;

    return {
        taskDetails: {
            header: '',
            type: 'group',
            fields: {
                title: {
                    type: 'text',
                    label: t`Title`,
                    initialValue: taskData?.title || '',
                    validator: z.string().min(1, t`Title is required`),
                },
                description: {
                    type: 'textarea',
                    label: t`Description`,
                    initialValue: taskData?.description || '',
                    isOptional: true,
                    validator: z.string().optional(),
                },
                owner: {
                    type: 'combobox',
                    label: t`Task owner`,
                    initialValue: mapUserToListItem(taskData?.assignee),
                    options:
                        sharedUsersInfiniteController.usersInfiniteListOptionsWithEntryId,
                    loaderLabel: t`Loading personnel...`,
                    placeholder: t`Search for personnel`,
                    getSearchEmptyState: () => t`No personnel found`,
                    hasMore: sharedUsersInfiniteController.hasNextPage,
                    isLoading: sharedUsersInfiniteController.isLoading,
                    onFetchOptions: sharedUsersInfiniteController.onFetchUsers,
                    validator: z.object(
                        {
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                            entryId: z.string(),
                        },
                        { message: t`Task owner is required` },
                    ),
                },
                dueDate: {
                    type: 'date',
                    label: t`Due date`,
                    initialValue: taskData?.dueDate as TDateISODate | undefined,
                    validator: z
                        .string({ message: t`Due date is required` })
                        .date(t`Select a valid due date`),
                },
                isRecurringTask: {
                    type: 'toggle',
                    label: t`Make this a recurring task`,
                    helpText: t`You can eliminate the need to manually schedule this in the future with a recurring task if it's something you plan on doing again.`,
                    initialValue: false,
                },
            },
        },
        recurring: {
            shownIf: {
                fieldName: 'taskDetails.isRecurringTask',
                operator: 'equals',
                value: true,
            },
            header: '', // TODO: add option to hide group headers?
            type: 'group',
            fields: {
                frequency: {
                    header: t`Repeat every`,
                    type: 'group',
                    fields: {
                        interval: {
                            shouldHideLabel: true,
                            type: 'text',
                            label: t`Repeat interval`,
                            initialValue: '1',
                            validator: z.coerce
                                .number({
                                    message: t`Cadence is required`,
                                })
                                .min(1, t`Cadence must be at lea st 1`)
                                .max(99, t`Cadence must be less than 100`),
                        },
                        type: {
                            shouldHideLabel: true,
                            type: 'select',
                            label: t`Repeat type`,
                            initialValue: WEEKLY,
                            loaderLabel: t`Loading...`,
                            options: TASK_FREQUENCY_OPTIONS,
                        },
                    },
                    showDivider: false,
                },
                repeats: {
                    shownIf: {
                        fieldName: 'recurring.frequency.type',
                        operator: 'notEquals',
                        value: YEARLY.value, // YEAR
                    },
                    header: t`Repeat on`,
                    type: 'group',
                    fields: {
                        on: {
                            shownIf: {
                                fieldName: 'recurring.frequency.type',
                                operator: 'equals',
                                value: MONTHLY.value,
                            },
                            shouldHideLabel: true,
                            type: 'radioGroup',
                            label: t`Repeat on`,
                            initialValue: DAY_OF_MONTH,
                            options: TASK_REPEAT_ON_OPTIONS,
                        },
                        dayOfMonth: {
                            shownIf: {
                                operator: 'and',
                                conditions: [
                                    {
                                        fieldName: 'recurring.frequency.type',
                                        operator: 'equals',
                                        value: MONTHLY.value,
                                    },
                                    {
                                        fieldName: 'recurring.repeats.on',
                                        operator: 'equals',
                                        value: DAY_OF_MONTH,
                                    },
                                ],
                            },
                            shouldHideLabel: true,
                            type: 'select',
                            label: t`Specific day of the month`,
                            initialValue: undefined,
                            options: getDayOptions(),
                        },
                        dayOfWeek: {
                            shownIf: {
                                operator: 'and',
                                conditions: [
                                    {
                                        fieldName: 'recurring.frequency.type',
                                        operator: 'equals',
                                        value: MONTHLY.value,
                                    },
                                    {
                                        fieldName: 'recurring.repeats.on',
                                        operator: 'equals',
                                        value: DAY_OF_WEEK,
                                    },
                                ],
                            },
                            type: 'group',
                            header: '',
                            fields: {
                                frequencyCardinal: {
                                    shouldHideLabel: true,
                                    type: 'select',
                                    label: t`Cardinal`,
                                    initialValue: TASK_CARDINAL_OPTIONS[0],
                                    options: TASK_CARDINAL_OPTIONS,
                                },
                                frequencyWeekday: {
                                    shouldHideLabel: true,
                                    type: 'select',
                                    label: t`Weekday`,
                                    initialValue: TASK_WEEKDAYS_OPTIONS[0],
                                    options: TASK_WEEKDAYS_OPTIONS,
                                },
                            },
                            showDivider: false,
                        },
                        weekdays: {
                            shownIf: {
                                fieldName: 'recurring.frequency.type',
                                operator: 'equals',
                                value: WEEKLY.value, // WEEKLY
                            },
                            shouldHideLabel: true,
                            type: 'checkboxGroup',
                            label: t`Weekday Options`,
                            initialValue: [],
                            options: TASK_SHORT_WEEKDAYS_OPTIONS,
                            cosmosUseWithCaution_forceOptionOrientation:
                                'horizontal',
                            validator: z
                                .array(z.string())
                                .min(1, t`Select at least one weekday`),
                        },
                    },
                    showDivider: false,
                },
            },
            showDivider: false,
        },
        ends: {
            shownIf: {
                fieldName: 'taskDetails.isRecurringTask',
                operator: 'equals',
                value: true,
            },
            type: 'group',
            header: t`Ends on`,
            fields: {
                on: {
                    shouldHideLabel: true,
                    type: 'radioGroup',
                    label: t`Ends`,
                    initialValue: NEVER,
                    options: TASK_ENDS_OPTIONS,
                    cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                },
                date: {
                    shownIf: {
                        fieldName: 'ends.on',
                        operator: 'equals',
                        value: ON_DATE,
                    },
                    shouldHideLabel: true,
                    type: 'date',
                    label: t`Ending date`,
                    initialValue: undefined,
                    validator: z.coerce.date({
                        message: t`End date is required`,
                    }),
                },
                // TODO: Create custom component to display next scheduled task date
                nextScheduledTaskDate: {
                    type: 'custom',
                    label: t`Next scheduled task date`,
                    render: () =>
                        'Next recurring task will be scheduled for: Apr 12, 2027',
                },
            },
        },
        taskType: {
            header: '',
            type: 'group',
            fields: {
                mappedTo: {
                    type: 'radioGroup',
                    label: t`Map an object to this task`,
                    initialValue: TASK_OBJECT_TYPE_OPTIONS[0].value,
                    options: TASK_OBJECT_TYPE_OPTIONS,
                    validator: z.enum(['NONE', 'CONTROL', 'RISK']),
                },
                control: {
                    shownIf: {
                        fieldName: 'taskType.mappedTo',
                        operator: 'equals',
                        value: CONTROL,
                    },
                    type: 'combobox',
                    label: t`Map control`,
                    initialValue: undefined,
                    options:
                        sharedControlsInfiniteListController.controlsComboboxOptions,
                    loaderLabel: t`Loading controls...`,
                    placeholder: t`Search for controls`,
                    getSearchEmptyState: () => t`No controls found`,
                    hasMore: sharedControlsInfiniteListController.hasNextPage,
                    isLoading: sharedControlsInfiniteListController.isLoading,
                    onFetchOptions:
                        sharedControlsInfiniteListController.onFetchControls,
                    validator: z.object(
                        {
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                        },
                        { message: t`Control is required` },
                    ),
                },
                risk: {
                    shownIf: {
                        fieldName: 'taskType.mappedTo',
                        operator: 'equals',
                        value: RISK,
                    },
                    type: 'combobox',
                    label: t`Map risk`,
                    initialValue: undefined,
                    options:
                        sharedRisksInfiniteListController.risksComboboxOptions,
                    loaderLabel: t`Loading risks...`,
                    placeholder: t`Search for risks`,
                    getSearchEmptyState: () => t`No risks found`,
                    hasMore: sharedRisksInfiniteListController.hasNextPage,
                    isLoading: sharedRisksInfiniteListController.isLoading,
                    onFetchOptions:
                        sharedRisksInfiniteListController.onFetchRisks,
                    validator: z.object(
                        {
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                        },
                        { message: t`Risk is required` },
                    ),
                },
            },
            showDivider: false,
        },
    } as const satisfies FormSchema;
};
