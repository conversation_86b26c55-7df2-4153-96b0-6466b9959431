import { sharedMonitoringUnmapMutationController } from '@controllers/monitoring-details';
import { sharedMonitorsController } from '@controllers/monitors';
import { Icon } from '@cosmos/components/icon';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { ControlsMonitoringCellProps } from '../types/controls-monitoring-cell.type';

export const MonitorActionsCell = ({
    row,
}: ControlsMonitoringCellProps): React.JSX.Element => {
    return (
        <SchemaDropdown
            isIconOnly
            label="Actions"
            level="tertiary"
            startIconName="Action"
            data-testid="MonitorActionsCell"
            data-id="s0nK5Fbh"
            colorScheme="neutral"
            items={[
                {
                    id: 'control-option',
                    label: t`Unmap test`,
                    type: 'item',
                    value: 'unlink',
                    startSlot: <Icon name="Unlink" size="200" />,
                    onClick: () => {
                        openConfirmationModal({
                            title: t`Unmap test?`,
                            body: t`Unmapping this test from this control will result in this test no longer automating evidence collection or monitoring readiness for this control.`,
                            confirmText: t`Unmap`,
                            cancelText: t`Cancel`,
                            type: 'danger',
                            onConfirm: action(() => {
                                const { controlId } = sharedMonitorsController;

                                sharedMonitoringUnmapMutationController.unmapMonitorFromControl(
                                    row.original.testId,
                                    controlId,
                                );

                                closeConfirmationModal();
                            }),
                            onCancel: closeConfirmationModal,
                        });
                    },
                },
            ]}
        />
    );
};
