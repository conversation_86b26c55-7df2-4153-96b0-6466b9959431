import { z } from 'zod';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { EMAIL_CONTENT_MAX_LENGTH } from '../constants';
import { getDefaultEmailContent } from '../helpers/default-email-content.constant';

class VendorsSettingsEmailContentFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    get vendorSettings() {
        return sharedVendorsSettingsController.vendorSettings;
    }

    getSchema(): FormSchema {
        return {
            emailContent: {
                type: 'textarea',
                label: t`Email content`,
                helpText: t`Customize the email vendors receive when you send a questionnaire.`,
                validator: z
                    .string()
                    .min(1, t`Email content is required`)
                    .max(
                        EMAIL_CONTENT_MAX_LENGTH,
                        t`Email content must be less than ${EMAIL_CONTENT_MAX_LENGTH} characters`,
                    ),
                initialValue:
                    this.vendorSettings?.emailContent ||
                    getDefaultEmailContent(),
                rows: 10,
            },
        };
    }
}

export const sharedVendorsSettingsEmailContentFormModel =
    new VendorsSettingsEmailContentFormModel();
