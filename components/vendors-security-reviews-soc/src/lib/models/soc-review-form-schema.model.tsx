import { isEmpty } from 'lodash-es';
import { z } from 'zod';
import { sharedVendorsSecurityReviewDocumentsController } from '@controllers/vendors';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import { t } from '@globals/i18n/macro';
import type { CustomFieldRenderProps, FormSchema } from '@ui/forms';
import {
    BRIDGE_LETTER_FILE_UPLOAD_ACCEPTED_FORMATS,
    CERTIFICATION_TYPE_PREFIX,
    COMPLIANCE_REPORT_SCOPE_CERTIFICATION_OPTIONS,
    COMPLIANCE_REPORT_SCOPE_TYPE_OPTIONS,
    CONTROLS_ENCOMPASS_BUSINESS_NEEDS_OPTIONS,
    DEFAULT_END_USER_CONTROL,
    DEFAULT_FINDING,
    END_USER_CONTROL_DOCUMENTED_IN_PLACE_OPTIONS,
    END_USER_CONTROL_IN_PLACE_OPTIONS,
    REPORT_FINDINGS_MATERIAL_IMPACT_OPTIONS,
    REPORT_OPINION_OPTIONS,
    STUB_REVIEWER_INFO_REVIEWER_OPTIONS,
    SUBSERVICE_ORGS_USE_INCLUSIVE_METHODS_OPTIONS,
    TRUST_SERVICE_CRITERIA_OPTIONS,
} from '../constants/vendors-security-reviews-soc.constants';
import { ComplianceScopeAccordionField } from '../custom-fields/compliance-scope-accordion-field.component';
import { CpaFirmAccordionField } from '../custom-fields/cpa-firm-accordion-field.component';
import { EndUserControlsAccordionField } from '../custom-fields/end-user-controls-accordion-field.component';
import { ReportFindingsAccordionField } from '../custom-fields/report-findings-accordion-field.component';
import { ReportOpinionAccordionField } from '../custom-fields/report-opinion-accordion-field.component';
import { ReviewerInfoAccordionField } from '../custom-fields/reviewer-info-accordion-field.component';
import { ServicesListedAccordionField } from '../custom-fields/services-listed-accordion-field.component';
import { SocEndUserControlsField } from '../custom-fields/soc-end-user-controls-field.component';
import { SocFindingsField } from '../custom-fields/soc-findings-field.component';
import { SocLocationsField } from '../custom-fields/soc-locations-field.component';
import { SocServicesField } from '../custom-fields/soc-services-field.component';
import { SubserviceOrgsAccordionField } from '../custom-fields/subservice-orgs-accordion-field.component';
import { getInitialAuditPeriod } from '../helpers/get-initial-audit-period.helper';
import {
    getInitialAllControlsInPlace,
    getInitialScopeType,
} from '../helpers/soc-form-initial-values.helper';

/**
 * SOC Review Form Schema Model
 * Each accordion is represented by a single custom field with its own render component
 * Uses existing controller for initial values.
 */
export const socReviewFormSchemaModel = {
    /**
     * Complete SOC Review Form Schema
     * Each accordion is a single custom field that handles all its internal inputs.
     */
    getCompleteFormSchema(): FormSchema {
        const { vendorReview } =
            sharedVendorsSecurityReviewDocumentsController.reviewDocument ?? {};

        const { bridgeLetterDocuments } =
            sharedVendorsSecurityReviewDocumentsController;

        const formattedBridgeLetterDocuments: CosmosFileObject[] = isEmpty(
            bridgeLetterDocuments,
        )
            ? []
            : bridgeLetterDocuments.map((document) => ({
                  file: new File([''], document.name, {
                      type: 'application/pdf',
                  }),
                  errors: [],
              }));

        return {
            reviewerInfo: {
                type: 'custom',
                label: 'Reviewer Information',
                render: (fieldProps: CustomFieldRenderProps) => (
                    <ReviewerInfoAccordionField
                        {...fieldProps}
                        reviewDate={vendorReview?.reviewDate}
                        data-id="RSlSL7bK"
                    />
                ),
                customType: 'object',
                fields: {
                    reviewer: {
                        type: 'select',
                        label: 'Reviewer',
                        options: STUB_REVIEWER_INFO_REVIEWER_OPTIONS, // TODO: fix reviewer
                        initialValue: STUB_REVIEWER_INFO_REVIEWER_OPTIONS[0],
                        validator: z.string().min(1, 'Reviewer is required'),
                    },
                    reportIssueDate: {
                        type: 'date',
                        label: 'SOC report issue date',
                        validator: z
                            .string()
                            .min(1, 'Report issue date is required'),
                        isMulti: false,
                        initialValue:
                            vendorReview?.reportIssueDate as TDateISODate,
                        /**
                         * Only today or past dates allowed in the locale date.
                         */
                        getIsDateUnavailable: (date) => {
                            return (
                                new Date(date).toISOString().split('T')[0] >
                                new Date().toISOString().split('T')[0]
                            );
                        },
                        dateUnavailableText: t`SOC report issue date not available`,
                    },
                },
            },

            complianceScope: {
                type: 'custom',
                label: 'Compliance Report Scope',
                render: ComplianceScopeAccordionField,
                customType: 'object',
                fields: {
                    certification: {
                        type: 'select',
                        label: 'Certification',
                        options: COMPLIANCE_REPORT_SCOPE_CERTIFICATION_OPTIONS,
                        initialValue:
                            COMPLIANCE_REPORT_SCOPE_CERTIFICATION_OPTIONS.find(
                                (option) =>
                                    option.value ===
                                    `${CERTIFICATION_TYPE_PREFIX}${vendorReview?.socReport}`,
                            ),
                        validator: z
                            .string()
                            .min(1, 'Certification is required'),
                    },
                    scopeType: {
                        type: 'radioGroup',
                        label: 'Type',
                        options: COMPLIANCE_REPORT_SCOPE_TYPE_OPTIONS,
                        initialValue: getInitialScopeType(vendorReview),
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        isOptional: true,
                        shownIf: {
                            fieldName: 'complianceScope.certification',
                            operator: 'notEquals',
                            value: 'SOC_3',
                        },
                    },
                    trustServiceCriteria: {
                        type: 'checkboxGroup',
                        label: 'Trust service criteria',
                        options: TRUST_SERVICE_CRITERIA_OPTIONS,
                        initialValue:
                            vendorReview?.trustServiceCategories?.map(
                                ({ category }) => String(category),
                            ) ?? [],
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                        isOptional: true,
                        shownIf: {
                            fieldName: 'complianceScope.certification',
                            operator: 'notEquals',
                            value: 'SOC_1',
                        },
                    },
                    auditPeriod: {
                        type: 'date',
                        label: 'Audit period',
                        initialValue: vendorReview
                            ? (getInitialAuditPeriod(
                                  vendorReview.socReportType1 || false,
                                  vendorReview,
                              ).start as TDateISODate)
                            : undefined,
                        locale: 'en-US',
                        isOptional: true,
                        shownIf: {
                            operator: 'and',
                            conditions: [
                                {
                                    operator: 'notEquals',
                                    fieldName: 'complianceScope.certification',
                                    value: 'SOC_3',
                                },
                                {
                                    operator: 'equals',
                                    fieldName: 'complianceScope.scopeType',
                                    value: 'type1',
                                },
                            ],
                        },
                    },
                    auditPeriodRange: {
                        type: 'dateRange',
                        initialValue: vendorReview
                            ? getInitialAuditPeriod(false, vendorReview)
                            : { start: null, end: null },
                        label: 'Audit period',
                        locale: 'en-US',
                        shownIf: {
                            operator: 'and',
                            conditions: [
                                {
                                    operator: 'notEquals',
                                    fieldName: 'complianceScope.certification',
                                    value: 'SOC_3',
                                },
                                {
                                    operator: 'equals',
                                    fieldName: 'complianceScope.scopeType',
                                    value: 'type2',
                                },
                            ],
                        },
                    },
                    // There is a bug. Ticket: https://drata.atlassian.net/browse/ENG-71811
                    bridgeLetter: {
                        type: 'file',
                        label: 'Bridge letter',
                        acceptedFormats:
                            BRIDGE_LETTER_FILE_UPLOAD_ACCEPTED_FORMATS,
                        maxFileSizeInBytes: 10 * 1024 * 1024, // 10MB
                        isMulti: false,
                        initialValue: formattedBridgeLetterDocuments,
                    },
                },
            },

            reportOpinion: {
                type: 'custom',
                label: 'Report Opinion',
                render: ReportOpinionAccordionField,
                customType: 'object',
                fields: {
                    reportOpinion: {
                        type: 'select',
                        label: 'Report opinion',
                        options: REPORT_OPINION_OPTIONS,
                        initialValue: REPORT_OPINION_OPTIONS.find(
                            (option) =>
                                option.value ===
                                String(vendorReview?.reportOpinion),
                        ),
                        validator: z
                            .string()
                            .min(1, 'Report opinion is required'),
                    },
                    encompassBusinessNeeds: {
                        type: 'radioGroup',
                        label: 'Do control objectives or trust principles encompass business needs?',
                        options: CONTROLS_ENCOMPASS_BUSINESS_NEEDS_OPTIONS,
                        initialValue: vendorReview?.encompassBusinessNeeds
                            ? 'YES'
                            : 'NO',
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                    },
                    followUpActivity: {
                        type: 'textarea',
                        label: 'Follow up activity if opinion is qualified',
                        isOptional: true,
                        initialValue: vendorReview?.followUpActivity ?? '',
                        rows: 4,
                        maxCharacters: 400,
                    },
                },
            },

            reportFindings: {
                type: 'custom',
                label: 'Report Findings',
                render: ReportFindingsAccordionField,
                customType: 'object',
                fields: {
                    noFindingsChecked: {
                        type: 'checkbox',
                        label: 'No findings identified in the report',
                        initialValue: !(
                            vendorReview?.findings &&
                            !isEmpty(vendorReview.findings)
                        ),
                    },
                    findings: {
                        type: 'custom',
                        label: 'Findings',
                        render: SocFindingsField,
                        initialValue:
                            vendorReview?.findings &&
                            !isEmpty(vendorReview.findings)
                                ? vendorReview.findings
                                : [{ ...DEFAULT_FINDING }],
                        isOptional: true,
                        shownIf: {
                            fieldName: 'reportFindings.noFindingsChecked',
                            operator: 'equals',
                            value: false,
                        },
                        fields: {
                            description: {
                                type: 'text',
                                label: 'Finding',
                                initialValue: '',
                                validator: z
                                    .string()
                                    .min(1, 'Finding description is required'),
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                    hasMaterialImpact: {
                        type: 'radioGroup',
                        label: 'Do the findings have any material impact on your control environment?',
                        options: REPORT_FINDINGS_MATERIAL_IMPACT_OPTIONS,
                        initialValue: vendorReview?.hasMaterialImpact
                            ? 'YES'
                            : 'NO',
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        isOptional: true,
                        shownIf: {
                            fieldName: 'reportFindings.noFindingsChecked',
                            operator: 'equals',
                            value: false,
                        },
                    },
                },
            },

            endUserControls: {
                type: 'custom',
                label: 'End User Controls',
                render: EndUserControlsAccordionField,
                customType: 'object',
                fields: {
                    noControlsChecked: {
                        type: 'checkbox',
                        label: 'No end-user controls identified in the report',
                        initialValue: !(
                            vendorReview?.userControls &&
                            !isEmpty(vendorReview.userControls)
                        ),
                    },
                    allControlsInPlace: {
                        type: 'radioGroup',
                        label: 'For applicable end-user controls documented, do you have controls in place?',
                        options: END_USER_CONTROL_DOCUMENTED_IN_PLACE_OPTIONS,
                        initialValue:
                            getInitialAllControlsInPlace(vendorReview),
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        isOptional: true,
                        shownIf: {
                            fieldName: 'endUserControls.noControlsChecked',
                            operator: 'equals',
                            value: false,
                        },
                    },
                    userControls: {
                        type: 'custom',
                        label: 'End User Controls',
                        render: SocEndUserControlsField,
                        initialValue:
                            vendorReview?.userControls &&
                            !isEmpty(vendorReview.userControls)
                                ? vendorReview.userControls.map((control) => ({
                                      ...control,
                                      inPlace: control.inPlace ? 'YES' : 'NO',
                                  }))
                                : [{ ...DEFAULT_END_USER_CONTROL }],
                        isOptional: true,
                        shownIf: {
                            fieldName: 'endUserControls.noControlsChecked',
                            operator: 'equals',
                            value: false,
                        },
                        fields: {
                            name: {
                                type: 'text',
                                label: 'Control',
                                validator: z
                                    .string()
                                    .min(1, 'Control name is required'),
                            },
                            inPlace: {
                                type: 'radioGroup',
                                label: 'Is this control in place?',
                                options: END_USER_CONTROL_IN_PLACE_OPTIONS,
                                cosmosUseWithCaution_forceOptionOrientation:
                                    'horizontal',
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                },
            },

            servicesListed: {
                type: 'custom',
                label: 'Services Listed',
                render: ServicesListedAccordionField,
                customType: 'object',
                initialValue: {
                    services:
                        vendorReview?.services &&
                        !isEmpty(vendorReview.services)
                            ? vendorReview.services
                            : [],
                    locations:
                        vendorReview?.locations &&
                        !isEmpty(vendorReview.locations)
                            ? vendorReview.locations
                            : [],
                },
                fields: {
                    services: {
                        type: 'custom',
                        label: 'Services',
                        render: SocServicesField,
                        initialValue: [],
                        fields: {
                            name: {
                                type: 'text',
                                label: 'Service',
                                initialValue: '',
                                validator: z
                                    .string()
                                    .min(1, 'Service name is required'),
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                    locations: {
                        type: 'custom',
                        label: 'Locations',
                        render: SocLocationsField,
                        initialValue: [],
                        fields: {
                            city: {
                                type: 'text',
                                label: 'City',
                                initialValue: '',
                                validator: z
                                    .string()
                                    .min(1, 'City is required'),
                            },
                            stateCountry: {
                                type: 'text',
                                label: 'State/Country',
                                initialValue: '',
                                validator: z
                                    .string()
                                    .min(1, 'State/Country is required'),
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                },
            },

            cpaFirm: {
                type: 'custom',
                label: 'CPA Firm',
                render: CpaFirmAccordionField,
                customType: 'object',
                initialValue: {
                    cpaFirm: vendorReview?.cpaFirm ?? '',
                    cpaProcedurePerformed:
                        vendorReview?.cpaProcedurePerformed ?? '',
                },
                fields: {
                    cpaFirm: {
                        type: 'text',
                        label: 'CPA firm that performed the audit',
                        initialValue: '',
                    },
                    cpaProcedurePerformed: {
                        type: 'textarea',
                        label: 'Procedures performed to assess reputation of CPA firm',
                        initialValue: '',
                        rows: 4,
                        maxCharacters: 30000,
                    },
                },
            },

            subserviceOrgs: {
                type: 'custom',
                label: 'Subservice Organizations',
                render: SubserviceOrgsAccordionField,
                customType: 'object',
                initialValue: {
                    subserviceOrganization:
                        vendorReview?.subserviceOrganization ?? '',
                    subserviceOrganizationUsingInclusiveMethod:
                        vendorReview?.subserviceOrganizationUsingInclusiveMethod
                            ? 'YES'
                            : 'NO',
                    subserviceOrganizationProcedurePerformed:
                        vendorReview?.subserviceOrganizationProcedurePerformed ??
                        '',
                },
                fields: {
                    subserviceOrganization: {
                        type: 'textarea',
                        label: 'Subservice organizations in report',
                        initialValue: '',
                        rows: 4,
                        maxCharacters: 30000,
                    },
                    subserviceOrganizationUsingInclusiveMethod: {
                        type: 'radioGroup',
                        label: 'Are subservice organizations presented in the report using the inclusive method?',
                        options: SUBSERVICE_ORGS_USE_INCLUSIVE_METHODS_OPTIONS,
                        initialValue: undefined,
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                    },
                    subserviceOrganizationProcedurePerformed: {
                        type: 'textarea',
                        label: 'Subservice organization procedure performed',
                        initialValue: '',
                        rows: 4,
                        maxCharacters: 30000,
                    },
                },
            },
        };
    },
};
