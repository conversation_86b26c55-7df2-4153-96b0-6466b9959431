import type React from 'react';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { Form, type FormValues, type useFormSubmit } from '@ui/forms';
import { sharedVendorsSettingsQuestionnaireRemindersFormModel } from '../models/vendors-settings-questionnaire-reminders-form.model.js';

interface VendorsSettingsQuestionnaireRemindersEditComponentProps {
    formRef: ReturnType<typeof useFormSubmit>['formRef'];
    isUpdating: boolean;
    onSubmit: (values: {
        enableFollowUpReminders: boolean;
        daysToSendReminder?: number;
    }) => void;
}

export const VendorsSettingsQuestionnaireRemindersEditComponent = observer(
    ({
        formRef,
        isUpdating,
        onSubmit,
    }: VendorsSettingsQuestionnaireRemindersEditComponentProps): React.JSX.Element => {
        const formSchema =
            sharedVendorsSettingsQuestionnaireRemindersFormModel.getSchema();

        const handleSubmit = (values: FormValues) => {
            const submitData = {
                enableFollowUpReminders:
                    values.enableFollowUpReminders as boolean,
                daysToSendReminder: values.daysToSendReminder
                    ? Number(values.daysToSendReminder as string)
                    : undefined,
            };

            onSubmit(submitData);
        };

        return (
            <Stack
                direction="column"
                gap="lg"
                data-testid="VendorsSettingsQuestionnaireRemindersEditComponent"
                data-id="questionnaire-reminders-edit"
            >
                <Form
                    hasExternalSubmitButton
                    ref={formRef}
                    schema={formSchema}
                    formId="questionnaire-reminders-form"
                    isReadOnly={isUpdating}
                    data-testid="QuestionnaireRemindersForm"
                    data-id="questionnaire-reminders-form"
                    onSubmit={handleSubmit}
                />
            </Stack>
        );
    },
);
