import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { AuditorSettingsProfileView } from '@views/audit-hub-auditor-settings-apikeys';

export const meta: MetaFunction = () => [
    { title: t`Audit hub - Settings - API Keys` },
];

const AuditorSettingsApiKeys = (): React.JSX.Element => {
    return (
        <AuditorSettingsProfileView
            data-testid="AuditorSettingsApiKeys"
            data-id="xP7Zt5Lq"
        />
    );
};

export default AuditorSettingsApiKeys;
