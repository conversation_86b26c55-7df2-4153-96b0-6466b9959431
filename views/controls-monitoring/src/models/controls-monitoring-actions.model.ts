import { openMapAutomatedTestModal } from '@components/map-tests-modal';
import { sharedMonitoringMapTestsMutationController } from '@controllers/monitoring-details';
import { sharedMonitorsController } from '@controllers/monitors';
import type { TableAction } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { FORM_ID, MAP_TESTS_MODAL_ID } from '../constants/modal.constant';
import { canManageControlTests } from '../helpers/can-manage-control-tests.helpers';
import { openControlsMonitoringCompareDefaultModal } from '../helpers/controls-monitoring-compare-default-modal.helper';
import { mapSelectedTests } from '../helpers/map-selected-tests.helper';

export class ControlsMonitoringActionsModel {
    get tableActions(): TableAction[] {
        const { controlId } = sharedMonitorsController;
        const { isMapping } = sharedMonitoringMapTestsMutationController;

        if (!canManageControlTests()) {
            return [];
        }

        return [
            {
                actionType: 'button',
                id: 'compare-button',
                typeProps: {
                    level: 'tertiary',
                    label: t`Compare to defaults`,
                    onClick: openControlsMonitoringCompareDefaultModal,
                },
            },
            {
                actionType: 'button',
                id: 'map-button',
                typeProps: {
                    level: 'primary',
                    label: t`Map tests`,
                    onClick: () => {
                        openMapAutomatedTestModal(
                            FORM_ID,
                            MAP_TESTS_MODAL_ID,
                            mapSelectedTests,
                            controlId,
                            isMapping,
                        );
                    },
                },
            },
        ];
    }
}

export const sharedControlsMonitoringActionsModel =
    new ControlsMonitoringActionsModel();
