import { VENDORS_CURRENT_PROFILE_HEADER_ACTION_VALUES } from '@components/vendors-current';
import { openArchiveVendorModalFromProfile } from '@components/vendors-current-archive-vendor';
import { openDeleteVendorModalFromProfile } from '@components/vendors-current-delete-vendor';
import { openRecurringReviewsModal } from '@components/vendors-recurring-reviews';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import type { Stack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';

export const buildCurrentHeaderActions = (): Stack[] => {
    const { data: vendor } = sharedVendorsDetailsController.vendorDetailsQuery;

    return VENDORS_CURRENT_PROFILE_HEADER_ACTION_VALUES.map((stack) => ({
        ...stack,
        id: `current-${vendor?.id}-${stack.id}`,
        actions: stack.actions.map((action) => {
            if (
                action.actionType === 'button' &&
                action.id.includes('recurring-reviews-button')
            ) {
                return {
                    ...action,
                    typeProps: {
                        ...action.typeProps,
                        onClick: () => {
                            if (vendor) {
                                openRecurringReviewsModal(vendor.id);
                            }
                        },
                    },
                };
            }
            if (
                action.actionType === 'dropdown' &&
                action.id.includes('horizontal-menu')
            ) {
                return {
                    ...action,
                    typeProps: {
                        ...action.typeProps,
                        items: action.typeProps.items.map((item) => {
                            switch (item.value) {
                                case 'DELETE_VENDOR': {
                                    return {
                                        ...item,
                                        onClick: () => {
                                            if (vendor) {
                                                openDeleteVendorModalFromProfile(
                                                    {
                                                        isProspective: false,
                                                    },
                                                );
                                            }
                                        },
                                    };
                                }
                                case 'ARCHIVE_VENDOR': {
                                    const isArchived =
                                        vendor?.status === 'ARCHIVED';

                                    return {
                                        ...item,
                                        label: isArchived
                                            ? t`Restore vendor`
                                            : t`Archive vendor`,
                                        onClick: () => {
                                            if (vendor) {
                                                if (isArchived) {
                                                    sharedVendorsDetailsController.restoreVendor(
                                                        vendor.id,
                                                    );
                                                } else {
                                                    openArchiveVendorModalFromProfile(
                                                        {
                                                            isProspective: false,
                                                        },
                                                    );
                                                }
                                            }
                                        },
                                    };
                                }
                                default: {
                                    return item;
                                }
                            }
                        }),
                    },
                };
            }

            return action;
        }),
    }));
};
