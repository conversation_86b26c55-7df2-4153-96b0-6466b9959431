import type {
    AccessReviewPeriodApplicationResponseDto,
    ClientTypeEnum,
} from '@globals/api-sdk/types';

export interface ApplicationData {
    id: number;
    name: string;
    websiteUrl: string;
    logo?: string | null;
    source:
        | 'DIRECT_CONNECTION'
        | 'MANUALLY_ADDED'
        | 'PARTNER_CONNECTION'
        | 'PARTNER_PUBLIC_CONNECTION';
    clientType?: ClientTypeEnum | null;
    hasFailed?: boolean | null;
    levelChange: number;
    formerPersonnel: number;
    missingMfa: number;
    unlinkedIdentities: number;
    serviceAccounts: number;
    totalWarnings: number;
}

export interface ApplicationDataRowCellProps {
    row: {
        original: AccessReviewPeriodApplicationResponseDto | ApplicationData;
    };
}
