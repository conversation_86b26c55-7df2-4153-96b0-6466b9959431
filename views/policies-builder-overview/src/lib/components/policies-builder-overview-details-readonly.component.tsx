import { useMemo } from 'react';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';

const LIMIT_TAGS_VIEW = 5;

export const PoliciesBuilderOverviewDetailsReadOnlyComponent = observer(
    (): React.JSX.Element => {
        const {
            policyName,
            policyDescription,
            renewalDateDisplay,
            assignedToText,
            shouldDisplaySLA,
            disclaimer,
            replacedPoliciesCount,
            frameworksDisplay,
        } = sharedPolicyBuilderModel;

        // Helper function for more items text
        const moreItemsText = useMemo(
            () => (itemsQty: number) => t`+${itemsQty} more`,
            [],
        );

        return (
            <Stack gap="4x" direction="column" data-id="DfwQ8_Mm">
                <KeyValuePair
                    type="TEXT"
                    label={t`Policy name`}
                    value={policyName}
                />

                <KeyValuePair
                    type="TEXT"
                    label={t`Renewal date`}
                    feedbackProps={renewalDateDisplay.feedbackProps}
                    value={renewalDateDisplay.value}
                />

                <KeyValuePair
                    type="TEXT"
                    label={t`Description`}
                    value={policyDescription}
                />

                {disclaimer && (
                    <KeyValuePair
                        type="TEXT"
                        label={t`Disclaimer`}
                        value={disclaimer}
                    />
                )}

                <KeyValuePair
                    type="TEXT"
                    label={t`Assigned to`}
                    value={assignedToText}
                />

                <KeyValuePair
                    label={t`Frameworks`}
                    visibleItemsLimit={LIMIT_TAGS_VIEW}
                    moreItemsText={moreItemsText}
                    type={frameworksDisplay.type}
                    value={frameworksDisplay.value}
                />

                <KeyValuePair
                    type="TEXT"
                    label={t`Policies replaced`}
                    value={replacedPoliciesCount}
                />

                {shouldDisplaySLA && (
                    <KeyValuePair
                        type="TEXT"
                        label={t`SLA`}
                        value={t`Monitored by Drata`}
                    />
                )}
            </Stack>
        );
    },
);
