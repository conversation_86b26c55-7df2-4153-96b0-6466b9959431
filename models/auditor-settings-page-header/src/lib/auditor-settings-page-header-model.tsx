import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { AppLink } from '@ui/app-link';

export class AuditorSettingsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return t`Settings`;
    }

    get backLink(): React.JSX.Element {
        return (
            <AppLink
                data-id="auditor-client-list-page-back-link"
                href="/audit-hub/clients"
                label={t`Back to Client List`}
            />
        );
    }
}
