import { isNil } from 'lodash-es';
import type { MonitorTrackStatus } from '@controllers/monitoring-details';
import type { ColorScheme } from '@cosmos/components/metadata';
import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export interface StatusMetadata {
    label: string;
    colorScheme: ColorScheme;
    type: 'status';
}

export function getStatusColorScheme(
    checkResultStatus?: MonitorTrackStatus,
): ColorScheme {
    let colorScheme: ColorScheme;

    switch (checkResultStatus) {
        case 'PASSED': {
            colorScheme = 'success';
            break;
        }
        case 'FAILED': {
            colorScheme = 'critical';
            break;
        }
        case 'ERROR': {
            colorScheme = 'warning';
            break;
        }
        case 'READY': {
            colorScheme = 'success';
            break;
        }
        case 'PREAUDIT': {
            colorScheme = 'neutral';
            break;
        }
        default: {
            colorScheme = 'neutral';
            break;
        }
    }

    return colorScheme;
}

export function getMonitorStatusMetadata(
    checkResultStatus?: MonitorV2ControlTestInstanceOverviewResponseDto['checkResultStatus'],
): StatusMetadata {
    if (isNil(checkResultStatus)) {
        return { label: '', colorScheme: 'neutral', type: 'status' };
    }

    let colorScheme: ColorScheme;
    let label: string;

    switch (checkResultStatus) {
        case 'PASSED': {
            colorScheme = 'success';
            label = t`Passed`;
            break;
        }
        case 'FAILED': {
            colorScheme = 'critical';
            label = t`Failed`;
            break;
        }
        case 'ERROR': {
            colorScheme = 'warning';
            label = t`Error`;
            break;
        }
        case 'READY': {
            colorScheme = 'success';
            label = t`Ready`;
            break;
        }
        case 'PREAUDIT': {
            colorScheme = 'neutral';
            label = t`Pre-Audit`;
            break;
        }
        default: {
            colorScheme = 'neutral';
            label = checkResultStatus;
            break;
        }
    }

    return { label, colorScheme, type: 'status' };
}

export function getMonitorCheckStatusMetadata(
    checkStatus?: MonitorV2ControlTestInstanceOverviewResponseDto['checkStatus'],
): StatusMetadata {
    if (isNil(checkStatus)) {
        return { label: '', colorScheme: 'neutral', type: 'status' };
    }

    let colorScheme: ColorScheme;
    let label: string;

    switch (checkStatus) {
        case 'ENABLED': {
            colorScheme = 'success';
            label = t`Enabled`;
            break;
        }
        case 'DISABLED': {
            colorScheme = 'neutral';
            label = t`Disabled`;
            break;
        }
        case 'UNUSED': {
            colorScheme = 'neutral';
            label = t`Unused`;
            break;
        }
        case 'TESTING': {
            colorScheme = 'neutral';
            label = t`Testing`;
            break;
        }
        default: {
            colorScheme = 'neutral';
            label = checkStatus;
            break;
        }
    }

    return { label, colorScheme, type: 'status' };
}
