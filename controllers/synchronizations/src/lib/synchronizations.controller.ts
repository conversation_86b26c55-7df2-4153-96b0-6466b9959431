import { snackbarController } from '@controllers/snackbar';
import { synchronizationsControllerResyncMutation } from '@globals/api-sdk/queries';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { sharedAsyncCompanySyncController } from '@globals/async-events';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { providers } from '@globals/providers';

class SynchronizationsController {
    synchronizationsQuery = new ObservedMutation(
        synchronizationsControllerResyncMutation,
    );
    isInitialized = false;
    isSyncing = false;

    constructor() {
        makeAutoObservable(this);
    }

    get hasError(): boolean {
        return this.synchronizationsQuery.hasError;
    }

    reSyncByClientType = (
        clientType: ClientTypeEnum,
        callback?: () => void,
    ) => {
        const { name } = providers[clientType as keyof typeof providers];

        if (this.isSyncing) {
            snackbarController.addSnackbar({
                id: 'sync-complete',
                props: {
                    title: t`Cannot synchronize while another synchronization is in progress`,
                    description: t`Please wait for the current synchronization to complete before trying again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return;
        }
        this.isSyncing = true;
        this.synchronizationsQuery.mutate({ body: { clientType } });

        when(
            () => !this.synchronizationsQuery.isPending,
            () => {
                if (this.hasError) {
                    this.isSyncing = false;
                    snackbarController.addSnackbar({
                        id: 'sync-error',
                        props: {
                            title: t`Unable to fetch application users at this time.`,
                            description: t`Click the question mark in the top right to reach technical support.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (callback) {
                    sharedAsyncCompanySyncController.setCallback(() => {
                        callback();
                        this.isSyncing = false;
                        snackbarController.addSnackbar({
                            id: 'sync-complete',
                            props: {
                                title: t`Provider ${name} data synchronized successfully`,
                                description: t`All information has been updated with the latest data`,
                                severity: 'success',
                                closeButtonAriaLabel: 'Close',
                            },
                        });
                    });
                }
            },
        );
    };
}

export const sharedSynchronizationsController =
    new SynchronizationsController();
