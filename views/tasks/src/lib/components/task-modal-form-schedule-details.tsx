import { observer } from '@globals/mobx';
import { TaskModalFormScheduleEnd } from './task-modal-form-schedule-end';
import { TaskModalFormScheduleInterval } from './task-modal-form-schedule-interval';
import { TaskModalFormScheduleMonthly } from './task-modal-form-schedule-monthly';
import { TaskModalFormScheduleOccurrence } from './task-modal-form-schedule-occurrence';
import { TaskModalFormScheduleWeekly } from './task-modal-form-schedule-weekly';

export const TaskModalFormScheduleDetails = observer(() => {
    return (
        <>
            <TaskModalFormScheduleInterval />
            <TaskModalFormScheduleWeekly />
            <TaskModalFormScheduleMonthly />
            <TaskModalFormScheduleEnd />
            <TaskModalFormScheduleOccurrence />
        </>
    );
});
