import { z } from 'zod';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { QuestionnaireReminderFrequencyField } from '../components/questionnaire-reminder-frequency-field.component';

class VendorsSettingsQuestionnaireRemindersFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    getSchema(): FormSchema {
        const { isQuestionnaireReminderEnabled, questionnaireReminderDays } =
            sharedVendorsSettingsController;

        return {
            enableFollowUpReminders: {
                type: 'checkbox',
                label: t`Follow-up reminders`,
                helpText: t`Automatically send reminder emails to vendors to complete questionnaires.`,
                validator: z.boolean(),
                initialValue: isQuestionnaireReminderEnabled,
            },
            daysToSendReminder: {
                type: 'custom',
                label: t`Reminder frequency`,
                render: QuestionnaireReminderFrequencyField,
                validator: z
                    .string()
                    .min(1, t`Enter number of days`)
                    .regex(/^\d+$/, t`Invalid number of days`)
                    .refine((value: string) => {
                        const num = parseInt(value, 10);

                        return num >= 3 && num <= 90;
                    }, t`Must be a number between 3 and 90 days`),
                shownIf: {
                    fieldName: 'enableFollowUpReminders',
                    operator: 'equals',
                    value: true,
                },
                initialValue: isQuestionnaireReminderEnabled
                    ? String(questionnaireReminderDays)
                    : '',
            },
        };
    }
}

export const sharedVendorsSettingsQuestionnaireRemindersFormModel =
    new VendorsSettingsQuestionnaireRemindersFormModel();
