import { describe, expect, test } from 'vitest';
import type { VendorReviewResponseDto } from '@globals/api-sdk/types';
import {
    getInitialAllControlsInPlace,
    getInitialScopeType,
} from './soc-form-initial-values.helper';

describe('soc-form-initial-values.helper', () => {
    describe('getInitialAllControlsInPlace', () => {
        test('should return undefined when vendorReview is null', () => {
            const result = getInitialAllControlsInPlace(null);

            expect(result).toBeUndefined();
        });

        test('should return undefined when vendorReview is undefined', () => {
            const result = getInitialAllControlsInPlace(undefined);

            expect(result).toBeUndefined();
        });

        test('should return undefined when userControls is undefined', () => {
            const vendorReview = {
                userControls: undefined,
            } as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBeUndefined();
        });

        test('should return undefined when userControls is empty array', () => {
            const vendorReview = {
                userControls: [],
            } as unknown as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBeUndefined();
        });

        test('should return YES_TO_ALL when all controls are in place', () => {
            const vendorReview = {
                userControls: [
                    { id: 1, name: 'Control 1', inPlace: true },
                    { id: 2, name: 'Control 2', inPlace: true },
                    { id: 3, name: 'Control 3', inPlace: true },
                ],
            } as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBe('YES_TO_ALL');
        });

        test('should return NO_TO_ALL when no controls are in place', () => {
            const vendorReview = {
                userControls: [
                    { id: 1, name: 'Control 1', inPlace: false },
                    { id: 2, name: 'Control 2', inPlace: false },
                    { id: 3, name: 'Control 3', inPlace: false },
                ],
            } as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBe('NO_TO_ALL');
        });

        test('should return undefined when controls have mixed values', () => {
            const vendorReview = {
                userControls: [
                    { id: 1, name: 'Control 1', inPlace: true },
                    { id: 2, name: 'Control 2', inPlace: false },
                    { id: 3, name: 'Control 3', inPlace: true },
                ],
            } as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBeUndefined();
        });

        test('should return YES_TO_ALL for single control that is in place', () => {
            const vendorReview = {
                userControls: [{ id: 1, name: 'Control 1', inPlace: true }],
            } as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBe('YES_TO_ALL');
        });

        test('should return NO_TO_ALL for single control that is not in place', () => {
            const vendorReview = {
                userControls: [{ id: 1, name: 'Control 1', inPlace: false }],
            } as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBe('NO_TO_ALL');
        });

        test('should handle controls with missing inPlace property as falsy', () => {
            const vendorReview = {
                userControls: [
                    { id: 1, name: 'Control 1' }, // missing inPlace property
                    { id: 2, name: 'Control 2', inPlace: false },
                ],
            } as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBe('NO_TO_ALL');
        });

        test('should handle controls with null inPlace property as falsy', () => {
            const vendorReview = {
                userControls: [
                    { id: 1, name: 'Control 1', inPlace: null },
                    { id: 2, name: 'Control 2', inPlace: false },
                ],
            } as unknown as VendorReviewResponseDto;

            const result = getInitialAllControlsInPlace(vendorReview);

            expect(result).toBe('NO_TO_ALL');
        });
    });

    describe('getInitialScopeType', () => {
        test('should return undefined when vendorReview is null', () => {
            const result = getInitialScopeType(null);

            expect(result).toBeUndefined();
        });

        test('should return undefined when vendorReview is undefined', () => {
            const result = getInitialScopeType(undefined);

            expect(result).toBeUndefined();
        });

        test('should return type1 when socReportType1 is true', () => {
            const vendorReview = {
                socReportType1: true,
                socReportType2: false,
            } as VendorReviewResponseDto;

            const result = getInitialScopeType(vendorReview);

            expect(result).toBe('type1');
        });

        test('should return type2 when socReportType2 is true', () => {
            const vendorReview = {
                socReportType1: false,
                socReportType2: true,
            } as VendorReviewResponseDto;

            const result = getInitialScopeType(vendorReview);

            expect(result).toBe('type2');
        });

        test('should return type1 when both socReportType1 and socReportType2 are true (type1 has priority)', () => {
            const vendorReview = {
                socReportType1: true,
                socReportType2: true,
            } as VendorReviewResponseDto;

            const result = getInitialScopeType(vendorReview);

            expect(result).toBe('type1');
        });

        test('should return undefined when both socReportType1 and socReportType2 are false', () => {
            const vendorReview = {
                socReportType1: false,
                socReportType2: false,
            } as VendorReviewResponseDto;

            const result = getInitialScopeType(vendorReview);

            expect(result).toBeUndefined();
        });

        test('should return undefined when socReportType properties are missing', () => {
            const vendorReview = {} as VendorReviewResponseDto;

            const result = getInitialScopeType(vendorReview);

            expect(result).toBeUndefined();
        });
    });
});
