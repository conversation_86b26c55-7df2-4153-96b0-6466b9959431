import type { ClientLoader } from '@app/types';
import {
    sharedAccessReviewController,
    sharedActiveAccessReviewPeriodsController,
} from '@controllers/access-reviews';
import {
    sharedUsersController,
    sharedUsersInfiniteController,
} from '@controllers/users';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: t`Access Review` }];
};

class AccessReviewPageContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return t`Access Review`;
    }
}

export const clientLoader = action((): ClientLoader => {
    // Use the controller's loadAccessReview method with default parameters instead of calling ObservedQuery directly
    sharedAccessReviewController.loadAccessReview({
        pagination: {
            page: 1,
            pageSize: DEFAULT_PAGE_SIZE,
            pageIndex: 0,
            pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
        },
        globalFilter: { search: '', filters: {} },
        sorting: [],
    });
    sharedActiveAccessReviewPeriodsController.activeAccessReviewPeriods.load();
    sharedUsersController.loadReviewerUsers();
    sharedUsersInfiniteController.loadUsers({
        roles: ['ADMIN', 'REVIEWER'],
    });

    // Check if Access Review entitlement is enabled
    const { isAccessReviewReadEnabled } = sharedFeatureAccessModel;

    // Base tabs - Applications is always shown
    const baseTabs = [
        {
            id: 'governance.access-review.applications',
            topicPath: `governance/access-review/applications`,
            label: t`Applications`,
        },
    ];

    // Additional tabs - only shown when Access Review entitlement is enabled
    const fullAccessTabs = [
        {
            id: 'governance.access-review.active',
            topicPath: `governance/access-review/active`,
            label: t`Active review`,
        },
        {
            id: 'governance.access-review.completed',
            topicPath: `governance/access-review/completed`,
            label: t`Completed reviews`,
        },
    ];

    // Show all tabs if entitlement is enabled, otherwise only Applications tab
    const tabs = isAccessReviewReadEnabled
        ? [...baseTabs, ...fullAccessTabs]
        : baseTabs;

    return {
        pageHeader: new AccessReviewPageContentNavModel(),
        tabs,
    };
});

const AccessReview = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AccessReview"
            data-id="MviHNu9x"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AccessReview;
