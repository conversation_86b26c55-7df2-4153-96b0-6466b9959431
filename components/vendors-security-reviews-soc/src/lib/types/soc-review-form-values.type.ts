import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { CosmosFileObject } from '@cosmos/components/file-upload';

/**
 * SOC Review Form Values Type.
 *
 * This type defines the structure of form values for the SOC review form,
 * following the same pattern as CreateRiskCustomMutationType.
 *
 * Each section corresponds to an accordion in the SOC review form.
 */
export interface SocReviewFormValuesType {
    /**
     * Reviewer Information Accordion.
     * Required fields: reviewer, reportIssueDate.
     */
    reviewerInfo?: {
        reviewer?: { id: string; label: string; value: string };
        reportIssueDate: TDateISODate;
    };

    /**
     * Compliance Report Scope Accordion.
     * Dynamic fields based on SOC type.
     */
    complianceScope?: {
        certification?: {
            id: string;
            label: string;
            value: 'SOC_1' | 'SOC_2' | 'SOC_3';
        };
        scopeType?: 'type1' | 'type2';
        trustServiceCriteria?: string[];
        auditPeriod?: TDateISODate;
        auditPeriodRange?: {
            start: TDateISODate | null;
            end: TDateISODate | null;
        };
        bridgeLetter?: CosmosFileObject[];
    };

    /**
     * Report Opinion Accordion.
     * Required field: reportOpinion.
     */
    reportOpinion?: {
        reportOpinion?: {
            id: string;
            label: string;
            value: 'UNQUALIFIED' | 'QUALIFIED' | 'ADVERSE' | 'DISCLAIMER';
        };
        encompassBusinessNeeds?: 'YES' | 'NO';
        followUpActivity?: string;
    };

    /**
     * Report Findings Accordion.
     * Conditional logic based on noFindingsChecked.
     */
    reportFindings?: {
        noFindingsChecked?: boolean;
        findings?: {
            id?: number;
            description: string;
        }[];
        hasMaterialImpact?: 'YES' | 'NO';
    };

    /**
     * End User Controls Accordion.
     * Conditional logic based on noControlsChecked.
     */
    endUserControls?: {
        noControlsChecked?: boolean;
        allControlsInPlace?: 'YES_TO_ALL' | 'NO_TO_ALL' | '';
        userControls?: {
            id?: number;
            name: string;
            inPlace: 'YES' | 'NO';
        }[];
    };

    /**
     * Services Listed Accordion (Optional).
     * Contains services and locations arrays.
     */
    servicesListed?: {
        services?: {
            id?: number;
            name: string;
        }[];
        locations?: {
            id?: number;
            city: string;
            stateCountry: string;
        }[];
    };

    /**
     * CPA Firm Accordion (Optional).
     * Contains firm information and procedures.
     */
    cpaFirm?: {
        cpaFirm?: string;
        cpaProcedurePerformed?: string;
    };

    /**
     * Subservice Organizations Accordion (Optional).
     * Contains organization details and methods.
     */
    subserviceOrgs?: {
        subserviceOrganization?: string;
        subserviceOrganizationUsingInclusiveMethod?: 'YES' | 'NO';
        subserviceOrganizationProcedurePerformed?: string;
    };
}

/**
 * Union types for form field values.
 */
export type YesNoOption = 'YES' | 'NO';
