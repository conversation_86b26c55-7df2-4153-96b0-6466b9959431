import type { ColorScheme } from '@cosmos/components/metadata';
import type { AuditListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export function getAuditStatusLabel(status: AuditListResponseDto['status']): {
    label: string;
    colorScheme: ColorScheme;
} {
    switch (status) {
        case 'ACTIVE':
        case 'PREPARING': {
            return { label: t`Active`, colorScheme: 'success' };
        }
        case 'COMPLETED': {
            return { label: t`Completed`, colorScheme: 'neutral' };
        }
        default: {
            return { label: '—', colorScheme: 'neutral' };
        }
    }
}
