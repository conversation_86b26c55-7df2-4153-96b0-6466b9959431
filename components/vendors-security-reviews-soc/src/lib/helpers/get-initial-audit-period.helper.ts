import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { VendorReviewResponseDto } from '@globals/api-sdk/types';
import { isTDateISODate } from '@helpers/date-time';

/**
 * Determines the initial audit period based on the SOC report type.
 *
 * @param isSocReportType1 - Whether this is for SOC report type 1 (true) or type 2 (false).
 * @param vendorReview - The vendor review data containing audit period dates.
 * @returns An object with start and end dates, or null values if dates are invalid.
 */
export const getInitialAuditPeriod = (
    isSocReportType1: boolean,
    vendorReview: VendorReviewResponseDto | null | undefined,
): { start: TDateISODate | null; end: TDateISODate | null } => {
    if (!vendorReview) {
        return { start: null, end: null };
    }

    if (isSocReportType1) {
        return {
            start: isTDateISODate(vendorReview.socType1StartDate)
                ? vendorReview.socType1StartDate
                : null,
            end: isTDateISODate(vendorReview.socType1EndDate)
                ? vendorReview.socType1EndDate
                : null,
        };
    }

    return {
        start: isTDateISODate(vendorReview.socType2StartDate)
            ? vendorReview.socType2StartDate
            : null,
        end: isTDateISODate(vendorReview.socType2EndDate)
            ? vendorReview.socType2EndDate
            : null,
    };
};
