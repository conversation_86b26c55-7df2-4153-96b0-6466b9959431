import { isEmpty } from 'lodash-es';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { activeAssetController } from '@controllers/asset';
import { sharedAssetCustomFieldsController } from '@controllers/assets';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { AssetDetailsCardBody } from './asset-details-card-body';
import { AssetsDetailsFormComponent } from './assets-details-form-component';

interface AssetDetailsComponentProps {
    isEditable?: boolean;
}

export const AssetsDetailsComponent = observer(
    ({ isEditable = true }: AssetDetailsComponentProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const {
            assetDetails,
            updateAssetDetailsMethod,
            updateAssetDetailsIsPending,
            updateAssetDetailsHasError,
        } = activeAssetController;
        const { assetCustomFieldsByAssetId } =
            sharedAssetCustomFieldsController;

        const handleOnSubmit = (values: FormValues) => {
            if (!assetDetails?.id) {
                return;
            }

            updateAssetDetailsMethod(assetDetails.id, values);
        };

        return (
            <ViewEditCardComponent
                title={t`Details`}
                readOnlyComponent={<AssetDetailsCardBody />}
                data-testid="AssetsDetailsComponent"
                data-id="HKZa20OT"
                isMutationPending={updateAssetDetailsIsPending}
                hasMutationError={updateAssetDetailsHasError}
                editComponent={
                    (isEditable &&
                        assetDetails?.assetReferenceType === 'OTHER') ||
                    !isEmpty(assetCustomFieldsByAssetId) ? (
                        <AssetsDetailsFormComponent
                            formRef={formRef}
                            state={assetDetails}
                            onSubmit={handleOnSubmit}
                        />
                    ) : null
                }
                onSave={triggerSubmit}
            />
        );
    },
);
