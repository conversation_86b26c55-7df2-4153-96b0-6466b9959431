import { useCallback, useMemo, useState } from 'react';
import { CompletedReviewActionCell } from '@components/access-review';
import {
    sharedAccessReviewCompletedRequestChangesController,
    sharedAccessReviewPeriodApplicationUserController,
} from '@controllers/access-reviews';
import type {
    DatatableProps,
    FetchDataResponseParams,
} from '@cosmos/components/datatable';
import type { AccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { CompletedReviewPeriodPresentation } from './completed-review-period-presentation.component';

/**
 * Period data structure for completed reviews.
 */
interface CompletedReviewPeriodData {
    id: number;
    startDate: string;
    endDate: string;
    completedAt?: string;
}

interface CompletedReviewPeriodContainerProps {
    applications: AccessReviewPeriodApplicationResponseDto[];
    startDate: string;
    endDate: string;
    id: number;
    isLoading: boolean;
    datatableColumns: DatatableProps<AccessReviewPeriodApplicationResponseDto>['columns'];
    onEditReview: (
        periodId: number,
        startDate: string,
        endDate: string,
    ) => void;
}

/**
 * Creates columns with period context injected into the actions column.
 */
const createColumnsWithPeriodContext = (
    periodData: CompletedReviewPeriodData,
    datatableColumns: DatatableProps<AccessReviewPeriodApplicationResponseDto>['columns'],
): DatatableProps<AccessReviewPeriodApplicationResponseDto>['columns'] => {
    return datatableColumns.map((column) => {
        if (column.id === 'actions') {
            return {
                ...column,
                cell: ({
                    row,
                }: {
                    row: { original: AccessReviewPeriodApplicationResponseDto };
                }) => (
                    <CompletedReviewActionCell
                        row={row}
                        data-id="ennW3Yv_"
                        periodData={periodData}
                    />
                ),
            };
        }

        return column;
    });
};

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 10;

export const CompletedReviewPeriodContainer = observer(
    ({
        applications,
        startDate,
        endDate,
        id,
        isLoading,
        datatableColumns,
        onEditReview,
    }: CompletedReviewPeriodContainerProps): React.JSX.Element => {
        const [currentPage, setCurrentPage] = useState(1);
        const [pageSize, setPageSize] = useState(10);

        const navigate = useNavigate();

        const { downloadEvidencePackage } =
            sharedAccessReviewPeriodApplicationUserController;

        const { activePeriodsData } =
            sharedAccessReviewCompletedRequestChangesController;

        const workspaceId =
            sharedWorkspacesController.currentWorkspace?.id ?? 1;

        const { hasLimitedAccess } = sharedFeatureAccessModel;

        const sortedApplications = applications.toSorted((a, b) =>
            a.name.localeCompare(b.name),
        );

        const paginatedApplications = useMemo(() => {
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;

            return sortedApplications.slice(startIndex, endIndex);
        }, [sortedApplications, currentPage, pageSize]);

        const handleFetchData = useCallback(
            (params: FetchDataResponseParams) => {
                setCurrentPage(params.pagination.page || DEFAULT_PAGE);
                setPageSize(params.pagination.pageSize || DEFAULT_PAGE_SIZE);
            },
            [],
        );

        const periodData: CompletedReviewPeriodData = useMemo(
            () => ({
                id,
                startDate,
                endDate,
                completedAt: new Date().toISOString(),
            }),
            [id, startDate, endDate],
        );

        const columnsWithPeriodContext = useMemo(
            () => createColumnsWithPeriodContext(periodData, datatableColumns),
            [periodData, datatableColumns],
        );

        const handleRowClick = useCallback(
            (row: AccessReviewPeriodApplicationResponseDto) => {
                navigate(
                    `/workspaces/${workspaceId}/governance/access-review/completed/period/${row.reviewPeriodId}/applications/${row.id}`,
                );
            },
            [navigate, workspaceId],
        );

        const handleDownloadEvidence = useCallback(() => {
            downloadEvidencePackage({ periodId: id });
        }, [downloadEvidencePackage, id]);

        const handleEditReviewClick = useCallback(() => {
            onEditReview(id, startDate, endDate);
        }, [onEditReview, id, startDate, endDate]);

        return (
            <CompletedReviewPeriodPresentation
                id={id}
                startDate={startDate}
                endDate={endDate}
                isLoading={isLoading}
                sortedApplications={sortedApplications}
                paginatedApplications={paginatedApplications}
                columnsWithPeriodContext={columnsWithPeriodContext}
                hasLimitedAccess={hasLimitedAccess}
                activePeriodsData={activePeriodsData}
                workspaceId={workspaceId}
                data-id="_kb_Rz_g"
                onFetchData={handleFetchData}
                onRowClick={handleRowClick}
                onDownloadEvidence={handleDownloadEvidence}
                onEditReview={handleEditReviewClick}
            />
        );
    },
);
