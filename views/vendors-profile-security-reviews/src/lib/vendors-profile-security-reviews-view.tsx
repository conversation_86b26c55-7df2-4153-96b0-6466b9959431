import { isEmpty } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import {
    getVendorsSecurityReviewsTableColumns,
    useVendorsSecurityReviewsTableActions,
    VENDORS_SECURITY_REVIEWS_DEFAULT_PAGINATION_OPTIONS,
    VENDORS_SECURITY_REVIEWS_DEFAULT_SORTING,
} from '@components/vendors-security-reviews';
import {
    sharedVendorsDetailsController,
    sharedVendorsProfileSecurityReviewsController,
} from '@controllers/vendors';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const VendorsProfileSecurityReviewsView = observer(
    (): React.JSX.Element => {
        const { paginatedSecurityReviews, loadPaginatedSecurityReviews } =
            sharedVendorsProfileSecurityReviewsController;
        const { vendorDetails } = sharedVendorsDetailsController;

        const isProspective = vendorDetails?.status === 'PROSPECTIVE';
        const securityReviewsData = paginatedSecurityReviews.data?.data ?? [];
        const hasExistingReview = !isEmpty(securityReviewsData);

        // For prospective vendors: hide table actions if there's already a review (max 1 allowed)
        const shouldShowTableActions = !isProspective || !hasExistingReview;
        const tableActionsFromHook = useVendorsSecurityReviewsTableActions();
        const tableActions = shouldShowTableActions
            ? tableActionsFromHook
            : undefined;
        const defaultPaginationOptions = isProspective
            ? undefined
            : VENDORS_SECURITY_REVIEWS_DEFAULT_PAGINATION_OPTIONS;

        return (
            <Grid
                gap="6x"
                data-testid="VendorsProfileSecurityReviewsView"
                data-id="rWHNQhZb"
            >
                <AppDatatable
                    columns={getVendorsSecurityReviewsTableColumns()}
                    isSortable={!isProspective}
                    data={securityReviewsData}
                    data-id="datatable-vendors-profile-security-reviews"
                    isLoading={paginatedSecurityReviews.isLoading}
                    tableId="datatable-vendors-profile-security-reviews"
                    total={paginatedSecurityReviews.data?.total ?? 0}
                    isRowSelectionEnabled={false}
                    initialSorting={VENDORS_SECURITY_REVIEWS_DEFAULT_SORTING}
                    hidePagination={isProspective}
                    tableActions={tableActions}
                    defaultPaginationOptions={defaultPaginationOptions}
                    tableSearchProps={{
                        placeholder: t`Search security review`,
                        hideSearch: isProspective,
                    }}
                    onFetchData={loadPaginatedSecurityReviews}
                />
            </Grid>
        );
    },
);
