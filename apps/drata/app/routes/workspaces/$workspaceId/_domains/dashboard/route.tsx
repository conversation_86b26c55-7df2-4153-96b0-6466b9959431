import type { ClientLoader } from '@app/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedDashboardModel } from '@models/dashboard';
import { type MetaFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Dashboard' }];
};

export const clientLoader = action((): ClientLoader => {
    const { isDashboardDomainReadEnabled } = sharedFeatureAccessModel;

    if (!isDashboardDomainReadEnabled) {
        throw new Error(t`Missing permission to access Dashboard`, {
            cause: '403',
        });
    }
    const { isWorkspacesEnabled } = sharedDashboardModel;

    if (isWorkspacesEnabled) {
        return {
            pageHeader: {
                title: t`Dashboard`,
            },

            tabs: [
                {
                    topicPath: 'dashboard/insights',
                    label: t`Insights`,
                },
                {
                    topicPath: 'dashboard/all-workspaces',
                    label: t`All workspaces`,
                    metadata: {
                        label: t`Beta`,
                        type: 'status',
                        colorScheme: 'education',
                    },
                },
            ],
        };
    }

    return {
        pageHeader: {
            title: t`Dashboard`,
        },
    };
});

const DashboardDomain = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="DashboardDomain"
            data-id="ISzzDb1x"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default DashboardDomain;
