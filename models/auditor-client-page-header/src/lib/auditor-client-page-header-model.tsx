import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedAuthController } from '@controllers/auth';
import { sharedRolesAdministrationController } from '@controllers/role-administration';
import { ActionStack } from '@cosmos/components/action-stack';
import { Box } from '@cosmos/components/box';
import { Text } from '@cosmos/components/text';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, reaction } from '@globals/mobx';
import {
    generateFallbackText,
    getFullName,
    getInitials,
} from '@helpers/formatters';
import { AppLink } from '@ui/app-link';

/**
 * Page header model for auditor client pages.
 * Manages client-specific data loading and authentication state.
 */
export class AuditorClientPageHeaderModel {
    data: { clientId: string } = { clientId: '' };
    isRefreshingCompany = false;
    reactionDisposers: (() => void)[] = [];

    constructor(data?: { clientId: string }) {
        if (data) {
            this.data = data;
        }
        makeAutoObservable(this);
        this.setupReactions();
    }

    dispose = (): void => {
        this.reactionDisposers.forEach((dispose) => {
            dispose();
        });
        this.reactionDisposers = [];
    };

    setupReactions = (): void => {
        const authAttemptDisposer = reaction(
            () => sharedAuthController.isAttemptingLogin,
            (isAttemptingLogin) => {
                if (isAttemptingLogin) {
                    this.isRefreshingCompany = true;
                }
            },
        );

        const accessTokenDisposer = reaction(
            () => sharedAuthController.accessToken,
            (newAccessToken, previousAccessToken) => {
                if (!newAccessToken || newAccessToken === previousAccessToken) {
                    return;
                }
                this.refreshCompanyData();
            },
        );

        this.reactionDisposers.push(authAttemptDisposer, accessTokenDisposer);
    };

    refreshCompanyData = (): void => {
        sharedCurrentCompanyController.companyQuery.invalidate();
        sharedRolesAdministrationController.userRolesPaginatedQuery.invalidate();
        sharedRolesAdministrationController.loadUserRoles();

        const loadingCompletionDisposer = reaction(
            () => sharedCurrentCompanyController.isLoading,
            (isLoading) => {
                if (isLoading) {
                    return;
                }
                this.isRefreshingCompany = false;
                loadingCompletionDisposer();
            },
            { fireImmediately: true },
        );
    };

    get backLink(): React.JSX.Element {
        return (
            <AppLink
                data-id="auditor-client-page-back-link"
                href="/audit-hub/clients"
                label={t`Back to Clients`}
            />
        );
    }

    get title(): string {
        return '';
    }

    get isLoading(): boolean {
        return (
            sharedAuthController.isAttemptingLogin ||
            this.isRefreshingCompany ||
            sharedCurrentCompanyController.isLoading
        );
    }

    get slot(): React.JSX.Element {
        const { company } = sharedCurrentCompanyController;

        if (this.isLoading || !company) {
            return (
                <OrganizationIdentity
                    primaryLabel={t`Loading...`}
                    fallbackText="L"
                    imgSrc={undefined}
                />
            );
        }

        return (
            <OrganizationIdentity
                primaryLabel={company.name || t`Client`}
                fallbackText={generateFallbackText(company.name)}
                imgSrc={company.logoUrl}
            />
        );
    }

    get actionStack(): React.JSX.Element | undefined {
        const { company } = sharedCurrentCompanyController;
        const { auditorDetails } = sharedAuditHubAuditorClientAuditController;

        const { clientId } = this.data;

        if (!company || !clientId || !auditorDetails?.readOnly) {
            return undefined;
        }

        return (
            <ActionStack
                data-id="auditor-client-page-header-action-stack"
                actions={[
                    {
                        actionType: 'button',
                        id: 'view-read-only-mode',
                        typeProps: {
                            label: t`View in read-only mode`,
                            startIconName: 'LinkOut',
                            level: 'tertiary',
                            href: `/audit-hub/clients/${clientId}/act-as/${company.accountId}`,
                            target: '_blank' as const,
                        },
                    },
                ]}
            />
        );
    }

    get banner(): React.JSX.Element {
        const { admins } = sharedRolesAdministrationController;

        return (
            <Box>
                <Text as="h2">{t`Admins`}</Text>
                <AvatarStack
                    data-id="auditor-client-admins"
                    avatarData={admins.map((admin) => {
                        const { firstName, lastName, avatarUrl, email } = admin;
                        const fullName = getFullName(firstName, lastName);

                        return {
                            fallbackText: getInitials(fullName),
                            primaryLabel: fullName,
                            secondaryLabel: email,
                            imgSrc: avatarUrl ?? undefined,
                        };
                    })}
                />
            </Box>
        );
    }
}
