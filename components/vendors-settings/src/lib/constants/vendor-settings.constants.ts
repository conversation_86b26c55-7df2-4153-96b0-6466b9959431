import type { VendorsSettingsState } from '../types/vendors-settings-types';

export const VENDORS_CURRENT_SETTINGS_FORM_ID = 'vendors-settings-current';

export const VENDORS_PROSPECTIVE_SETTINGS_FORM_ID =
    'vendors-settings-prospective';

export const VENDORS_CURRENT_SETTINGS_FORM_FIELDS_NAMES = {
    ENABLE_SSO_SUGGESTIONS: 'enableSsoSuggestions',
    DAYS_BEFORE_DEADLINE_TO_REVIEW: 'daysBeforeDeadlineToReview',
    ENABLE_FOLLOW_UP_REMINDERS: 'enableFollowUpReminders',
    DAYS_TO_SEND_REMINDER: 'daysToSendReminder',
} as const satisfies Record<string, keyof VendorsSettingsState>;

export const DEFAULT_REVIEW_PERIOD_DAYS = 15;

export const STUB_VENDORS_SETTINGS_STATE: Readonly<VendorsSettingsState> = {
    enableSsoSuggestions: true,
    daysBeforeDeadlineToReview: 10,
    enableFollowUpReminders: true,
    daysToSendReminder: 50,
};
