import { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedTaskModalState } from './task-modal-state.model';

export class TasksHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'tasks';

    title = t`Tasks`;

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="action-stack"
                stacks={[
                    {
                        actions: [
                            {
                                actionType: 'button',
                                id: 'tasks-action-stack',
                                typeProps: {
                                    label: t`Create task`,
                                    colorScheme: 'primary',
                                    onClick: () => {
                                        sharedTaskModalState
                                            .openModal({})
                                            .catch((error) => {
                                                // Handle error if needed
                                                console.error(
                                                    'Error opening task modal:',
                                                    error,
                                                );
                                            });
                                    },
                                },
                            },
                        ],
                        id: 'task-header-action-stack',
                    },
                ]}
            />
        );
    }
}

export const sharedTasksHeaderModel = new TasksHeaderModel();
