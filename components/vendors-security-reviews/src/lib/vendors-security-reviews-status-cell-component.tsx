import { Metadata } from '@cosmos/components/metadata';
import { getVendorsSecurityReviewsStatusLabel } from './constants/vendors-security-reviews.constant';
import type {
    VendorSecurityReviewStatusType,
    VendorSecurityReviewTableRowCellProps,
} from './types/vendors-security-reviews.type';

export const VendorsSecurityReviewsStatusCellComponent = ({
    row: { original },
}: VendorSecurityReviewTableRowCellProps): React.JSX.Element => {
    const VENDOR_SECURITY_REVIEW_STATUS_METADATA: Readonly<
        Record<VendorSecurityReviewStatusType, React.JSX.Element>
    > = {
        COMPLETED: (
            <Metadata
                label={getVendorsSecurityReviewsStatusLabel('COMPLETED')}
                type="status"
                colorScheme="success"
            />
        ),
        IN_PROGRESS: (
            <Metadata
                label={getVendorsSecurityReviewsStatusLabel('IN_PROGRESS')}
                type="status"
                colorScheme="neutral"
            />
        ),
        NOT_YET_STARTED: (
            <Metadata
                label={getVendorsSecurityReviewsStatusLabel('NOT_YET_STARTED')}
                type="status"
                colorScheme="neutral"
            />
        ),
        NOT_REQUIRED: (
            <Metadata
                label={getVendorsSecurityReviewsStatusLabel('NOT_REQUIRED')}
                type="status"
                colorScheme="neutral"
            />
        ),
    };

    return VENDOR_SECURITY_REVIEW_STATUS_METADATA[original.status];
};
