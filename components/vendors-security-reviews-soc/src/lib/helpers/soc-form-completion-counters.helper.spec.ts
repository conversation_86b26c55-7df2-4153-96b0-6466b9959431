import { describe, expect, test } from 'vitest';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';
import {
    getComplianceScopeCompletionCounter,
    getEndUserControlsCompletionCounter,
    getReportFindingsCompletionCounter,
    getReportOpinionCompletionCounter,
    getReviewerInfoCompletionCounter,
    getSocFormCompletionCounters,
} from './soc-form-completion-counters.helper';

describe('sOC Form Completion Counters', () => {
    describe('getReviewerInfoCompletionCounter', () => {
        test('should return 0 when no reviewer info is provided', () => {
            const result = getReviewerInfoCompletionCounter(
                {} as SocReviewFormValuesType,
            );

            expect(result).toBe(0);
        });

        test('should return 1 when only reviewer is provided', () => {
            const formValues: SocReviewFormValuesType = {
                reviewerInfo: {
                    reviewer: { id: '1', label: '<PERSON>', value: '<PERSON>' },
                    reportIssueDate:
                        '' as `${number}${number}${number}${number}-${number}${number}-${number}${number}`,
                },
            };
            const result = getReviewerInfoCompletionCounter(formValues);

            expect(result).toBe(1);
        });

        test('should return 2 when both reviewer and reportIssueDate are provided', () => {
            const formValues: SocReviewFormValuesType = {
                reviewerInfo: {
                    reviewer: { id: '1', label: 'John Doe', value: 'John Doe' },
                    reportIssueDate:
                        '2024-01-15' as `${number}${number}${number}${number}-${number}${number}-${number}${number}`,
                },
            };
            const result = getReviewerInfoCompletionCounter(formValues);

            expect(result).toBe(2);
        });
    });

    describe('getComplianceScopeCompletionCounter', () => {
        test('should return correct totals for different SOC types', () => {
            const soc1Values: SocReviewFormValuesType = {
                complianceScope: {
                    certification: { id: '1', label: 'SOC_1', value: 'SOC_1' },
                },
            };
            const soc2Values: SocReviewFormValuesType = {
                complianceScope: {
                    certification: { id: '2', label: 'SOC_2', value: 'SOC_2' },
                },
            };
            const soc3Values: SocReviewFormValuesType = {
                complianceScope: {
                    certification: { id: '3', label: 'SOC_3', value: 'SOC_3' },
                },
            };

            expect(getComplianceScopeCompletionCounter(soc1Values).total).toBe(
                3,
            );
            expect(getComplianceScopeCompletionCounter(soc2Values).total).toBe(
                4,
            );
            expect(getComplianceScopeCompletionCounter(soc3Values).total).toBe(
                2,
            );
        });

        test('should count completed fields correctly for SOC 2', () => {
            const formValues: SocReviewFormValuesType = {
                complianceScope: {
                    certification: { id: '2', label: 'SOC_2', value: 'SOC_2' },
                    scopeType: 'type1',
                    auditPeriod:
                        '2024-01-15' as `${number}${number}${number}${number}-${number}${number}-${number}${number}`,
                    trustServiceCriteria: ['SECURITY', 'AVAILABILITY'],
                },
            };
            const result = getComplianceScopeCompletionCounter(formValues);

            expect(result.completed).toBe(4);
            expect(result.total).toBe(4);
        });
    });

    describe('getReportOpinionCompletionCounter', () => {
        test('should return 0 when no report opinion is provided', () => {
            const result = getReportOpinionCompletionCounter(
                {} as SocReviewFormValuesType,
            );

            expect(result).toBe(0);
        });

        test('should return 1 when report opinion is provided', () => {
            const formValues: SocReviewFormValuesType = {
                reportOpinion: {
                    reportOpinion: {
                        id: '1',
                        label: 'UNQUALIFIED',
                        value: 'UNQUALIFIED',
                    },
                },
            };
            const result = getReportOpinionCompletionCounter(formValues);

            expect(result).toBe(1);
        });
    });

    describe('getReportFindingsCompletionCounter', () => {
        test('should return 1/1 when no findings checkbox is checked', () => {
            const formValues: SocReviewFormValuesType = {
                reportFindings: {
                    noFindingsChecked: true,
                },
            };
            const result = getReportFindingsCompletionCounter(formValues);

            expect(result.completed).toBe(1);
            expect(result.total).toBe(1);
        });

        test('should return 2/2 when findings and material impact are provided', () => {
            const formValues: SocReviewFormValuesType = {
                reportFindings: {
                    noFindingsChecked: false,
                    findings: [{ description: 'Some finding' }],
                    hasMaterialImpact: 'YES',
                },
            };
            const result = getReportFindingsCompletionCounter(formValues);

            expect(result.completed).toBe(2);
            expect(result.total).toBe(2);
        });
    });

    describe('getEndUserControlsCompletionCounter', () => {
        test('should return 1 when no controls checkbox is checked', () => {
            const formValues: SocReviewFormValuesType = {
                endUserControls: {
                    noControlsChecked: true,
                },
            };
            const result = getEndUserControlsCompletionCounter(formValues);

            expect(result).toBe(1);
        });

        test('should return 1 when controls are provided', () => {
            const formValues: SocReviewFormValuesType = {
                endUserControls: {
                    noControlsChecked: false,
                    userControls: [{ name: 'Some control', inPlace: 'YES' }],
                },
            };
            const result = getEndUserControlsCompletionCounter(formValues);

            expect(result).toBe(1);
        });
    });

    describe('getSocFormCompletionCounters', () => {
        test('should return all completion counters', () => {
            const formValues: SocReviewFormValuesType = {
                reviewerInfo: {
                    reviewer: { id: '1', label: 'John Doe', value: 'John Doe' },
                    reportIssueDate:
                        '2024-01-15' as `${number}${number}${number}${number}-${number}${number}-${number}${number}`,
                },
                complianceScope: {
                    certification: { id: '1', label: 'SOC_1', value: 'SOC_1' },
                },
                reportOpinion: {
                    reportOpinion: {
                        id: '1',
                        label: 'UNQUALIFIED',
                        value: 'UNQUALIFIED',
                    },
                },
                reportFindings: {
                    noFindingsChecked: true,
                },
                endUserControls: {
                    noControlsChecked: true,
                },
            };

            const result = getSocFormCompletionCounters(formValues);

            expect(result.reviewerInfo.completed).toBe(2);
            expect(result.reviewerInfo.total).toBe(2);
            expect(result.complianceScope.completed).toBe(1);
            expect(result.complianceScope.total).toBe(3);
            expect(result.reportOpinion.completed).toBe(1);
            expect(result.reportOpinion.total).toBe(1);
            expect(result.reportFindings.completed).toBe(1);
            expect(result.reportFindings.total).toBe(1);
            expect(result.endUserControls.completed).toBe(1);
            expect(result.endUserControls.total).toBe(1);
        });
    });
});
