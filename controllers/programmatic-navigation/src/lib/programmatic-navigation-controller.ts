import { makeAutoObservable } from '@globals/mobx';
import type { NavigateFunction } from '@remix-run/react';

/**
 * Navigation controller for use in controllers and other non-React contexts.
 * This allows navigation from MobX controllers, utilities, and other places
 * where React hooks are not available.
 */
class ProgrammaticNavigationController {
    navigateFn: NavigateFunction | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Set the navigate function from useNavigate hook.
     * This should be called once in the app root or layout.
     */
    setNavigateFunction = (fn: NavigateFunction): void => {
        this.navigateFn = fn;
    };

    /**
     * Navigate to a URL. Can be used from controllers and other non-React contexts.
     *
     * @param url - The URL to navigate to.
     * @param options - Navigation options (replace, state, etc.).
     */
    navigateTo = (
        url: string,
        options?: {
            replace?: boolean;
            state?: unknown;
        },
    ): void => {
        if (!this.navigateFn) {
            console.warn(
                'Global navigation not initialized. Make sure to call setNavigateFunction in your app root.',
            );

            return;
        }

        this.navigateFn(url, options);
    };

    /**
     * Navigate back in history.
     *
     * Note: Be aware that this may send you out of your application since the history stack of the browser isn't scoped to just your application.
     *
     * @param delta - Number of steps to go back (default: -1).
     */
    goBack = (delta = -1): void => {
        if (!this.navigateFn) {
            console.warn(
                'Global navigation not initialized. Make sure to call setNavigateFunction in your app root.',
            );

            return;
        }

        this.navigateFn(delta);
    };
}

export const sharedProgrammaticNavigationController =
    new ProgrammaticNavigationController();
