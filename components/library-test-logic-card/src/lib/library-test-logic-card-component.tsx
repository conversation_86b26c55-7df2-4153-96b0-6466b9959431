import { activeLibraryTestController } from '@controllers/library-test';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const LibraryTestLogicCardComponent = observer(
    (): React.JSX.Element | null => {
        const recipe =
            activeLibraryTestController.monitoringControlInstance?.recipe;

        if (!recipe || recipe === '{}') {
            return null;
        }

        return (
            <Card
                title={t`Test Logic`}
                data-testid="LibraryTestLogicCardComponent"
                data-id="isVJ8fGu"
                body={<p></p>}
            />
        );
    },
);
