import { getEvidenceTypeApiValue } from '@controllers/audit-hub';
import type { FilterProps } from '@cosmos/components/datatable';
import type { AuditHubEvidenceTypeEnum } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

/**
 * Priority order for evidence types - most commonly used types first.
 */
const EVIDENCE_TYPE_PRIORITY_ORDER: AuditHubEvidenceTypeEnum[] = [
    'Evidence Library',
    'Policies',
    'Test Evidence',
    'Assets',
    'Personnel',
    'Company Info',
    'Vendors',
    'Miscellaneous Evidence',
];

function getEvidenceTypeLabel(evidenceType: AuditHubEvidenceTypeEnum): string {
    switch (evidenceType) {
        case 'Miscellaneous Evidence': {
            return t`Miscellaneous Evidence`;
        }
        case 'Policies': {
            return t`Policies`;
        }
        case 'Evidence Library': {
            return t`Evidence Library`;
        }
        case 'Assets': {
            return t`Assets`;
        }
        case 'Company Info': {
            return t`Company Info`;
        }
        case 'Personnel': {
            return t`Personnel`;
        }
        case 'Test Evidence': {
            return t`Test Evidence`;
        }
        case 'Vendors': {
            return t`Vendors`;
        }
        default: {
            return evidenceType as string;
        }
    }
}

/**
 * Compare function for sorting evidence types by priority order.
 */
function compareEvidenceTypes(
    a: AuditHubEvidenceTypeEnum,
    b: AuditHubEvidenceTypeEnum,
): number {
    const indexA = EVIDENCE_TYPE_PRIORITY_ORDER.indexOf(a);
    const indexB = EVIDENCE_TYPE_PRIORITY_ORDER.indexOf(b);

    // If both types are in the priority list, sort by their position
    if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
    }

    // If only one type is in the priority list, prioritize it
    if (indexA !== -1) {
        return -1;
    }
    if (indexB !== -1) {
        return 1;
    }

    // If neither type is in the priority list, fall back to alphabetical sorting
    return (a as string).localeCompare(b as string);
}

function createFilterOption(evidenceType: AuditHubEvidenceTypeEnum) {
    const mappedValue = getEvidenceTypeApiValue(evidenceType);

    return {
        id: mappedValue,
        label: getEvidenceTypeLabel(evidenceType),
        value: evidenceType,
    };
}

/**
 * Generates dynamic filter configuration for evidence request details based on available evidence types.
 *
 * @param availableEvidenceTypes - Array of available evidence types from the API response.
 * @returns FilterProps configuration with options for each unique evidence type present in the data.
 */
export function getEvidenceRequestDetailsFilters(
    availableEvidenceTypes: AuditHubEvidenceTypeEnum[] = [],
): FilterProps {
    const filterOptions = [...availableEvidenceTypes]
        .sort(compareEvidenceTypes)
        .map(createFilterOption);

    return {
        clearAllButtonLabel: t`Clear all`,
        filters: [
            {
                filterType: 'checkbox',
                id: 'types',
                label: t`Document type`,
                options: filterOptions,
            },
        ],
        triggerLabel: t`Filters`,
    };
}
