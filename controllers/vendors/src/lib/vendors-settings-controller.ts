import { snackbarController } from '@controllers/snackbar';
import {
    schedulesFellowUpReminderControllerCreateFollowUpRemindersMutation,
    schedulesFellowUpReminderControllerDeleteFollowUpRemindersMutation,
    schedulesFellowUpReminderControllerGetFollowUpRemindersOptions,
    schedulesFellowUpReminderControllerUpdateFollowUpRemindersMutation,
    vendorsControllerGetVendorsSettingsOptions,
    vendorsControllerUpdateVendorEmailContentMutation,
    vendorsControllerUpdateVendorReviewPeriodMutation,
    vendorsControllerUpdateVendorSettingsMutation,
} from '@globals/api-sdk/queries';
import type {
    ScheduleConfigurationResponseDto,
    ScheduleFollowUpReminderRequestDto,
    VendorEmailContentRequestDto,
    VendorReviewPeriodRequestDto,
    VendorSettingsRequestDto,
    VendorsSettingsResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getInitials } from '@helpers/formatters';

/**
 * Simulated mutation options for "no changes" scenario.
 */
const noChangesMutationOptions = () => ({
    mutationFn: () => Promise.resolve({}),
    mutationKey: ['questionnaire-reminder-no-changes'],
});

class VendorsSettingsController {
    constructor() {
        makeAutoObservable(this);
    }

    allVendorsSettingsQuery = new ObservedQuery(
        vendorsControllerGetVendorsSettingsOptions,
    );

    questionnaireRemindersQuery = new ObservedQuery(
        schedulesFellowUpReminderControllerGetFollowUpRemindersOptions,
    );

    updateSsoSettingsMutation = new ObservedMutation(
        vendorsControllerUpdateVendorSettingsMutation,
        {
            onSuccess: () => {
                this.allVendorsSettingsQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'sso-settings-update-success',
                    hasTimeout: true,
                    props: {
                        title: t`SSO settings updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'sso-settings-update-error',
                    props: {
                        title: t`Failed to update SSO settings`,
                        description: t`Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    updateReviewPeriodMutation = new ObservedMutation(
        vendorsControllerUpdateVendorReviewPeriodMutation,
        {
            onSuccess: () => {
                this.allVendorsSettingsQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'review-period-update-success',
                    hasTimeout: true,
                    props: {
                        title: t`Review period updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'review-period-update-error',
                    props: {
                        title: t`Failed to update review period`,
                        description: t`Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    updateEmailContentMutation = new ObservedMutation(
        vendorsControllerUpdateVendorEmailContentMutation,
        {
            onSuccess: () => {
                this.allVendorsSettingsQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'email-content-update-success',
                    hasTimeout: true,
                    props: {
                        title: t`Email content updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'email-content-update-error',
                    props: {
                        title: t`Failed to update email content`,
                        description: t`Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    createQuestionnaireReminderMutation = new ObservedMutation(
        schedulesFellowUpReminderControllerCreateFollowUpRemindersMutation,
        {
            onSuccess: () => {
                this.questionnaireRemindersQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'questionnaire-reminder-create-success',
                    hasTimeout: true,
                    props: {
                        title: t`Questionnaire reminder saved successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-reminder-create-error',
                    props: {
                        title: t`Failed to save questionnaire reminder`,
                        description: t`Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    updateQuestionnaireReminderMutation = new ObservedMutation(
        schedulesFellowUpReminderControllerUpdateFollowUpRemindersMutation,
        {
            onSuccess: () => {
                this.questionnaireRemindersQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'questionnaire-reminder-update-success',
                    hasTimeout: true,
                    props: {
                        title: t`Questionnaire reminder updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-reminder-update-error',
                    props: {
                        title: t`Failed to update questionnaire reminder`,
                        description: t`Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    deleteQuestionnaireReminderMutation = new ObservedMutation(
        schedulesFellowUpReminderControllerDeleteFollowUpRemindersMutation,
        {
            onSuccess: () => {
                this.questionnaireRemindersQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'questionnaire-reminder-delete-success',
                    hasTimeout: true,
                    props: {
                        title: t`Questionnaire reminder disabled successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-reminder-delete-error',
                    props: {
                        title: t`Failed to disable questionnaire reminder`,
                        description: t`Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    /**
     * Simulated mutation for "no changes" scenario to trigger ViewEditCardComponent exit from edit mode.
     */
    noChangesMutation = new ObservedMutation(noChangesMutationOptions, {
        onSuccess: () => {
            // No additional action needed, just to trigger the mutation completion
        },
    });

    get vendorSettings(): VendorsSettingsResponseDto | null {
        return this.allVendorsSettingsQuery.data;
    }

    get isLoading(): boolean {
        return this.allVendorsSettingsQuery.isLoading;
    }

    get isUpdatingSsoSettings(): boolean {
        return this.updateSsoSettingsMutation.isPending;
    }

    get isUpdatingReviewPeriod(): boolean {
        return this.updateReviewPeriodMutation.isPending;
    }

    get isUpdatingEmailContent(): boolean {
        return this.updateEmailContentMutation.isPending;
    }

    get hasErrorEmailContentUpdate(): boolean {
        return this.updateEmailContentMutation.hasError;
    }

    get hasErrorSsoSettingsUpdate(): boolean {
        return this.updateSsoSettingsMutation.hasError;
    }

    get hasErrorReviewPeriodUpdate(): boolean {
        return this.updateReviewPeriodMutation.hasError;
    }

    get questionnaireReminders(): ScheduleConfigurationResponseDto | null {
        return this.questionnaireRemindersQuery.data;
    }

    get isLoadingQuestionnaireReminders(): boolean {
        return this.questionnaireRemindersQuery.isLoading;
    }

    get isUpdatingQuestionnaireReminders(): boolean {
        return (
            this.createQuestionnaireReminderMutation.isPending ||
            this.updateQuestionnaireReminderMutation.isPending ||
            this.deleteQuestionnaireReminderMutation.isPending ||
            this.noChangesMutation.isPending
        );
    }

    get hasErrorQuestionnaireRemindersUpdate(): boolean {
        return (
            this.createQuestionnaireReminderMutation.hasError ||
            this.updateQuestionnaireReminderMutation.hasError ||
            this.deleteQuestionnaireReminderMutation.hasError ||
            this.noChangesMutation.hasError
        );
    }

    get questionnaireReminderDays(): number {
        if (!this.questionnaireReminders?.criteria) {
            return 0;
        }

        try {
            const criteria = JSON.parse(
                this.questionnaireReminders.criteria,
            ) as {
                followUpReminderDays?: number;
            };

            return criteria.followUpReminderDays || 0;
        } catch {
            return 0;
        }
    }

    get isQuestionnaireReminderEnabled(): boolean {
        return this.questionnaireReminderDays > 0;
    }

    get companyDetails(): {
        logoUrl?: string;
        name: string;
        fallbackText: string;
    } {
        const { company } = sharedCurrentCompanyController;
        const { primaryWorkspace, workspaces } = sharedWorkspacesController;

        const hasMultipleWorkspaces = workspaces.length > 1;

        if (hasMultipleWorkspaces && primaryWorkspace) {
            return {
                logoUrl: primaryWorkspace.logo || undefined,
                name: primaryWorkspace.name,
                fallbackText: getInitials(primaryWorkspace.name),
            };
        }

        const companyName = company?.name || 'Company';

        return {
            logoUrl: company?.logoUrl || undefined,
            name: companyName,
            fallbackText: getInitials(companyName),
        };
    }

    loadVendorsSettings = () => {
        this.allVendorsSettingsQuery.load();
    };

    loadQuestionnaireReminders = () => {
        this.questionnaireRemindersQuery.load();
    };

    updateSsoSettings = (data: VendorSettingsRequestDto) => {
        this.updateSsoSettingsMutation.mutate({ body: data });
    };

    updateReviewPeriod = (data: VendorReviewPeriodRequestDto) => {
        this.updateReviewPeriodMutation.mutate({ body: data });
    };

    updateEmailContent = (data: VendorEmailContentRequestDto) => {
        this.updateEmailContentMutation.mutate({ body: data });
    };

    updateQuestionnaireReminders = (data: {
        enableFollowUpReminders: boolean;
        daysToSendReminder?: number;
    }): void => {
        const { enableFollowUpReminders, daysToSendReminder } = data;

        // Handle case where reminders are already disabled
        if (!enableFollowUpReminders && !this.questionnaireReminders?.id) {
            // Trigger the simulated mutation to make ViewEditCardComponent exit edit mode
            this.noChangesMutation.mutate();

            snackbarController.addSnackbar({
                id: 'questionnaire-reminder-no-changes',
                props: {
                    title: t`No changes made`,
                    severity: 'primary',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        // Handle disabling reminders
        if (
            !enableFollowUpReminders &&
            Boolean(this.questionnaireReminders?.id)
        ) {
            if (!this.questionnaireReminders?.id) {
                return;
            }

            this.deleteQuestionnaireReminderMutation.mutate({
                path: { id: this.questionnaireReminders.id },
            });

            return;
        }

        // Handle enabling reminders (create or update)
        if (enableFollowUpReminders && Boolean(daysToSendReminder)) {
            const requestData: ScheduleFollowUpReminderRequestDto = {
                followUpReminderDays: Number(daysToSendReminder),
            };

            if (this.questionnaireReminders?.id) {
                // Update existing reminder
                this.updateQuestionnaireReminderMutation.mutate({
                    path: { id: this.questionnaireReminders.id },
                    body: requestData,
                });
            } else {
                // Create new reminder
                this.createQuestionnaireReminderMutation.mutate({
                    body: requestData,
                });
            }

            return;
        }

        // Invalid state - show error
        snackbarController.addSnackbar({
            id: 'questionnaire-reminder-validation-error',
            props: {
                title: t`Invalid reminder configuration`,
                description: t`Days to send reminder is required when enabling reminders`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };
}

export const sharedVendorsSettingsController = new VendorsSettingsController();
