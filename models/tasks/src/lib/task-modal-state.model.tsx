import { modalController } from '@controllers/modal';
import type { TaskGetParams } from '@controllers/tasks';
import { makeAutoObservable } from '@globals/mobx';
import {
    TaskModalForm,
    TaskModalState,
    type TaskModalStateKeys,
    TASKS_MODAL_ID,
} from '@views/tasks';
import { sharedTaskForm } from './task-form.model';

export class TaskModalStateModel {
    currentKey: TaskModalStateKeys | null = null;
    isLoading = false;

    constructor() {
        makeAutoObservable(this);
    }

    setCurrent(key: TaskModalStateKeys | null): void {
        this.currentKey = key;
    }

    get current(): TaskModalStateKeys | null {
        return this.currentKey;
    }

    openModal(params: TaskGetParams): Promise<void> {
        this.setCurrent(null);
        this.isLoading = true;

        // loads the task data before opening the modal
        return sharedTaskForm.init(params).then(() => {
            modalController.openModal({
                id: TASKS_MODAL_ID,
                content: () => (
                    <TaskModalState
                        data-id="4g4BssdA"
                        defaultContent={<TaskModalForm />}
                    />
                ),
                size: 'lg',
                centered: true,
                disableClickOutsideToClose: true,
            });
            this.isLoading = false;
        });
    }

    closeModal(): void {
        modalController.closeModal(TASKS_MODAL_ID);
    }
}

export const sharedTaskModalState = new TaskModalStateModel();
