import { isEmpty } from 'lodash-es';
import type { VendorReviewResponseDto } from '@globals/api-sdk/types';

/**
 * Determines the initial value for the "all controls in place" radio button
 * based on the vendor review user controls data.
 *
 * @param vendorReview - The vendor review data containing user controls.
 * @returns 'YES_TO_ALL' if all controls are in place, 'NO_TO_ALL' if none are in place, undefined otherwise.
 */
export const getInitialAllControlsInPlace = (
    vendorReview: VendorReviewResponseDto | null | undefined,
): string | undefined => {
    if (!vendorReview?.userControls || isEmpty(vendorReview.userControls)) {
        return undefined;
    }

    const allYes = vendorReview.userControls.every(
        (control) => control.inPlace,
    );
    const allNo = vendorReview.userControls.every(
        (control) => !control.inPlace,
    );

    if (allYes) {
        return 'YES_TO_ALL';
    }
    if (allNo) {
        return 'NO_TO_ALL';
    }

    return undefined;
};

/**
 * Determines the initial scope type value based on the vendor review SOC report types.
 *
 * @param vendorReview - The vendor review data containing SOC report type flags.
 * @returns 'type1' if socReportType1 is true, 'type2' if socReportType2 is true, undefined otherwise.
 */
export const getInitialScopeType = (
    vendorReview: VendorReviewResponseDto | null | undefined,
): string | undefined => {
    if (vendorReview?.socReportType1) {
        return 'type1';
    }
    if (vendorReview?.socReportType2) {
        return 'type2';
    }

    return undefined;
};
