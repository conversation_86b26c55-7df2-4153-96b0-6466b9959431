import type { ButtonProps } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedTaskForm, sharedTaskModalState } from '@models/tasks';
import { TASK_MODAL_STATE, TASKS_FORM_ID } from '../constants/tasks.constants';
import { TaskModalFormDetails } from './task-modal-form-details';
import { TaskModalFormMapping } from './task-modal-form-mapping';
import { TaskModalFormSchedule } from './task-modal-form-schedule';

export const TaskModalForm = observer((): React.JSX.Element => {
    const { modalFormTitle, submit, isEdit, isDirty } = sharedTaskForm;

    const handleClose = () => {
        if (isDirty) {
            sharedTaskModalState.setCurrent(TASK_MODAL_STATE.CANCEL);

            return;
        }
        sharedTaskModalState.closeModal();
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        await submit();
    };

    const leftActionStack: ButtonProps[] = [];

    if (isEdit) {
        leftActionStack.push({
            label: t`Delete task`,
            level: 'tertiary',
            colorScheme: 'danger',
            onClick: () => {
                sharedTaskModalState.setCurrent(TASK_MODAL_STATE.DELETE);
            },
        });
    }

    return (
        <>
            <Modal.Header
                title={modalFormTitle}
                closeButtonAriaLabel={t`Close modal`}
                onClose={handleClose}
            />
            <Modal.Body>
                <form id={TASKS_FORM_ID} onSubmit={handleSubmit}>
                    <Stack direction="column" gap="4x">
                        <TaskModalFormDetails />
                        <TaskModalFormSchedule />
                        <TaskModalFormMapping />
                    </Stack>
                </form>
            </Modal.Body>
            <Modal.Footer
                leftActionStack={leftActionStack}
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: handleClose,
                    },
                    {
                        label: t`Save`,
                        form: TASKS_FORM_ID,
                        level: 'primary',
                        colorScheme: 'primary',
                        type: 'submit',
                    },
                ]}
            />
        </>
    );
});
