import { isEmpty } from 'lodash-es';
import { useState } from 'react';
import { modalController } from '@controllers/modal';
import {
    sharedVendorsCurrentController,
    sharedVendorsDetailsController,
    sharedVendorsProspectiveController,
    sharedVendorsSchedulesQuestionnairesController,
} from '@controllers/vendors';
import type {
    VendorRequestDto,
    VendorResponseDto,
} from '@globals/api-sdk/types';
import { openMarkVendorAsActiveSuccessModal } from '../helpers/open-mark-vendor-as-active-success-modal';
import type { QuestionnaireOption, VendorFormData } from './types';

export const useVendorForm = (): {
    formData: VendorFormData;
    reminderToggle: boolean;
    scheduleToggle: boolean;
    selectedQuestionnaire: QuestionnaireOption | undefined;
    setReminderToggle: (value: boolean) => void;
    setScheduleToggle: (value: boolean) => void;
    handleRecurringReviewsChange: (
        renewalScheduleType: NonNullable<
            VendorResponseDto['renewalScheduleType']
        >,
    ) => void;
    handleQuestionnaireChange: (questionnaire: QuestionnaireOption) => void;
    handleContactEmailChange: (email: string) => void;
    handleRiskChange: (risk: string) => void;
    handleSubmit: () => void;
    isUpdating: boolean;
} => {
    const { data: vendor } = sharedVendorsDetailsController.vendorDetailsQuery;
    const [formData, setFormData] = useState<VendorFormData>({
        risk: 'NONE',
        renewalScheduleType: 'ONE_YEAR',
        questionnaireIds: [],
        contactsEmail: vendor?.contactsEmail ?? null,
    });

    const [reminderToggle, setReminderToggle] = useState(false);
    const [scheduleToggle, setScheduleToggle] = useState(false);
    const [selectedQuestionnaire, setSelectedQuestionnaire] =
        useState<QuestionnaireOption>();

    const handleRecurringReviewsChange = (
        renewalScheduleType: NonNullable<
            VendorResponseDto['renewalScheduleType']
        >,
    ) => {
        setFormData((prev) => ({
            ...prev,
            renewalScheduleType,
        }));
    };

    const handleQuestionnaireChange = (questionnaire: QuestionnaireOption) => {
        setSelectedQuestionnaire(questionnaire);
        setFormData((prev) => ({
            ...prev,
            questionnaireIds: [Number(questionnaire.id)],
        }));
    };

    const handleContactEmailChange = (email: string) => {
        setFormData((prev) => ({
            ...prev,
            contactsEmail: email,
        }));
    };

    const handleRiskChange = (risk: string) => {
        setFormData((prev) => ({
            ...prev,
            risk: risk as VendorRequestDto['risk'],
        }));
    };

    const handleSubmit = () => {
        if (!vendor) {
            return;
        }

        try {
            sharedVendorsProspectiveController.updateVendorDetails(vendor.id, {
                ...vendor,
                status: 'ACTIVE',
                risk: formData.risk,
                renewalScheduleType: formData.renewalScheduleType,
                integrations: vendor.integrations?.map(
                    (integration) => integration.id,
                ),
                passwordPolicy:
                    (vendor.passwordPolicy as string | null) ?? undefined,
                type: (vendor.type as string | null) ?? undefined,
            } as unknown as VendorRequestDto);

            if (scheduleToggle && !isEmpty(formData.questionnaireIds)) {
                sharedVendorsSchedulesQuestionnairesController.createSchedulesVendorQuestionnaires(
                    vendor.id,
                    formData.questionnaireIds,
                    formData.contactsEmail ?? '',
                );
            }

            sharedVendorsDetailsController.vendorDetailsQuery.invalidate();
            sharedVendorsProspectiveController.allVendorsProspectiveQuery.invalidate();
            sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();

            modalController.closeModal('mark-vendor-as-active-modal');

            // Show success modal
            openMarkVendorAsActiveSuccessModal();
        } catch (error) {
            console.error('Failed to mark vendor as active:', error);
        }
    };

    return {
        formData,
        reminderToggle,
        scheduleToggle,
        selectedQuestionnaire,
        setReminderToggle,
        setScheduleToggle,
        handleRecurringReviewsChange,
        handleQuestionnaireChange,
        handleContactEmailChange,
        handleRiskChange,
        handleSubmit,
        isUpdating: sharedVendorsProspectiveController.isUpdating,
    };
};
