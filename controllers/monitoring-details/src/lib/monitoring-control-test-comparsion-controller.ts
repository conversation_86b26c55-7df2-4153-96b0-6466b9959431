import { grcControllerGetControlTestComparisonOptions } from '@globals/api-sdk/queries';
import type { ShortTestResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class MonitoringControlTestComparisonController {
    constructor() {
        makeAutoObservable(this);
    }

    monitoringControlTestComparsionQuery = new ObservedQuery(
        grcControllerGetControlTestComparisonOptions,
    );

    invalidate = (): void => {
        this.monitoringControlTestComparsionQuery.invalidate();
    };

    get userMappedTests(): ShortTestResponseDto[] {
        return (
            this.monitoringControlTestComparsionQuery.data?.userMappedTests ??
            []
        );
    }

    get alignedTests(): ShortTestResponseDto[] {
        return (
            this.monitoringControlTestComparsionQuery.data?.alignedTests ?? []
        );
    }

    get templateMappedTests(): ShortTestResponseDto[] {
        return (
            this.monitoringControlTestComparsionQuery.data
                ?.templateMappedTests ?? []
        );
    }

    get isLoading(): boolean {
        return this.monitoringControlTestComparsionQuery.isLoading;
    }

    load = (controlId: number) => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                this.monitoringControlTestComparsionQuery.load({
                    path: {
                        controlId,
                        xWorkspaceId: sharedWorkspacesController
                            .currentWorkspace?.id as number,
                    },
                });
            },
        );
    };
}

export const sharedMonitoringControlTestComparisonController =
    new MonitoringControlTestComparisonController();
