import type { AssetResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { sharedAssetFormModel } from '@models/assets';
import { Form, type FormValues } from '@ui/forms';

interface AssetsDetailsNotesFormComponentsProps {
    formRef: React.RefObject<HTMLFormElement>;
    state: AssetResponseDto | null;
    onSubmit: (values: FormValues) => void;
}

export const AssetsDetailsNotesFormComponent = observer(
    ({
        formRef,
        state,
        onSubmit,
    }: AssetsDetailsNotesFormComponentsProps): React.JSX.Element => {
        const schema = {
            notes: sharedAssetFormModel.notesFieldConfig(state?.notes || ''),
        };

        return (
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="assets-notes-form"
                data-id="assets-notes-form"
                schema={schema}
                data-testid="AssetsDetailsNotesFormComponent"
                onSubmit={onSubmit}
            />
        );
    },
);
