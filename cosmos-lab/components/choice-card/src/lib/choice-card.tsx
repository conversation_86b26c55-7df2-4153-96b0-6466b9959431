import { isNil } from 'lodash-es';
import type { ReactNode } from 'react';
import { Box } from '@cosmos/components/box';
import { Checkbox, type CheckboxProps } from '@cosmos/components/checkbox';
import { Radio, type RadioProps } from '@cosmos/components/radio-field';
import { createElementIds } from './helpers/create-element-ids.helper';
import { StyledFieldLabel, StyledStack } from './styles';
import type { ChoiceCardInputType } from './types/choice-card-input.type';

export interface BaseChoiceCardProps {
    /**
     * Optional slot to render next to the input.
     */
    slot?: ReactNode;
    /**
     * The controlled checked state of the checkbox. Must be used in conjunction with `onChange`.
     */
    checked?: boolean;
    /**
     * Check if it is a checkbox input or a radio button input.
     */
    choiceCardInputType: ChoiceCardInputType;
    /**
     * The controlled checked state of the checkbox at rendering. Must be used in conjunction with `checked`.
     */
    defaultChecked?: boolean;
    /**
     * Label for the box.
     */
    label: string;
    /**
     * Optional help text for the box.
     */
    helpText?: string;
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Whether the choice card is disabled.
     */
    disabled?: boolean;
    /**
     * A unique identifier for the form this field is a part of.
     */
    formId: string;
    /**
     * Name of the form.
     */
    name: string;
    /**
     * Whether the choice card is required or not.
     */
    required?: boolean;
    /**
     * Function called when value changes.
     */
    readOnly?: boolean;
    /**
     * The value given as data.
     */
    value: string;
}

export interface CheckBoxChoiceCardProps extends BaseChoiceCardProps {
    /**
     * Check if it is a checkbox input or a radio button input.
     */
    choiceCardInputType: 'checkbox';
    /**
     * Function called when value changes.
     */
    onChange: CheckboxProps['onChange'];
}

export interface RadioChoiceCardProps extends BaseChoiceCardProps {
    /**
     * Check if it is a checkbox input or a radio button input.
     */
    choiceCardInputType: 'radio';
    /**
     * Function called when value changes.
     */
    onChange: RadioProps['onChange'];
}

export type ChoiceCardProps = CheckBoxChoiceCardProps | RadioChoiceCardProps;

/**
 * A selectable card component that displays either a checkbox or radio button with accompanying content, allowing users to make single or multiple selections in a visually prominent format.
 *
 * [ChoiceCard in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=46194-66023&t=drDWqK0vcTZRzyw0-4).
 */
export const ChoiceCard = ({
    checked = false,
    defaultChecked,
    'data-id': dataId = 'choice-card',
    disabled,
    formId,
    label,
    helpText,
    name,
    onChange,
    required,
    readOnly = false,
    value,
    slot = undefined,
    choiceCardInputType,
}: ChoiceCardProps): React.JSX.Element => {
    const { helpTextId, inputId, labelId } = createElementIds({
        formId,
        name,
        hasHelpText: Boolean(helpText),
    });

    return (
        <StyledStack
            $isSelected={checked}
            align="center"
            data-testid="ChoiceCard"
            data-id={dataId}
            gap="3x"
            minWidth="48x"
            p="3x"
        >
            {choiceCardInputType === 'checkbox' ? (
                <Checkbox
                    aria-describedby={helpTextId}
                    aria-labelledby={labelId}
                    checked={checked}
                    defaultChecked={defaultChecked}
                    data-id={`${dataId}-checkbox`}
                    disabled={disabled}
                    id={`${formId}-${name}-checkbox`}
                    name={name}
                    readOnly={readOnly}
                    required={required}
                    value={value}
                    onChange={onChange}
                />
            ) : (
                <Radio
                    aria-describedby={helpTextId}
                    aria-labelledby={labelId}
                    checked={checked}
                    data-id={`${dataId}-radio`}
                    disabled={disabled}
                    id={`${formId}-${name}-radio`}
                    name={name}
                    readOnly={readOnly}
                    required={required}
                    value={value}
                    onChange={onChange}
                />
            )}
            {!isNil(slot) && <Box flexShrink="0">{slot}</Box>}
            {!helpText && (
                <StyledFieldLabel
                    data-id={`${dataId}-label`}
                    htmlFor={inputId}
                    label={label}
                    labelId={labelId}
                    size="md"
                    type="regular"
                />
            )}
            {helpText && helpTextId && (
                <StyledFieldLabel
                    data-id={`${dataId}-label`}
                    helpText={helpText}
                    helpTextId={helpTextId}
                    htmlFor={inputId}
                    label={label}
                    labelId={labelId}
                    size="md"
                    type="regular"
                />
            )}
        </StyledStack>
    );
};
