import type React from 'react';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const VendorsSettingsQuestionnaireRemindersReadOnlyComponent = observer(
    (): React.JSX.Element => {
        const { isQuestionnaireReminderEnabled, questionnaireReminderDays } =
            sharedVendorsSettingsController;

        return (
            <Stack
                direction="column"
                gap="lg"
                data-testid="VendorsSettingsQuestionnaireRemindersReadOnlyComponent"
                data-id="questionnaire-reminders-read-only"
            >
                <KeyValuePair
                    label={t`Send follow-up reminders`}
                    type="TEXT"
                    data-id="follow-up-reminders-status"
                    value={
                        isQuestionnaireReminderEnabled
                            ? t`Enabled`
                            : t`Disabled`
                    }
                />
                {isQuestionnaireReminderEnabled && (
                    <KeyValuePair
                        label={t`Reminder frequency`}
                        value={t`Every ${questionnaireReminderDays} days after the initial request`}
                        type="TEXT"
                        data-id="reminder-frequency"
                    />
                )}
            </Stack>
        );
    },
);
