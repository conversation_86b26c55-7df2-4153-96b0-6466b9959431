import type { TaskGetParams } from '@controllers/tasks';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { RecurringScheduleSpec, Weekday } from '@drata/recurring-schedule';
import type { CustomTaskBaseRequestDto } from '@globals/api-sdk/types';
import type {
    getTaskCardinalOptions,
    getTaskEndsOptions,
    getTaskFrequencyOptions,
    getTaskRepeatOnOptions,
    getTasksWeekdaysOptions,
} from '@models/tasks';

export const RecurringDetailsChoice = {
    TASK_ONLY: 'TASK_ONLY',
    TASK_AND_FUTURE: 'TASK_AND_FUTURE',
    SCHEDULE_OFF: 'SCHEDULE_OFF',
} as const;

export const MonthlyRepeatOnOption = {
    DAY_OF_MONTH: 'dayOfMonth',
    DAY_OF_WEEK: 'dayOfWeek',
} as const;

export interface RecurringScheduleSpecValues {
    scheduleInterval?: number;
    scheduleType?: ReturnType<typeof getTaskFrequencyOptions>[number];
    scheduleStart?: string;
    scheduleDaysOfWeek?: Weekday[];
    scheduleMonthlyType?: ReturnType<
        typeof getTaskRepeatOnOptions
    >[number]['value'];
    scheduleDayOfMonth?: {
        id: number | string;
        label: string;
    } | null;
    scheduleWeekNumber?: ReturnType<typeof getTaskCardinalOptions>[number];
    scheduleWeekday?: ReturnType<typeof getTasksWeekdaysOptions>[number];
    scheduleDay?: number;
    scheduleMonth?: number;
    scheduleEnd?: string;
    scheduleEndsOn?: ReturnType<typeof getTaskEndsOptions>[number]['value'];
    schedule?: RecurringScheduleSpec;
}

export interface TaskControlData extends ListBoxItemData {
    id: string;
    value: string;
    title: string;
    code: string;
    isReady?: boolean;
    label: string;
    description: string;
    taskId?: number;
    configurationId?: number;
}

export interface TaskRiskData extends ListBoxItemData {
    id: string;
    title: string;
    riskId: string;
    label: string;
    description: string;
    taskId?: number;

    owners?: {
        firstName: string;
        lastName: string;
        avatarUrl?: string;
    }[];
    configurationId?: number;
}

export interface TaskAssignee extends ListBoxItemData {
    id: string;
    firstName: string;
    lastName: string;
    label: string;
    avatarUrl: string;
    avatar: {
        imgSrc: string | null;
        imgAlt: string;
        fallbackText: string;
    };
}

export interface TaskDataValues {
    title?: string;
    description?: string;
    dueDate?: string;
    isRecurringTask?: boolean;
    scheduleDate?: string | null;
    completedAt?: string | null;
    entityId?: number | null;
    entityType?: CustomTaskBaseRequestDto['taskType'];
    assignee?: ListBoxItemData | null;
    control?: TaskControlData | null;
    risk?: TaskRiskData | null;
    taskType?: CustomTaskBaseRequestDto['taskType'];
    recurringDetailsChoice?: keyof typeof RecurringDetailsChoice;
}

export type TaskFormValues = TaskGetParams &
    TaskDataValues &
    RecurringScheduleSpecValues;
