import { useCallback } from 'react';
import type { VendorReviewPeriodRequestDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import { sharedVendorsSettingsRecurringReviewsFormModel } from '../models/vendors-settings-recurring-reviews-form.model';

interface FormRefHandle extends HTMLFormElement {
    submitForm: () => void;
    clearForm: () => void;
}

interface Props {
    onSubmit: (data: VendorReviewPeriodRequestDto) => void;
    isUpdating: boolean;
    formRef: React.RefObject<FormRefHandle>;
}

export const VendorsSettingsRecurringReviewsEditComponent = observer(
    ({ onSubmit, isUpdating, formRef }: Props): React.JSX.Element => {
        const handleSubmit = useCallback(
            (values: FormValues) => {
                onSubmit({
                    defaultReviewPeriod: parseInt(
                        String(values.defaultReviewPeriod),
                        10,
                    ),
                });
            },
            [onSubmit],
        );

        const formSchema =
            sharedVendorsSettingsRecurringReviewsFormModel.getSchema();

        return (
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="vendor-recurring-reviews-form"
                schema={formSchema}
                isReadOnly={isUpdating}
                data-testid="VendorsSettingsRecurringReviewsEditComponent"
                data-id="recurring-reviews-edit-form"
                onSubmit={handleSubmit}
            />
        );
    },
);
