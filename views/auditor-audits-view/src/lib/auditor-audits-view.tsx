import { AuditGalleryCard } from '@components/audits';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { Datatable } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AUDITOR_AUDITS_VIEW } from './constants/auditor-audits-view.constants';

export const AuditorAuditsView = observer((): React.JSX.Element => {
    const {
        loadAssignedAuditsPage,
        assignedAuditsIsLoading,
        assignedAudits,
        totalAssignedAudits,
    } = sharedAuditHubController;

    return (
        <Stack direction="column" gap="2xl" data-id="64z07JyN">
            <Datatable
                viewMode="gallery"
                tableId="datatable-audits"
                data-testid="datatable-audits"
                data={assignedAudits}
                columns={[]}
                total={totalAssignedAudits}
                isLoading={assignedAuditsIsLoading}
                defaultPaginationOptions={
                    AUDITOR_AUDITS_VIEW.defaultPaginationOptions
                }
                galleryCard={({ row }) => (
                    <AuditGalleryCard row={row} data-id="Ai9b9jm4" />
                )}
                tableSearchProps={{
                    hideSearch: true,
                }}
                filterProps={{
                    clearAllButtonLabel: t`Reset`,
                    filters: [
                        {
                            filterType: 'radio',
                            id: 'status',
                            label: t`Status`,
                            options: [
                                {
                                    label: t`Active`,
                                    value: 'ACTIVE_AUDITS',
                                },
                                {
                                    label: t`Complete`,
                                    value: 'COMPLETED',
                                },
                            ],
                        },
                    ],
                    triggerLabel: t`Filter`,
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'unpinned',
                        togglePinnedLabel: 'Pin filters to page',
                        toggleUnpinnedLabel: 'Move filters to dropdown',
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadAssignedAuditsPage}
            />
        </Stack>
    );
});
