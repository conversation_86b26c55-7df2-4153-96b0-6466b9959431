import type { ClientLoader } from '@app/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { AuditorSettingsPageHeaderModel } from '@models/auditor-settings-page-header';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: t`Audit Hub - Settings` }];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    return {
        pageHeader: new AuditorSettingsPageHeaderModel(),
        tabs: [
            {
                id: 'profile',
                label: t`Profile`,
                topicPath: 'settings/profile',
            },
            {
                id: 'api-keys',
                label: t`API Keys`,
                topicPath: 'settings/api-keys',
            },
        ],
        utilities: {
            utilitiesList: [],
        },
        layout: {
            centered: true,
        },
    };
});

const AuditHubSettings = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditHubSettings"
            data-id="AuditHubSettingsContent"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AuditHubSettings;
