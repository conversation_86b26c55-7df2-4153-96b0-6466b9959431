import { z } from 'zod';
import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { AuditorFrameworkTypeNames } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { CONTROL_EVIDENCE_INPUT_NAMES } from './constants/control-evidence.constants';
import { OS } from './constants/wizard-samples.constants';

export class AuditHubWizardSampleModel {
    constructor() {
        makeAutoObservable(this);
    }

    get sampleFormSchema(): FormSchema {
        const { auditorPersonnelData } = sharedAuditorController;
        const { auditByIdData } = sharedAuditHubController;
        const { sampleWizardData } = sharedAuditHubAuditController;

        const isSOC2Type1 =
            auditByIdData?.framework.type ===
            AuditorFrameworkTypeNames.SOC_2_TYPE_1;

        const getMultiPersonnelEmptyPlaceholder = (
            personnelDataType: string,
        ) => {
            const personnel =
                auditorPersonnelData?.[
                    personnelDataType as keyof typeof auditorPersonnelData
                ];

            return personnel?.length
                ? { message: t`Select`, enabled: true }
                : {
                      message: t`No relevant personnel for the sample period`,
                      enabled: false,
                  };
        };

        const formSchema = {
            platform: {
                type: 'select',
                label: t`Operating System`,
                options: OS,
                placeholder: t`Select`,
                required: true,
                loaderLabel: t`Loading`,
                initialValue: sampleWizardData.platform,
                validator: z
                    .object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    })
                    .required(),
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.DATE_RANGE]: {
                type: 'date',
                label: t`Dates`,
                isMulti: !isSOC2Type1,
                required: true,
                locale: 'en-US',
                loaderLabel: t`Loading`,
                initialValue: sampleWizardData.date,
                getIsDateUnavailable: (date: string) => {
                    const { startDate, endDate } =
                        auditByIdData?.framework ?? {};

                    if (!startDate || !endDate) {
                        return false;
                    }
                    const selectedDate = new Date(date);
                    const startFormattedDate = new Date(startDate);
                    const endFormattedDate = new Date(endDate);

                    if (isSOC2Type1) {
                        return (
                            selectedDate < startFormattedDate ||
                            selectedDate >= endFormattedDate
                        );
                    }

                    return (
                        selectedDate < startFormattedDate ||
                        selectedDate > endFormattedDate
                    );
                },
                validator: isSOC2Type1
                    ? z.string().date()
                    : z.array(z.string().date()).min(2, {
                          message: t`At least two dates are required for this audit type`,
                      }),
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.HIRED_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel hired during audit period`,
                initialValue: sampleWizardData.hiredPersonnel,
                loaderLabel: t`Loading`,
                isMultiSelect: true,
                isOptional: true,
                removeAllSelectedItemsLabel: t`Remove all`,

                disabled: !getMultiPersonnelEmptyPlaceholder(
                    CONTROL_EVIDENCE_INPUT_NAMES.HIRED_PERSONNEL,
                ).enabled,
                placeholder: getMultiPersonnelEmptyPlaceholder(
                    CONTROL_EVIDENCE_INPUT_NAMES.HIRED_PERSONNEL,
                ).message,
                options:
                    auditorPersonnelData?.hiredPersonnel.map((p) => ({
                        id: String(p.id),
                        label: `${p.firstName} ${p.lastName}`,
                        value: String(p.id),
                        avatar: {
                            imgSrc: p.avatarUrl ?? undefined,
                        },
                    })) ?? [],
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.FORMER_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel terminated during audit period`,
                loaderLabel: t`Loading`,
                initialValue: sampleWizardData.formerPersonnel,
                isMultiSelect: true,
                isOptional: true,
                removeAllSelectedItemsLabel: t`Remove all`,

                disabled: !getMultiPersonnelEmptyPlaceholder(
                    CONTROL_EVIDENCE_INPUT_NAMES.FORMER_PERSONNEL,
                ).enabled,
                placeholder: getMultiPersonnelEmptyPlaceholder(
                    CONTROL_EVIDENCE_INPUT_NAMES.FORMER_PERSONNEL,
                ).message,
                options:
                    auditorPersonnelData?.formerPersonnel.map((p) => ({
                        id: String(p.id),
                        label: `${p.firstName} ${p.lastName}`,
                        value: String(p.id),
                        avatar: {
                            imgSrc: p.avatarUrl ?? undefined,
                        },
                    })) ?? [],
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.CURRENT_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel employed throughout audit period`,
                loaderLabel: t`Loading`,
                isMultiSelect: true,
                initialValue: sampleWizardData.currentPersonnel,
                removeAllSelectedItemsLabel: t`Remove all`,
                isOptional: true,
                disabled: !getMultiPersonnelEmptyPlaceholder(
                    CONTROL_EVIDENCE_INPUT_NAMES.CURRENT_PERSONNEL,
                ).enabled,
                placeholder: getMultiPersonnelEmptyPlaceholder(
                    CONTROL_EVIDENCE_INPUT_NAMES.CURRENT_PERSONNEL,
                ).message,
                options:
                    auditorPersonnelData?.currentPersonnel.map((p) => ({
                        id: String(p.id),
                        label: `${p.firstName} ${p.lastName}`,
                        value: String(p.id),
                        avatar: {
                            imgSrc: p.avatarUrl ?? undefined,
                        },
                    })) ?? [],
            },
        };

        return formSchema as FormSchema;
    }
}
