import { isEmpty } from 'lodash-es';
import { useMemo } from 'react';
import { Accordion } from '@cosmos/components/accordion';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import {
    Datatable,
    type DatatableProps,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import type {
    AccessReviewPeriodApplicationResponseDto,
    AccessReviewPeriodListResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';

interface CompletedReviewPeriodPresentationProps {
    id: number;
    startDate: string;
    endDate: string;
    isLoading: boolean;
    sortedApplications: AccessReviewPeriodApplicationResponseDto[];
    paginatedApplications: AccessReviewPeriodApplicationResponseDto[];
    columnsWithPeriodContext: DatatableProps<AccessReviewPeriodApplicationResponseDto>['columns'];
    hasLimitedAccess: boolean;
    activePeriodsData: AccessReviewPeriodListResponseDto | null;
    workspaceId: number;
    onFetchData: (params: FetchDataResponseParams) => void;
    onRowClick: (row: AccessReviewPeriodApplicationResponseDto) => void;
    onDownloadEvidence: () => void;
    onEditReview: () => void;
}

export const CompletedReviewPeriodPresentation = ({
    id,
    startDate,
    endDate,
    isLoading,
    sortedApplications,
    paginatedApplications,
    columnsWithPeriodContext,
    hasLimitedAccess,
    activePeriodsData,
    onFetchData,
    onRowClick,
    onDownloadEvidence,
    onEditReview,
}: CompletedReviewPeriodPresentationProps): React.JSX.Element => {
    const title = `Review period: ${formatDate(
        'sentence',
        startDate,
    )} - ${formatDate('sentence', endDate)}`;

    const actions: Action[] = useMemo(() => {
        return [
            {
                actionType: 'button',
                id: 'review-download-period-button',
                typeProps: {
                    startIconName: 'Download',
                    label: t`Download evidence package`,
                    level: 'secondary',
                    onClick: onDownloadEvidence,
                    'data-id': 'assetDetails-notesCard-download-button',
                },
            },
            ...(isEmpty(activePeriodsData?.data) && !hasLimitedAccess
                ? [
                      {
                          actionType: 'button' as const,
                          id: 'review-edit-period-button',
                          typeProps: {
                              label: t`Edit review`,
                              level: 'tertiary' as const,
                              onClick: onEditReview,
                              'data-id': 'assetDetails-notesCard-edit-button',
                          },
                      },
                  ]
                : []),
        ];
    }, [activePeriodsData, hasLimitedAccess, onDownloadEvidence, onEditReview]);

    return (
        <Accordion
            key={id}
            data-id="9q9qtv99"
            title={title}
            data-testid="CompletedReviewPeriodPresentation"
            body={
                <Stack gap="6x" direction="column">
                    <Datatable
                        isSortable
                        tableId={`datatable-uar-applications-${id}`}
                        data-id="datatable-uar-applications"
                        isLoading={isLoading}
                        total={sortedApplications.length}
                        data={paginatedApplications}
                        columns={columnsWithPeriodContext}
                        emptyStateProps={{
                            title: t`No applications completed found`,
                            description: t`No applications completed found`,
                        }}
                        tableSearchProps={{
                            hideSearch: true,
                        }}
                        filterViewModeProps={{
                            props: {
                                selectedOption: 'pinned',
                                initialSelectedOption: 'pinned',
                                togglePinnedLabel: t`Pin filters to page`,
                                toggleUnpinnedLabel: t`Move filters to dropdown`,
                            },
                            viewMode: 'pinned',
                        }}
                        onFetchData={onFetchData}
                        onRowClick={({ row }) => {
                            onRowClick(row);
                        }}
                    />
                    <ActionStack
                        gap="2x"
                        data-id="action-stack-completed"
                        actions={actions}
                    />
                </Stack>
            }
        />
    );
};
