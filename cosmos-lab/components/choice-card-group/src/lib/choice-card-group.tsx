import { useRef } from 'react';
import { <PERSON>Field } from '@cosmos/components/form-field';
import {
    ChoiceCard,
    type ChoiceCardProps,
} from '@cosmos-lab/components/choice-card';
import { DEFAULT_DATA_ID, OPTION_ORIENTATIONS } from './constants';
import { useCalculatedOrientation } from './hooks';
import { StyledOptionWrapperDiv } from './styles';
import type {
    ChoiceCardInputType,
    ChoiceCardOption,
    OptionOrientation,
} from './types';

type LimitedChoiceCardProps = Omit<
    ChoiceCardProps,
    | 'aria-labelledby'
    | 'checked'
    | 'defaultChecked'
    | 'onChange'
    | 'value'
    | 'label'
    | 'helpText'
>;
export interface ChoiceCardGroupProps extends LimitedChoiceCardProps {
    /** The options from which the user can choose.
     *
     * See [ChoiceCard documentation](/?path=/docs/components-choicecard--docs) for individual option props.
     */
    options: ChoiceCardOption[];

    /**
     * Function called when value changes.
     */
    onChange: (selections: string[]) => void;

    /**
     * The value of the controlled ChoiceCard field group.
     */
    value?: string[] | string;

    /**
     * Is a checkbox group or a radio button group.
     */
    choiceCardInputType: ChoiceCardInputType;
    /**
     * For exceptional use-cases only - most of the time, if you're using this, something is wrong ('horizontal' 'vertical').
     */
    cosmosUseWithCaution_forceOptionOrientation?: OptionOrientation;
    /**
     * Optional label for the field group title.
     */
    label?: string;
    /**
     * Optional help text for the field group title.
     */
    helpText?: string;
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Whether the choice card group is disabled.
     */
    disabled?: boolean;
    /**
     * A unique identifier for the form this field is a part of.
     */
    formId: string;
    /**
     * Name of the form.
     */
    name: string;
    /**
     * Whether the choice card group is required or not.
     */
    required?: boolean;
}

/**
 * Groups multiple choice cards together with shared validation and selection behavior for radio or checkbox interactions.
 *
 * [ChoiceCardGroup in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=46194-66122&t=HKkTPjsywjapzNly-4).
 */
export const ChoiceCardGroup = ({
    cosmosUseWithCaution_forceOptionOrientation = undefined,
    'data-id': dataId = DEFAULT_DATA_ID,
    disabled = undefined,
    formId,
    name,
    onChange,
    options,
    required = false,
    value,
    choiceCardInputType,
    label = undefined,
    helpText = undefined,
}: ChoiceCardGroupProps): React.JSX.Element => {
    const optionContainerRef = useRef(null);
    const optionOrientation = useCalculatedOrientation({
        forceOptionOrientation: cosmosUseWithCaution_forceOptionOrientation,
        horizontalConfig: OPTION_ORIENTATIONS.horizontal,
        optionContainerRef,
        optionsCount: options.length,
    });

    const { gap } = OPTION_ORIENTATIONS[optionOrientation];

    const handleCheckboxChange = ({
        isChecked,
        optionValue,
    }: {
        isChecked: boolean | 'indeterminate';
        optionValue: string;
    }) => {
        if (Array.isArray(value)) {
            if (isChecked && !value.includes(optionValue)) {
                onChange([...value, optionValue]);
            } else {
                onChange(value.filter((item) => item !== optionValue));
            }
        }
    };

    const handleRadioChange = ({ optionValue }: { optionValue: string }) => {
        onChange([optionValue]);
    };

    const mapOptions = (
        <StyledOptionWrapperDiv
            ref={optionContainerRef}
            $gap={gap}
            $optionOrientation={optionOrientation}
        >
            {options.map((option) => {
                const {
                    disabled: optionDisabled,
                    readOnly: optionReadOnly,
                    helpText: optionHelpText,
                    label: optionLabel,
                    value: optionValue,
                    slot: optionSlot,
                } = option;

                const checked = Array.isArray(value)
                    ? value.includes(optionValue)
                    : value === optionValue;

                if (choiceCardInputType === 'checkbox') {
                    return (
                        <ChoiceCard
                            aria-describedby={`${dataId}-${optionValue}-checkbox-group`}
                            aria-labelledby={`${dataId}-${optionValue}-checkbox-group`}
                            data-id={`${dataId}-${optionValue}`}
                            checked={checked}
                            choiceCardInputType="checkbox"
                            disabled={disabled || optionDisabled}
                            formId={`${formId}-${optionValue}`}
                            helpText={optionHelpText}
                            key={optionLabel}
                            label={optionLabel}
                            name={name}
                            required={required}
                            value={optionValue}
                            slot={optionSlot}
                            readOnly={optionReadOnly}
                            onChange={(isChecked: boolean) => {
                                handleCheckboxChange({
                                    isChecked,
                                    optionValue,
                                });
                            }}
                        />
                    );
                }

                return (
                    <ChoiceCard
                        aria-describedby={`${dataId}-${optionValue}-radio-group`}
                        aria-labelledby={`${dataId}-${optionValue}-radio-group`}
                        data-id={`${dataId}-${optionValue}`}
                        checked={checked}
                        choiceCardInputType="radio"
                        disabled={disabled || optionDisabled}
                        formId={`${formId}-${optionValue}`}
                        helpText={optionHelpText}
                        key={optionLabel}
                        label={optionLabel}
                        name={name}
                        required={required}
                        value={optionValue}
                        slot={optionSlot}
                        readOnly={optionReadOnly}
                        onChange={() => {
                            handleRadioChange({
                                optionValue,
                            });
                        }}
                    />
                );
            })}
        </StyledOptionWrapperDiv>
    );

    return label ? (
        <FormField
            data-id={dataId}
            labelStyleOverrides={{ size: 'md' }}
            formId={formId}
            label={label}
            helpText={helpText ?? undefined}
            name={name}
            renderInput={() => {
                return mapOptions;
            }}
        />
    ) : (
        mapOptions
    );
};
