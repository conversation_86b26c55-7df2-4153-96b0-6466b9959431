import { noop } from 'lodash-es';
import { useState } from 'react';
import {
    getLanguageValue,
    type Language,
    LanguagePickerComponent,
} from '@components/language-picker';
import { sharedAuditHubSettingsController } from '@controllers/audit-hub';
import { snackbarController } from '@controllers/snackbar';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import type { SelectFieldProps } from '@cosmos/components/select-field';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import type {
    AuditorMeResponseDto,
    OldAuditorRequestDto,
} from '@globals/api-sdk/types';
import { zOldAuditorRequestDto } from '@globals/api-sdk/zod';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormSchema, useFormSubmit } from '@ui/forms';

type Props = Omit<SelectFieldProps, 'options' | 'loaderLabel'>;

type FormData = Partial<OldAuditorRequestDto> & {
    newLanguage?: { value: Language };
};

const formSchema = (initialValues: AuditorMeResponseDto): FormSchema => {
    return {
        firstName: {
            type: 'text',
            initialValue: initialValues.firstName,
            label: t`First Name`,
            validator: zOldAuditorRequestDto.shape.firstName.trim().min(1, {
                message: t`First Name cannot be empty`,
            }),
        },
        lastName: {
            type: 'text',
            initialValue: initialValues.lastName,
            label: t`Last Name`,
            validator: zOldAuditorRequestDto.shape.lastName.trim().min(1, {
                message: t`Last Name cannot be empty`,
            }),
        },
        firmName: {
            type: 'text',
            initialValue: initialValues.firmName,
            helpText: t`This will modify the firm name for all auditors in the domain`,
            label: t`Firm Name`,
            validator: zOldAuditorRequestDto.shape.firmName.trim().min(1, {
                message: t`Firm Name cannot be empty`,
            }),
        },
        customFirmName: {
            type: 'text',
            initialValue: initialValues.customFirmName || '',
            helpText: t`This will allow you to display a custom firm name for your auditor profile`,
            label: t`Custom Firm Name`,
            isOptional: true,
        },
        newLanguage: {
            isOptional: false,
            type: 'custom',
            initialValue: getLanguageValue(initialValues.language as Language),
            label: t`Language`,
            render: (fieldProps: Props) => (
                <LanguagePickerComponent
                    formId={fieldProps.formId}
                    label={fieldProps.label}
                    name={fieldProps.name}
                    value={fieldProps.value}
                    disabled={fieldProps.disabled}
                    required={fieldProps.required}
                    feedback={fieldProps.feedback}
                    data-id="V4gzp_MS"
                    onChange={fieldProps.onChange}
                />
            ),
            validateWithDefault: 'select',
        },
    };
};

export const AuditorSettingsProfileView = observer((): JSX.Element => {
    const { auditorProfile, isLoading } = sharedAuditHubSettingsController;
    const [isEditorMode, setIsEditorMode] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const auditorLanguage = (auditorProfile?.language ??
        'ENGLISH_US') as Language;
    const { formRef, triggerSubmit } = useFormSubmit();

    if (isLoading || !auditorProfile) {
        return <Skeleton />;
    }

    const handleSave = (data: FormData) => {
        setIsSaving(true);
        try {
            sharedAuditHubSettingsController.updateAuditorSettings({
                ...auditorProfile,
                firstName: data.firstName ?? auditorProfile.firstName,
                lastName: data.lastName ?? auditorProfile.lastName,
                firmName: data.firmName ?? auditorProfile.firmName,
                customFirmName: data.customFirmName ?? undefined,
                language: data.newLanguage?.value as Language,
            });

            setIsEditorMode(false);
        } catch {
            snackbarController.addSnackbar({
                id: 'settings-save-error',
                props: {
                    title: t`Error`,
                    description: t`Failed to save settings. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } finally {
            setIsSaving(false);
        }
    };

    return (
        <Card
            title="Details"
            data-testid="AuditorSettingsProfileView"
            data-id="xP7nKw3L"
            isEditMode={isEditorMode}
            actions={
                isEditorMode
                    ? []
                    : [
                          {
                              actionType: 'button',
                              id: 'edit-button',
                              typeProps: {
                                  label: t`Edit`,
                                  level: 'secondary',
                                  onClick: () => {
                                      setIsEditorMode(true);
                                  },
                              },
                          },
                      ]
            }
            body={
                <Stack direction="column" gap="xl">
                    {isEditorMode ? (
                        <Box pl="3xl">
                            <Form
                                hasExternalSubmitButton
                                ref={formRef}
                                formId="auditor-settings-profile-form"
                                data-id={'auditor-settings-profile-form'}
                                schema={formSchema(auditorProfile)}
                                onSubmit={handleSave}
                            />
                            <Stack
                                direction="row"
                                align="center"
                                gap="4x"
                                mt="lg"
                                width="100%"
                            >
                                <Button
                                    label={t`Cancel`}
                                    colorScheme="neutral"
                                    isLoading={isSaving}
                                    onClick={() => {
                                        setIsEditorMode(false);
                                    }}
                                />
                                <Button
                                    label={t`Save`}
                                    colorScheme="primary"
                                    isLoading={isSaving}
                                    onClick={() => triggerSubmit()}
                                />
                            </Stack>
                        </Box>
                    ) : (
                        <>
                            <KeyValuePair
                                label={t`First Name`}
                                type="TEXT"
                                value={auditorProfile.firstName}
                            />
                            <KeyValuePair
                                label={t`Last Name`}
                                type="TEXT"
                                value={auditorProfile.lastName}
                            />
                            <KeyValuePair
                                label={t`Firm Name`}
                                type="TEXT"
                                value={auditorProfile.firmName}
                            />
                            <KeyValuePair
                                label={t`Custom Firm Name`}
                                type="TEXT"
                                value={auditorProfile.customFirmName || '-'}
                            />
                            <LanguagePickerComponent
                                label={t`Language`}
                                formId="language-form"
                                name="language"
                                value={getLanguageValue(auditorLanguage)}
                                readOnly={!isEditorMode}
                                onChange={noop}
                            />
                        </>
                    )}
                </Stack>
            }
        />
    );
});
