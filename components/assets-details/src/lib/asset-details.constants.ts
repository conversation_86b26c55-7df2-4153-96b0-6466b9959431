import { ASSET_CLASS_TYPE, ASSET_TYPE } from '@controllers/asset';
import { t } from '@globals/i18n/macro';

export const ASSET_TYPE_LABEL = {
    [ASSET_TYPE.VIRTUAL]: 'Virtual',
    [ASSET_TYPE.PHYSICAL]: 'Physical',
} as const;

export const ASSET_CLASS_LABEL = {
    [ASSET_CLASS_TYPE.HARDWARE]: 'Hardware',
    [ASSET_CLASS_TYPE.DOCUMENT]: 'Document',
    [ASSET_CLASS_TYPE.PERSONNEL]: 'Personnel',
    [ASSET_CLASS_TYPE.SOFTWARE]: 'Software',
    [ASSET_CLASS_TYPE.CODE]: 'Code',
    [ASSET_CLASS_TYPE.CONTAINER]: 'Container',
    [ASSET_CLASS_TYPE.COMPUTE]: 'Compute',
    [ASSET_CLASS_TYPE.NETWORKING]: 'Networking',
    [ASSET_CLASS_TYPE.DATABASE]: 'Database',
    [ASSET_CLASS_TYPE.STORAGE]: 'Storage',
    [ASSET_CLASS_TYPE.POLICY]: 'Policy',
} as const;

export const USER_ROLES_LABELS = {
    EMPLOYEE: 'Employee',
    TECHGOV: 'Information security lead',
    AUDITOR: 'Auditor',
    ADMIN: 'Admin',
    ACT_AS_READ_ONLY: 'Act As Read Only',
    APP: 'App',
    WORKSPACE_ADMINISTRATOR: 'Workspace administrator',
    RISK_MANAGER: 'Risk manager',
    SERVICE_USER: 'Service user',
    REVIEWER: 'Reviewer',
    CONTROL_MANAGER: 'Control manager',
    POLICY_MANAGER: 'Policy manager',
    PEOPLE_OPS: 'Personnel compliance manager',
    DEVOPS_ENGINEER: 'DevOps engineer',
    TRUST_CENTER_MANAGER: 'Trust Center manager',
    TRUST_CENTER_REVIEWER: 'Trust Center reviewer',
    KNOWLEDGE_BASE: 'Knowledge Base',
} as const;

export const getUserRolesLabels = (rol: string): string => {
    switch (rol) {
        case 'EMPLOYEE': {
            return t`Employee`;
        }
        case 'TECHGOV': {
            return t`Information security lead`;
        }
        case 'AUDITOR': {
            return t`Auditor`;
        }
        case 'ADMIN': {
            return t`Admin`;
        }
        case 'ACT_AS_READ_ONLY': {
            return t`Act As Read Only`;
        }
        case 'APP': {
            return t`App`;
        }
        case 'RISK_MANAGER': {
            return t`Risk manager`;
        }
        case 'WORKSPACE_ADMINISTRATOR': {
            return t`Workspace administrator`;
        }
        case 'SERVICE_USER': {
            return t`Service user`;
        }
        case 'REVIEWER': {
            return t`Reviewer`;
        }
        case 'CONTROL_MANAGER': {
            return t`Control manager`;
        }
        case 'POLICY_MANAGER': {
            return t`Policy manager`;
        }
        case 'PEOPLE_OPS': {
            return t`Personnel compliance manager`;
        }
        case 'DEVOPS_ENGINEER': {
            return t`DevOps engineer`;
        }
        case 'TRUST_CENTER_MANAGER': {
            return t`Trust Center manager`;
        }
        case 'TRUST_CENTER_REVIEWER': {
            return t`Trust Center reviewer`;
        }
        case 'KNOWLEDGE_BASE': {
            return t`Knowledge Base`;
        }
        default: {
            return t`Unknown`;
        }
    }
};
