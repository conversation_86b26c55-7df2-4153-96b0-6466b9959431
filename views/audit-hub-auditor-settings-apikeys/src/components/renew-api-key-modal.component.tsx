import { useState } from 'react';
import { sharedAuditHubSettingsController } from '@controllers/audit-hub';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import {
    DatePickerField,
    type TDateISODate,
} from '@cosmos/components/date-picker-field';
import { Modal } from '@cosmos/components/modal';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import {
    calculateRenewalDate,
    DATES_UNAVAILABLE,
    getDateUnavailableText,
    getExpirationOptions,
    getOption12Months,
    getOptionCustom,
} from '../constants/setting-api-keys.constants';
import { convertToEndOfDay } from '../helpers';
import type { RenewApiKeyModalProps } from '../types/create-api-key-modal.types';

const RENEW_API_KEY_MODAL_ID = 'renew-api-key-modal';
const FORM_ID = 'renew-api-key-form';

const handleClose = () => {
    modalController.closeModal(RENEW_API_KEY_MODAL_ID);
};

export const RenewApiKeyModal = observer(
    ({ apiKey, onClose }: RenewApiKeyModalProps): React.JSX.Element => {
        const [expirationType, setExpirationType] = useState(
            getOption12Months(),
        );
        const initExpirationDate = calculateRenewalDate(apiKey.expiresAt);
        const [customDate, setCustomDate] = useState<TDateISODate>(
            initExpirationDate as TDateISODate,
        );

        const handleSubmit = () => {
            try {
                // Create a timestamp at the end of the selected day (23:59:59.999Z)
                const fullISODate = convertToEndOfDay(customDate);

                sharedAuditHubSettingsController.renewApiKey(
                    apiKey,
                    fullISODate,
                );

                onClose();
            } catch (error) {
                logger.error({
                    message: t`Failed to renew API key`,
                    additionalInfo: { error },
                });

                snackbarController.addSnackbar({
                    id: 'api-key-renew-error',
                    props: {
                        title: t`Failed to renew API key`,
                        description: t`An error occurred while renewing the API key.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            }
        };

        return (
            <>
                <Modal.Header
                    title={t`Renew API key`}
                    closeButtonAriaLabel={t`Close`}
                    onClose={handleClose}
                />
                <Modal.Body>
                    <Stack direction="column" gap="xl">
                        <SelectField
                            label={t`Expiration`}
                            formId={FORM_ID}
                            name="expirationType"
                            options={getExpirationOptions()}
                            value={expirationType}
                            onChange={(value) => {
                                setExpirationType(value);
                                if (value.value === getOption12Months().value) {
                                    setCustomDate(
                                        initExpirationDate as TDateISODate,
                                    );
                                }
                            }}
                        />

                        <Stack direction="column" gap="xs">
                            <DatePickerField
                                required
                                formId={FORM_ID}
                                label={t`Date`}
                                name="customExpirationDate"
                                locale="en-US"
                                yearSelectionFieldLabel={t`Select year`}
                                monthSelectionFieldLabel={t`Select month`}
                                value={customDate}
                                isMulti={false}
                                dateUnavailableText={getDateUnavailableText()}
                                getIsDateUnavailable={DATES_UNAVAILABLE}
                                onChange={(
                                    date: TDateISODate | TDateISODate[],
                                ) => {
                                    if (Array.isArray(date)) {
                                        return;
                                    }

                                    setCustomDate(date);
                                    if (date === initExpirationDate) {
                                        setExpirationType(getOption12Months());
                                    } else {
                                        setExpirationType(getOptionCustom());
                                    }
                                }}
                            />
                        </Stack>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onClose,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: handleSubmit,
                        },
                    ]}
                />
            </>
        );
    },
);
