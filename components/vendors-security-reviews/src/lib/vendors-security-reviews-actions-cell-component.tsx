import { sharedVendorsSecurityReviewMutationController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { useNavigate } from '@remix-run/react';
import type { VendorSecurityReviewTableRowCellProps } from './types/vendors-security-reviews.type';

export const VendorSecurityReviewsActionsCellComponent = observer(
    ({
        row: { original },
    }: VendorSecurityReviewTableRowCellProps): React.JSX.Element => {
        const navigate = useNavigate();

        const { deleteSecurityReview } =
            sharedVendorsSecurityReviewMutationController;

        const navigateToCompletedRoute = () => {
            navigate(`${original.id}/completed`);
        };

        const navigateToSOCCompletedRoute = () => {
            navigate(`${original.id}/soc/completed`);
        };

        const navigateToInProgressRoute = () => {
            navigate(`${original.id}`);
        };

        const navigateToSocRoute = () => {
            navigate(`${original.id}/soc`);
        };

        const handleOnClick = () => {
            if (original.status === 'COMPLETED') {
                if (original.type === 'SOC_REPORT') {
                    navigateToSOCCompletedRoute();

                    return;
                }

                navigateToCompletedRoute();

                return;
            }

            if (original.type === 'SOC_REPORT') {
                navigateToSocRoute();

                return;
            }

            navigateToInProgressRoute();
        };

        return (
            <Grid
                columns="3fr auto"
                gap="3x"
                data-testid="VendorSecurityReviewsActionsCellComponent"
                data-id="7xbJM0NT"
            >
                <Button
                    level="secondary"
                    label={
                        original.status === 'COMPLETED' ? t`View` : t`Continue`
                    }
                    onClick={handleOnClick}
                />

                <Box>
                    <Button
                        isIconOnly
                        label={t`Delete`}
                        colorScheme="danger"
                        startIconName="Trash"
                        level="tertiary"
                        onClick={() => {
                            openConfirmationModal({
                                title: t`Are you sure?`,
                                body: t`This will permanently delete the review.`,
                                confirmText: t`Delete`,
                                cancelText: t`Cancel`,
                                type: 'danger',
                                size: 'md',
                                onConfirm: () => {
                                    deleteSecurityReview(original.id);
                                },
                                onCancel: () => {
                                    closeConfirmationModal();
                                },
                            });
                        }}
                    />
                </Box>
            </Grid>
        );
    },
);
