import type { IconName } from '@cosmos/components/icon';
import type { ColorScheme } from '@cosmos/components/metadata';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';

export const getStatusOptions = (): Record<
    string,
    {
        id: string;
        label: string;
        colorScheme?: ColorScheme;
        startIconName?: IconName;
    }
> => ({
    OUTSTANDING: { id: 'OUTSTANDING', label: t`New` },
    IN_REVIEW: { id: 'IN_REVIEW', label: t`Prepared` },
    ACCEPTED: { id: 'ACCEPTED', label: t`Completed` },
    DELETE: {
        id: 'DELETE-REQUEST',
        label: t`Delete request`,
        colorScheme: 'critical' as ColorScheme,
        startIconName: 'Trash' as IconName,
    },
});

export const getStatusOptionsDropdown = (): SchemaDropdownItems =>
    Object.values(getStatusOptions()).map((option) => ({
        ...option,
        type: 'item' as const,
    }));

export const STATUS_OPTIONS_DROPDOWN = getStatusOptionsDropdown();

export const getStatusMap = (): Record<
    string,
    { label: string; colorScheme: ColorScheme; icon: string }
> => ({
    IN_REVIEW: {
        label: t`Prepared`,
        colorScheme: 'warning',
        icon: 'InProgress' as IconName,
    },
    OUTSTANDING: {
        label: t`New`,
        colorScheme: 'neutral',
        icon: 'Circle' as IconName,
    },
    REJECTED: {
        label: t`Rejected`,
        colorScheme: 'critical',
        icon: 'WarningDiamond' as IconName,
    },
    ACCEPTED: {
        label: t`Completed`,
        colorScheme: 'success',
        icon: 'CheckCircle' as IconName,
    },
});

export const STATUS_MAP = getStatusMap();
