import { useMemo } from 'react';
import { sharedRequirementCreateController } from '@controllers/requirements';
import { Button } from '@cosmos/components/button';
import { Combobox } from '@cosmos/components/combobox';
import { FormField } from '@cosmos/components/form-field';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import type { CustomFieldRenderProps } from '@ui/forms';
import { openAddCategoryModal } from './helpers/open-add-category-modal.helper';

export const CategoryFieldWithAddButton = observer(
    ({
        formId,
        name,
        'data-id': dataId,
        onChange,
    }: CustomFieldRenderProps): React.JSX.Element => {
        const { selectedCategoryOption, requirementCategoriesOptions } =
            sharedRequirementCreateController;

        const options = useMemo(
            () => toJS(requirementCategoriesOptions),
            [requirementCategoriesOptions],
        );

        const selectedOption = useMemo(
            () => toJS(selectedCategoryOption),
            [selectedCategoryOption],
        );

        return (
            <FormField
                label={t`Category`}
                helpText={t`This category is used for filtering on the Framework overview page`}
                layout="stack"
                formId={formId}
                name={name}
                data-id={dataId}
                renderInput={({
                    describeIds,
                    inputId,
                    inputTestId,
                    labelId,
                    feedbackType,
                }) => (
                    <Grid
                        columns="1fr auto"
                        gap="md"
                        align="center"
                        data-id="category-field-grid"
                    >
                        <Combobox
                            key={selectedOption?.value}
                            aria-describedby={describeIds}
                            aria-labelledby={labelId}
                            data-id={inputTestId}
                            id={inputId}
                            name={name}
                            options={options}
                            clearSelectedItemButtonLabel={t`Clear`}
                            loaderLabel={t`Loading categories...`}
                            feedbackType={feedbackType}
                            defaultValue={selectedOption}
                            onChange={onChange}
                        />
                        <Button
                            label={t`Add category`}
                            level="secondary"
                            colorScheme="neutral"
                            data-id="add-category-button"
                            onClick={openAddCategoryModal}
                        />
                    </Grid>
                )}
            />
        );
    },
);
