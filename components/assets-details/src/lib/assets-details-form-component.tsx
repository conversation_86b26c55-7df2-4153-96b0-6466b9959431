import type { AssetResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { sharedAssetFormModel } from '@models/assets';
import { Form, type FormValues } from '@ui/forms';

interface AssetsDetailsFormComponentProps {
    formRef: React.RefObject<HTMLFormElement>;
    state: AssetResponseDto | null;
    onSubmit: (values: FormValues) => void;
}

export const AssetsDetailsFormComponent = observer(
    ({
        formRef,
        state,
        onSubmit,
    }: AssetsDetailsFormComponentProps): React.JSX.Element => {
        const { classOptions, customFields } = sharedAssetFormModel;

        const assetsClassTypes = classOptions.filter((option) =>
            state?.assetClassTypes.some(
                (classType) => classType.assetClassType === option.value,
            ),
        );

        const schema = {
            name: sharedAssetFormModel.nameFieldConfig(state?.name),
            class: sharedAssetFormModel.classFieldConfig(assetsClassTypes),
            type: sharedAssetFormModel.typeFieldConfig(state?.assetType),
            accountName: sharedAssetFormModel.accountNameFieldConfig(
                state?.company,
            ),
            description: sharedAssetFormModel.descriptionFieldConfig(
                state?.description,
            ),
            uniqueId: sharedAssetFormModel.uniqueIdFieldConfig(state?.uniqueId),
            assetId: sharedAssetFormModel.assetIdFieldConfig(
                state?.id ? String(state.id) : '',
            ),
            ...customFields,
        };

        return (
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="assets-details-form"
                data-id="assets-details-form"
                schema={schema}
                data-testid="AssetsDetailsFormComponent"
                onSubmit={onSubmit}
            />
        );
    },
);
