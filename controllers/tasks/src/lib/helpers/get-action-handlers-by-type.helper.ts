import type { UpcomingTaskDetailResponseDto } from '@globals/api-sdk/types';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedTaskModalState } from '@models/tasks';
import type { NavigateFunction } from '@remix-run/react';
import { CustomTaskCompleteController } from '../custom-task-complete-controller';
import type {
    TaskAccordionType,
    TaskActionHandlers,
} from '../types/tasks.types';

/**
 * Gets the appropriate handlers based on the task type.
 *
 * @param type - The task accordion type.
 * @param task - The task details.
 * @returns An object containing the appropriate handlers for the task type.
 */
export const getActionHandlersByType = (
    type: TaskAccordionType | undefined,
    task: UpcomingTaskDetailResponseDto,
): TaskActionHandlers => {
    const workspaceId = sharedWorkspacesController.currentWorkspaceId;

    switch (type) {
        case 'policyRenewals':
        case 'policyApprovals': {
            return {
                onReview: (navigate: NavigateFunction) => {
                    navigate(
                        `/workspaces/${workspaceId}/governance/policies/builder/${task.id}/overview`, // Task ID is the policy ID
                    );
                },
            };
        }
        case 'vendor': {
            return {
                onReview: (navigate: NavigateFunction) => {
                    navigate(
                        `/workspaces/${workspaceId}/vendors/current/${task.id}/overview`, // Task ID is the vendor ID
                    );
                },
            };
        }
        case 'evidence': {
            return {
                onReview: (navigate: NavigateFunction) => {
                    navigate(
                        `/workspaces/${workspaceId}/compliance/evidence/${task.id}`, // Task ID is the evidence ID
                    );
                },
            };
        }
        case 'general': {
            return {
                onEdit: () => {
                    sharedTaskModalState
                        .openModal({ taskId: task.id })
                        .catch((error) => {
                            // Handle error if needed
                            console.error('Error opening task modal:', error);
                        });
                },
                onToggleComplete: () => {
                    const completeController =
                        new CustomTaskCompleteController();

                    completeController.completeTask(
                        { taskId: task.id },
                        task.completedAt,
                    );
                },
            };
        }
        case 'control':
        case 'controlApprovals': {
            return {
                onEdit: () => {
                    sharedTaskModalState
                        .openModal({ taskId: task.id })
                        .catch((error) => {
                            // Handle error if needed
                            console.error('Error opening task modal:', error);
                        });
                },
                onReview: (navigate: NavigateFunction) => {
                    navigate(
                        // TODO: figure out where the controlId should be coming from
                        `/workspaces/${workspaceId}/compliance/controls/${task.id}/overview`,
                    );
                },
                onToggleComplete: () => {
                    new CustomTaskCompleteController().completeTask(
                        { taskId: task.id },
                        task.completedAt,
                    );
                },
            };
        }
        case 'risk': {
            return {
                onEdit: () => {
                    sharedTaskModalState
                        .openModal({ taskId: task.id })
                        .catch((error) => {
                            // Handle error if needed
                            console.error('Error opening task modal:', error);
                        });
                },
                onReview: (navigate: NavigateFunction) => {
                    navigate(
                        // TODO: figure out where the riskId should be coming from
                        `/workspaces/${workspaceId}/risk/register/management/${task.id}/overview`,
                    );
                },
                onToggleComplete: () => {
                    new CustomTaskCompleteController().completeTask(
                        { taskId: task.id },
                        task.completedAt,
                    );
                },
            };
        }
        default: {
            return {};
        }
    }
};
