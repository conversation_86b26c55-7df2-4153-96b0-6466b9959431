import { sharedAuditorController } from '@controllers/auditor';
import {
    dataCategorizeBlue1,
    dataCategorizeGreen2,
    dataCategorizeYellow3,
} from '@cosmos/constants/tokens';
import {
    DataDonut,
    type DataDonutSliceData,
} from '@cosmos-lab/components/data-donut';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const AuditHubAuditorClientAuditDonutDetailsView = observer(
    (): React.JSX.Element => {
        const { auditSummaryByIdData } = sharedAuditorController;

        const acceptedRequests = auditSummaryByIdData?.acceptedRequests ?? 0;
        const inReviewRequests = auditSummaryByIdData?.inReviewRequests ?? 0;
        const outstandingRequests =
            auditSummaryByIdData?.outstandingRequests ?? 0;

        const donutValues: DataDonutSliceData[] = [
            {
                value: acceptedRequests,
                color: dataCategorizeGreen2,
                label: t`Completed`,
            },
            {
                value: inReviewRequests,
                color: dataCategorizeYellow3,
                label: t`Prepared`,
            },
            {
                value: outstandingRequests,
                color: dataCategorizeBlue1,
                label: t`New`,
            },
        ];

        return (
            <DataDonut
                showLegend
                data-id="audit-hub-donut-details"
                size="lg"
                unit={t`Requests`}
                values={donutValues}
                legendPosition="right"
            />
        );
    },
);
