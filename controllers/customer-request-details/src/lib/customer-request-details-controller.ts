import { isEmpty, isError } from 'lodash-es';
import { sharedAuditHubControlsController } from '@controllers/audit-hub';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import type { DatatableRowSelectionState } from '@cosmos/components/datatable';
import {
    auditHubControllerGenerateRequestControlEvidencePackageMutation,
    auditHubControllerGetAuditCustomerRequestControlEvidencesOptions,
    customerRequestControllerAssignControlsToAuditRequestMutation,
    customerRequestControllerDeleteCustomerRequestsMutation,
    customerRequestControllerGetCustomerRequestDetailsWithFrameworkOptions,
    customerRequestControllerGetCustomerRequestEvidencesOptions,
    customerRequestControllerUpdateCustomerRequestDetailsMutation,
    customerRequestControllerUpdateCustomerRequestStatusMutation,
} from '@globals/api-sdk/queries';
import type {
    AuditHubEvidenceResponseDto,
    CustomerRequestDetailsWithFrameworkResponseDto,
    CustomerRequestEvidenceResponseDto,
    DeleteCustomerRequestsRequestDto,
    UpdateCustomerRequestDetailsRequestDto,
    UpdateCustomerRequestStatusesRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';

class CustomerRequestDetailsController {
    auditorId: string | null = '';
    clientId: string | null = '';
    auditorFrameworkId: string | null = '';
    requestId: number | null = null;
    selectedControlIds: number[] = [];
    isEvidencePackageDownloading = false;

    customerQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestDetailsWithFrameworkOptions,
    );

    updateDetailsMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestDetailsMutation,
    );

    updateStatusMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestStatusMutation,
    );

    customerRequestEvidence = new ObservedQuery(
        customerRequestControllerGetCustomerRequestEvidencesOptions,
    );

    generateRequestControlEvidencePackageMutation = new ObservedMutation(
        auditHubControllerGenerateRequestControlEvidencePackageMutation,
    );

    deleteCustomerRequestMutation = new ObservedMutation(
        customerRequestControllerDeleteCustomerRequestsMutation,
    );

    assignControlsToAuditRequestMutation = new ObservedMutation(
        customerRequestControllerAssignControlsToAuditRequestMutation,
    );

    controlEvidencesQuery = new ObservedQuery(
        auditHubControllerGetAuditCustomerRequestControlEvidencesOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get customerRequestDetails(): CustomerRequestDetailsWithFrameworkResponseDto | null {
        return this.customerQuery.data;
    }

    get isLoading(): boolean {
        return this.customerQuery.isLoading;
    }

    get isSaving(): boolean {
        return this.updateDetailsMutation.isIdle;
    }

    get customerRequestEvidences(): CustomerRequestEvidenceResponseDto[] {
        return this.customerRequestEvidence.data?.evidences ?? [];
    }

    get customerRequestEvidenceIsLoading(): boolean {
        return this.customerRequestEvidence.isLoading;
    }

    get getFrameworkId(): string | null {
        return this.auditorFrameworkId;
    }

    get getAuditorId(): string | null {
        return this.auditorId;
    }

    get getRequestId(): number | null {
        return this.requestId;
    }

    get controlEvidences(): AuditHubEvidenceResponseDto[] {
        const data = this.controlEvidencesQuery.data as {
            data?: AuditHubEvidenceResponseDto[];
        } | null;

        return data?.data ?? [];
    }

    get controlEvidencesIsLoading(): boolean {
        return this.controlEvidencesQuery.isLoading;
    }

    /**
     * Loads control evidences for a specific control if it has valid evidence.
     */
    loadControlEvidences = (controlId: number): void => {
        when(
            () =>
                !this.customerRequestEvidenceIsLoading &&
                !isEmpty(this.customerRequestEvidences),
        )
            .then(() => {
                const controlData = this.customerRequestEvidences.find(
                    (control) => Number(control.id) === controlId,
                );

                if (
                    this.auditorFrameworkId &&
                    this.requestId &&
                    controlId &&
                    controlData?.hasEvidence
                ) {
                    this.controlEvidencesQuery.load({
                        path: {
                            auditId: String(this.auditorFrameworkId),
                            customerRequestId: Number(this.requestId),
                            controlId: Number(controlId),
                        },
                    });
                }
            })
            .catch((error) => {
                logger.error({
                    message: 'Failed to load control evidences for control',
                    additionalInfo: {
                        controlId,
                    },
                    errorObject: {
                        message: isError(error) ? error.message : String(error),
                        statusCode: '500',
                    },
                });
            });
    };

    updateCustomerRequestDetails = async (
        requestId: number,
        updateData: UpdateCustomerRequestDetailsRequestDto,
    ) => {
        try {
            await this.updateDetailsMutation.mutateAsync({
                path: { customerRequestId: requestId },
                body: updateData,
            });

            this.customerQuery.invalidate();

            snackbarController.addSnackbar({
                id: 'customer-request-updated',
                props: {
                    title: t`Customer request updated successfully.`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch {
            snackbarController.addSnackbar({
                id: 'customer-request-update-error',
                props: {
                    title: t`Update Failed`,
                    description: t`An error occurred while updating the customer request. Try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    updateCustomerRequestStatus = (
        statusData: UpdateCustomerRequestStatusesRequestDto,
    ) => {
        this.updateStatusMutation.mutate({
            body: statusData,
        });

        when(
            () => !this.updateStatusMutation.isPending,
            () => {
                if (this.updateStatusMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'customer-request-status-update-error',
                        props: {
                            title: t`Update Failed`,
                            description: t`An error occurred while updating the customer request status. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.customerQuery.invalidate();
                sharedCustomerRequestsController.customerRequestListQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'customer-request-status-updated',
                    props: {
                        title: t`Customer request status updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    generateRequestControlEvidencePackage = async (controlsIds: number[]) => {
        try {
            await this.generateRequestControlEvidencePackageMutation.mutateAsync(
                {
                    path: {
                        auditId: this.auditorFrameworkId as string,
                        customerRequestId: this.requestId as number,
                    },
                    body: {
                        selectedControlIds: controlsIds,
                    },
                },
            );

            snackbarController.addSnackbar({
                id: 'generating-evidence-package',
                props: {
                    title: t`Generating evidence package...`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch {
            snackbarController.addSnackbar({
                id: 'generate-evidence-package-error',
                props: {
                    title: t`Generation Failed`,
                    description: t`An error occurred while generating the evidence package. Try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    deleteCustomerRequests = (requestDto: DeleteCustomerRequestsRequestDto) => {
        this.deleteCustomerRequestMutation.mutate({
            body: requestDto,
        });

        when(
            () => !this.deleteCustomerRequestMutation.isPending,
            () => {
                if (this.deleteCustomerRequestMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'delete-customer-request-error',
                        props: {
                            title: t`Delete Failed`,
                            description: t`An error occurred while deleting the customer request. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.customerQuery.invalidate();
                sharedCustomerRequestsController.customerRequestListQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'customer-request-deleted',
                    props: {
                        title: t`Customer request deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateRequestControls = (controlIds: number[]) => {
        when(() => !this.customerQuery.isLoading)
            .then(() => {
                this.assignControlsToAuditRequestMutation
                    .mutateAsync({
                        path: {
                            customerRequestId: this.requestId as number,
                        },
                        body: {
                            controlIds,
                        },
                    })
                    .then(() => {
                        this.customerRequestEvidence.invalidate();
                        sharedAuditHubControlsController.auditCustomerRequestControlsQuery.invalidate();
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: 'update-request-controls-error',
                            props: {
                                title: t`Update Failed`,
                                description: t`An error occurred while updating the request controls. Try again later.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'update-request-controls-error',
                    props: {
                        title: t`Update Failed`,
                        description: t`An error occurred while updating the request controls. Try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    setSelectedControls = (selectedIds: number[]) => {
        this.selectedControlIds = selectedIds;
    };

    downloadSelectedControls = () => {
        when(() => !this.isEvidencePackageDownloading)
            .then(() => {
                this._downloadControls(this.selectedControlIds).catch(() => {
                    snackbarController.addSnackbar({
                        id: 'download-controls-error',
                        props: {
                            title: t`Download Failed`,
                            description: t`An error occurred while downloading the controls. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'download-controls-error',
                    props: {
                        title: t`Download Failed`,
                        description: t`An error occurred while downloading the controls. Try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    downloadAllControls = () => {
        when(() => !this.isEvidencePackageDownloading)
            .then(() => {
                const allIds = this.customerRequestEvidences
                    .map((evidence) => evidence.id)
                    .filter((id): id is number => id !== undefined);

                this._downloadControls(allIds).catch(() => {
                    snackbarController.addSnackbar({
                        id: 'download-controls-error',
                        props: {
                            title: t`Download Failed`,
                            description: t`An error occurred while downloading the controls. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'download-controls-error',
                    props: {
                        title: t`Download Failed`,
                        description: t`An error occurred while downloading the controls. Try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ) => {
        if (
            this.customerRequestEvidenceIsLoading ||
            isEmpty(this.customerRequestEvidences)
        ) {
            this.customerRequestEvidence.load({
                path: { customerRequestId: Number(this.requestId) },
            });

            when(
                () =>
                    !this.customerRequestEvidenceIsLoading &&
                    !isEmpty(this.customerRequestEvidences),
                () => {
                    this.handleRowSelection(currentRowSelectionState);
                },
            );

            return;
        }

        const selectedIndices = Object.keys(
            currentRowSelectionState.selectedRows,
        );
        const selectedRowData = selectedIndices.map(
            (index) => this.customerRequestEvidences[parseInt(index)],
        );

        const selectedIds = selectedRowData
            .map((row) => row.id)
            .filter((id): id is number => id !== undefined);

        this.setSelectedControls(selectedIds);
    };

    _downloadControls = async (controlIds: number[]) => {
        this.isEvidencePackageDownloading = true;
        await this.generateRequestControlEvidencePackage(controlIds);
        this.isEvidencePackageDownloading = false;
    };
}

export const sharedCustomerRequestDetailsController =
    new CustomerRequestDetailsController();
