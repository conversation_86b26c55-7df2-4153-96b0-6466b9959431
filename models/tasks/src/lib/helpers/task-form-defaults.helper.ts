import { convertToISO8601String } from '@helpers/date-time';
import type {
    RecurringScheduleSpecValues,
    TaskDataValues,
    TaskFormValues,
} from '../types/task-form-values.type';
import { getTaskFrequencyOptions } from './task-options.helper';

export const getRecurringScheduleDefaults =
    (): RecurringScheduleSpecValues => ({
        scheduleType: getTaskFrequencyOptions()[0],
        scheduleStart: convertToISO8601String(new Date()),
        scheduleInterval: 1,
        scheduleEndsOn: 'NEVER',
        scheduleEnd: '',
        scheduleDaysOfWeek: [],
        scheduleMonthlyType: 'specificDayOfMonth',
        scheduleDayOfMonth: null,
    });

export const getTaskDefaults = (): TaskDataValues => ({
    title: '',
    description: '',
    assignee: null,
    dueDate: '',
    control: null,
    risk: null,
    isRecurringTask: false,
    taskType: 'GENERAL',
});

export const getTaskFormDefaults = (): TaskFormValues => ({
    ...getTaskDefaults(),
    ...getRecurringScheduleDefaults(),
});
