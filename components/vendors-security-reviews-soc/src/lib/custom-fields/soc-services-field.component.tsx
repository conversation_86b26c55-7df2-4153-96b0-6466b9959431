import { useFieldArray } from 'react-hook-form';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import { DEFAULT_SERVICE } from '../constants/vendors-security-reviews-soc.constants';

export const SocServicesField = ({
    'data-id': dataId,
    name,
    formId,
}: Omit<CustomFieldRenderProps, 'value' | 'onChange'>): React.JSX.Element => {
    const { fields, append, remove } = useFieldArray({
        name,
    });

    const addService = () => {
        append(DEFAULT_SERVICE);
    };

    const removeService = (index: number) => {
        remove(index);
    };

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label="Services"
            data-testid="SocServicesField"
            data-id={dataId}
            renderInput={() => (
                <Stack gap="lg" direction="column" data-id={`${dataId}-inputs`}>
                    {fields.map((field, index) => (
                        <Stack
                            key={field.id}
                            gap="md"
                            align="start"
                            data-id={`${dataId}-service-${index}`}
                        >
                            <UniversalFormField
                                name={`${name}[${index}].name`}
                                formId={formId}
                                data-id={`${dataId}-name-field`}
                            />

                            <Box pt="6x">
                                <Button
                                    isIconOnly
                                    label="Remove service"
                                    startIconName="Trash"
                                    level="tertiary"
                                    colorScheme="danger"
                                    type="button"
                                    onClick={() => {
                                        removeService(index);
                                    }}
                                />
                            </Box>
                        </Stack>
                    ))}

                    <Stack direction="row" align="start">
                        <Button
                            label="Add service"
                            level="tertiary"
                            type="button"
                            onClick={addService}
                        />
                    </Stack>
                </Stack>
            )}
        />
    );
};
