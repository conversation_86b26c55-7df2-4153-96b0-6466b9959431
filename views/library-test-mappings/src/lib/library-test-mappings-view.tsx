import { activeLibraryTestController } from '@controllers/library-test';
import { Datatable } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedLibraryTestMappingsFilterModel } from '@models/library-test';
import { getLibraryTestMappingsColumns } from '../constants/library-test-mappings-table.constants';

export const LibraryTestMappingsView = observer((): React.JSX.Element => {
    const {
        totalMonitorControls,
        testTemplateControls,
        isLoadingTestTemplateControls,
        loadTestTemplateControls,
    } = activeLibraryTestController;

    const { filters } = sharedLibraryTestMappingsFilterModel;

    return (
        <div data-testid="LibraryTestMappingsView" data-id="H5of_Nj4">
            <Datatable
                isLoading={isLoadingTestTemplateControls}
                tableId="library-test-datatable"
                data-id="library-test-datatable"
                data-testid="LibraryTestDataTable"
                columns={getLibraryTestMappingsColumns()}
                total={totalMonitorControls}
                data={testTemplateControls}
                filterProps={filters}
                density="spacious"
                tableSearchProps={{ hideSearch: true }}
                emptyStateProps={{
                    illustrationName: 'Warning',
                    title: t`Test Mappings`,
                    description: t`No controls were found.`,
                }}
                onFetchData={loadTestTemplateControls}
            />
        </div>
    );
});
