import { useCallback } from 'react';
import type { VendorSettingsRequestDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import { sharedVendorsSettingsSsoFormModel } from '../models/vendors-settings-sso-form.model';

interface FormRefHandle extends HTMLFormElement {
    submitForm: () => void;
    clearForm: () => void;
}

interface Props {
    onSubmit: (data: VendorSettingsRequestDto) => void;
    isUpdating: boolean;
    formRef: React.RefObject<FormRefHandle>;
}

export const VendorsSettingsSsoEditComponent = observer(
    ({ onSubmit, isUpdating, formRef }: Props): React.JSX.Element => {
        const handleSubmit = useCallback(
            (values: FormValues) => {
                onSubmit({
                    isSsoIntegration: values.isSsoIntegration as boolean,
                });
            },
            [onSubmit],
        );

        const formSchema = sharedVendorsSettingsSsoFormModel.getSchema();

        return (
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="vendor-sso-settings-form"
                schema={formSchema}
                isReadOnly={isUpdating}
                data-testid="VendorsSettingsSsoEditComponent"
                data-id="sso-settings-edit-form"
                onSubmit={handleSubmit}
            />
        );
    },
);
