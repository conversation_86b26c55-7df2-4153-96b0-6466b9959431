import { isEmpty } from 'lodash-es';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import { DEFAULT_END_USER_CONTROL } from '../constants/vendors-security-reviews-soc.constants';

export const SocEndUserControlsField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { control } = useFormContext();
    const { fields, append, remove } = useFieldArray({
        control,
        name,
    });

    const { setValue } = useFormContext();

    const addControl = () => {
        append({ ...DEFAULT_END_USER_CONTROL });
        setValue(`${name}.allControlsInPlace`, '', {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
        });
    };

    const removeControl = (index: number) => {
        remove(index);
    };

    return (
        <FormField
            formId={formId}
            name={name}
            label="End User Controls"
            data-testid="SocEndUserControlsField"
            data-id={dataId}
            renderInput={() => (
                <Stack gap="lg" direction="column" data-id={`${dataId}-inputs`}>
                    {fields.map((field, index) => (
                        <div
                            key={field.id}
                            data-id={`${dataId}-control-${index}`}
                        >
                            <Stack
                                gap="md"
                                align="start"
                                data-id={`${dataId}-control-row-${index}`}
                            >
                                <UniversalFormField
                                    __fromCustomRender
                                    name={`${name}[${index}].name`}
                                    formId={formId}
                                    data-id={`${dataId}-name-field`}
                                />

                                {!isEmpty(fields) && (
                                    <Box pt="6x">
                                        <Button
                                            isIconOnly
                                            label="Remove control"
                                            startIconName="Trash"
                                            level="tertiary"
                                            colorScheme="danger"
                                            type="button"
                                            onClick={() => {
                                                removeControl(index);
                                            }}
                                        />
                                    </Box>
                                )}
                            </Stack>

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}[${index}].inPlace`}
                                formId={formId}
                                data-id={`${dataId}-in-place-field`}
                            />
                        </div>
                    ))}

                    <Stack direction="row" align="start">
                        <Button
                            label="Add control"
                            level="tertiary"
                            type="button"
                            onClick={addControl}
                        />
                    </Stack>
                </Stack>
            )}
        />
    );
};
