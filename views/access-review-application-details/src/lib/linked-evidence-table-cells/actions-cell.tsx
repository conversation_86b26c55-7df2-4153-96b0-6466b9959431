import {
    activeAccessReviewApplicationDetailsController,
    sharedAccessReviewApplicationEvidenceController,
} from '@controllers/access-reviews';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Tooltip } from '@cosmos/components/tooltip';
import type { AccessReviewPeriodApplicationEvidenceResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const ActionsCell = observer(
    ({
        row: { original },
    }: {
        row: { original: AccessReviewPeriodApplicationEvidenceResponseDto };
    }): React.JSX.Element => {
        const {
            isApplicationDisabled,
            downloadLinkedEvidence,
            applicationDetails,
        } = activeAccessReviewApplicationDetailsController;

        const { removeEvidence } =
            sharedAccessReviewApplicationEvidenceController;

        const { hasLimitedAccess } = sharedFeatureAccessModel;

        const handleOnclickDownload = () => {
            downloadLinkedEvidence({
                periodId: applicationDetails?.reviewPeriod.id,
                reviewAppId: applicationDetails?.application.id,
                evidenceId: original.id,
            });
        };

        const handleDeleteEvidence = () => {
            removeEvidence(original.id);
        };

        return (
            <Stack gap="2x" data-id="Mguy7I0Y" justify="end">
                <Tooltip isInteractive text={t`Download evidence`}>
                    <Button
                        isIconOnly
                        size="sm"
                        level="tertiary"
                        label={t`Download evidence`}
                        startIconName="Download"
                        colorScheme="neutral"
                        onClick={handleOnclickDownload}
                    />
                </Tooltip>
                {!isApplicationDisabled && !hasLimitedAccess && (
                    <Tooltip isInteractive text={t`Delete evidence`}>
                        <Button
                            isIconOnly
                            level="tertiary"
                            size="sm"
                            label={t`Delete evidence`}
                            startIconName="Trash"
                            colorScheme="danger"
                            onClick={handleDeleteEvidence}
                        />
                    </Tooltip>
                )}
            </Stack>
        );
    },
);
