import { isNil } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { providers } from '@globals/providers';
import { sharedDatatable } from '@models/access-review';
import type { ApplicationDataRowCellProps } from '../types/access-review-cell.types';

export const AccessReviewApplicationSourceCell = ({
    row: { original },
}: ApplicationDataRowCellProps): React.JSX.Element => {
    const { source, clientType } = original;
    const sourceLabel = sharedDatatable.getLabels(source);

    if (source === 'PARTNER_CONNECTION') {
        const provider = Object.values(providers).find(
            (p) => p.id === clientType,
        );
        const providerName = provider?.name ?? '';

        if (isNil(provider)) {
            return <Text>{sourceLabel}</Text>;
        }

        return <Text>{t`${providerName} connection`}</Text>;
    }

    return (
        <Text
            data-testid="AccessReviewApplicationSourceCell"
            data-id="qksTqi2z"
        >
            {sourceLabel}
        </Text>
    );
};
