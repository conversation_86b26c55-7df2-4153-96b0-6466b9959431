import { sharedVendorsProfileSecurityReviewsController } from '@controllers/vendors';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { VendorSecurityReviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { useNavigate } from '@remix-run/react';

export const useVendorsSecurityReviewsTableActions =
    (): DatatableProps<VendorSecurityReviewResponseDto>['tableActions'] => {
        const navigate = useNavigate();
        const { createNewSecurityReview } =
            sharedVendorsProfileSecurityReviewsController;

        return [
            {
                actionType: 'dropdown',
                id: 'vendors-security-reviews-add-security-review',
                typeProps: {
                    isIconOnly: false,
                    label: t`Create review`,
                    items: [
                        {
                            id: 'add-security-review',
                            label: t`Security review`,
                            type: 'item',
                            description: t`Send questionnaires, add observations, and track your review decision.`,
                            onSelect: () => {
                                createNewSecurityReview(navigate);
                            },
                        },
                        {
                            id: 'add-soc-report-review',
                            label: t`SOC report review`,
                            type: 'item',
                            description: t`Please finalize or delete your in-progress review to start a new SOC report review.`,
                            onSelect: () => {
                                // TODO: Implement SOC report review functionality https://drata.atlassian.net/browse/ENG-68450
                            },
                        },
                        {
                            id: 'add-upload-review-report',
                            label: t`Upload review report`,
                            type: 'item',
                            description: t`Upload a completed compliance review report.`,
                            onSelect: () => {
                                // TODO: Implement upload review report functionality https://drata.atlassian.net/browse/ENG-68449
                            },
                        },
                    ],
                    level: 'primary',
                },
            },
        ];
    };

export const getVendorsSecurityReviewsStatusLabel = (
    status: NonNullable<VendorSecurityReviewResponseDto['status']>,
): string => {
    switch (status) {
        case 'NOT_YET_STARTED': {
            return t`Not started yet`;
        }
        case 'IN_PROGRESS': {
            return t`In progress`;
        }
        case 'COMPLETED': {
            return t`Completed`;
        }
        case 'NOT_REQUIRED':
        default: {
            return t`Not required`;
        }
    }
};
