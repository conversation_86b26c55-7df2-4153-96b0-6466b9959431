import { observer } from '@globals/mobx';
import { TaskModalFormMappingControl } from './task-modal-form-mapping-control';
import { TaskModalFormMappingHeader } from './task-modal-form-mapping-header';
import { TaskModalFormMappingRisk } from './task-modal-form-mapping-risk';

export const TaskModalFormMapping = observer(() => {
    return (
        <>
            <TaskModalFormMappingHeader />
            <TaskModalFormMappingControl />
            <TaskModalFormMappingRisk />
        </>
    );
});
