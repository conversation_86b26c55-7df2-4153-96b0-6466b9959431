import React from 'react';
import { Avatar } from '@cosmos/components/avatar';
import type { AssetResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import { sharedAssetFormModel } from '@models/assets';
import { Form, type FormValues } from '@ui/forms';

interface AssetsDetailsOwnerFormComponentProps {
    formRef: React.RefObject<HTMLFormElement>;
    state: AssetResponseDto | null;
    onSubmit: (values: FormValues) => void;
}

export const AssetsDetailsOwnerFormComponent = observer(
    ({
        formRef,
        state,
        onSubmit,
    }: AssetsDetailsOwnerFormComponentProps): React.JSX.Element => {
        const schema = {
            ownerId: sharedAssetFormModel.ownerFieldConfig(
                state?.owner.id
                    ? {
                          id: state.owner.id.toString(),
                          label: getFullName(
                              state.owner.firstName,
                              state.owner.lastName,
                          ),
                          value: String(state.owner.id),
                          startSlot: React.createElement(Avatar, {
                              fallbackText: getInitials(
                                  getFullName(
                                      state.owner.firstName,
                                      state.owner.lastName,
                                  ),
                              ),
                              imgSrc: state.owner.avatarUrl ?? undefined,
                              imgAlt: getFullName(
                                  state.owner.firstName,
                                  state.owner.lastName,
                              ),
                              size: 'sm',
                          }),
                      }
                    : undefined,
            ),
        };

        return (
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="assets-owner-form"
                data-id="assets-owner-form"
                schema={schema}
                data-testid="AssetsDetailsOwnerFormComponent"
                onSubmit={onSubmit}
            />
        );
    },
);
