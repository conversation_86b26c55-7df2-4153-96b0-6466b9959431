import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { Avatar } from '@cosmos/components/avatar';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { dimension64x } from '@cosmos/constants/tokens';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getInitials } from '@helpers/formatters';
import { AuditHubAuditDetailsRequestTable } from './audit-hub-auditor-client-audit-details-request-table';
import { AuditHubAuditorClientAuditDonutDetailsView } from './audit-hub-auditor-client-audit-donut-details-view';

export const AuditorClientAuditDetailsView = observer((): React.JSX.Element => {
    const { auditorsData } = sharedAuditHubAuditorClientAuditController;

    return (
        <Grid gap="4x" p="md" data-id="3HvhLIMM">
            <Grid
                gap="4x"
                data-testid="AuditHubAuditorClientAuditDetailsViewCards"
                columns="1fr 1fr"
            >
                <Card
                    title={t`Assigned auditors`}
                    cardHeight={dimension64x}
                    body={
                        <StackedList id="audit-hub-auditor-client-details">
                            {auditorsData.map((auditor) => {
                                const avatarText = getInitials(auditor.content);

                                return (
                                    <StackedListItem
                                        key={auditor.id}
                                        data-id="d0HYUXqE"
                                        primaryColumn={
                                            <Stack
                                                align="center"
                                                gap="md"
                                                direction="row"
                                            >
                                                <Avatar
                                                    size="sm"
                                                    imgAlt={avatarText.toUpperCase()}
                                                    fallbackText={avatarText.toUpperCase()}
                                                    imgSrc={
                                                        auditor.avatarUrl ||
                                                        undefined
                                                    }
                                                />
                                                {auditor.content}
                                            </Stack>
                                        }
                                    />
                                );
                            })}
                        </StackedList>
                    }
                />
                <Card
                    title={t`Request summary`}
                    cardHeight={dimension64x}
                    tooltipText={t`New: Requests that your organization must prepare. Prepared: Requests that are ready for the auditor. Completed: Requests that are done.`}
                    body={<AuditHubAuditorClientAuditDonutDetailsView />}
                />
            </Grid>
            <AuditHubAuditDetailsRequestTable />
        </Grid>
    );
});
