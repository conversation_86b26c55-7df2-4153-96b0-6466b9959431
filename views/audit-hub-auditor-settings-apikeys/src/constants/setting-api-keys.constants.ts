import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { addMonths, convertToISO8601String } from '@helpers/date-time';

export const getOptionCustom = (): ListBoxItemData => ({
    id: 'Custom',
    label: t`Custom`,
    value: 'Custom',
});

export const getOption12Months = (): ListBoxItemData => ({
    id: '12-months',
    label: t`12 months`,
    value: '12-months',
});

export const getExpirationOptions = (): ListBoxItemData[] => [
    getOption12Months(),
    getOptionCustom(),
];

export const getDateUnavailableText = (): string =>
    t`Date cannot be in the past`;
export const DATES_UNAVAILABLE = (date: TDateISODate): boolean => {
    const selectedDate = new Date(date);
    const today = new Date();

    today.setHours(0, 0, 0, 0); // Reset time to start of day

    return selectedDate < today;
};
export const DEFAULT_EXPIRATION_DATE = convertToISO8601String(
    addMonths(new Date(), 12),
);
export const calculateRenewalDate = (expirationDate: string | Date): string => {
    return convertToISO8601String(addMonths(expirationDate, 12));
};
