import { Modal } from '@cosmos/components/modal';
import { Form } from '../../form';
import { useFormSubmit } from '../../hooks/use-form-submit.hook';
import type { FormSchema } from '../../types/form-schema.type';
import type { FormValues } from '../../types/form-values.type';

export interface ModalFormProps {
    schema: FormSchema;
}

const handleSubmit = (values: FormValues) => {
    // eslint-disable-next-line no-console -- this component is only used in storybook
    console.log('handleSubmit', values);
};

export const ModalForm = ({ schema }: ModalFormProps): React.JSX.Element => {
    const { formRef, triggerSubmit, getFormValues } = useFormSubmit();

    return (
        <>
            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="modal-form"
                    ref={formRef}
                    schema={schema}
                    formId="modal-form"
                    onSubmit={handleSubmit}
                />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: 'Cancel',
                        level: 'tertiary',
                        onClick: () => {
                            alert('Cancel clicked');
                        },
                    },
                    {
                        label: 'Get Values',
                        level: 'secondary',
                        onClick: () => {
                            const currentValues = getFormValues();

                            // eslint-disable-next-line no-console -- this component is only used in storybook
                            console.log('Current form values:', currentValues);
                            alert(
                                `Current values: ${JSON.stringify(currentValues, null, 2)}`,
                            );
                        },
                    },
                    {
                        label: 'Confirm',
                        level: 'primary',
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                // not handling errors here
                                console.error('Failed to submit form');
                            });
                        },
                        type: 'submit',
                    },
                ]}
            />
        </>
    );
};
