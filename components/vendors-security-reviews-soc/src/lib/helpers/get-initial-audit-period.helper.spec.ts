import { describe, expect, test } from 'vitest';
import type { VendorReviewResponseDto } from '@globals/api-sdk/types';
import { getInitialAuditPeriod } from './get-initial-audit-period.helper';

describe('getInitialAuditPeriod', () => {
    describe('when vendorReview is null or undefined', () => {
        test('should return null values when vendorReview is null', () => {
            const result = getInitialAuditPeriod(true, null);

            expect(result).toStrictEqual({ start: null, end: null });
        });

        test('should return null values when vendorReview is undefined', () => {
            const result = getInitialAuditPeriod(false, undefined);

            expect(result).toStrictEqual({ start: null, end: null });
        });
    });

    describe('when isSocReportType1 is true', () => {
        test('should return type1 dates when both dates are valid', () => {
            const vendorReview = {
                socType1StartDate: '2024-01-01',
                socType1EndDate: '2024-12-31',
                socType2StartDate: '2023-01-01',
                socType2EndDate: '2023-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(true, vendorReview);

            expect(result).toStrictEqual({
                start: '2024-01-01',
                end: '2024-12-31',
            });
        });

        test('should return null for invalid type1 dates', () => {
            const vendorReview = {
                socType1StartDate: 'invalid-date',
                socType1EndDate: '2024-13-45', // invalid date
                socType2StartDate: '2023-01-01',
                socType2EndDate: '2023-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(true, vendorReview);

            expect(result).toStrictEqual({
                start: null,
                end: null,
            });
        });

        test('should handle missing type1 dates', () => {
            const vendorReview = {
                socType2StartDate: '2023-01-01',
                socType2EndDate: '2023-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(true, vendorReview);

            expect(result).toStrictEqual({
                start: null,
                end: null,
            });
        });

        test('should handle partial type1 dates', () => {
            const vendorReview = {
                socType1StartDate: '2024-01-01',
                // socType1EndDate is missing
                socType2StartDate: '2023-01-01',
                socType2EndDate: '2023-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(true, vendorReview);

            expect(result).toStrictEqual({
                start: '2024-01-01',
                end: null,
            });
        });
    });

    describe('when isSocReportType1 is false', () => {
        test('should return type2 dates when both dates are valid', () => {
            const vendorReview = {
                socType1StartDate: '2024-01-01',
                socType1EndDate: '2024-12-31',
                socType2StartDate: '2023-01-01',
                socType2EndDate: '2023-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(false, vendorReview);

            expect(result).toStrictEqual({
                start: '2023-01-01',
                end: '2023-12-31',
            });
        });

        test('should return null for invalid type2 dates', () => {
            const vendorReview = {
                socType1StartDate: '2024-01-01',
                socType1EndDate: '2024-12-31',
                socType2StartDate: 'invalid-date',
                socType2EndDate: '2023-13-45', // invalid date
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(false, vendorReview);

            expect(result).toStrictEqual({
                start: null,
                end: null,
            });
        });

        test('should handle missing type2 dates', () => {
            const vendorReview = {
                socType1StartDate: '2024-01-01',
                socType1EndDate: '2024-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(false, vendorReview);

            expect(result).toStrictEqual({
                start: null,
                end: null,
            });
        });

        test('should handle partial type2 dates', () => {
            const vendorReview = {
                socType1StartDate: '2024-01-01',
                socType1EndDate: '2024-12-31',
                socType2StartDate: '2023-01-01',
                // socType2EndDate is missing
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(false, vendorReview);

            expect(result).toStrictEqual({
                start: '2023-01-01',
                end: null,
            });
        });
    });

    describe('edge cases', () => {
        test('should handle leap year dates correctly', () => {
            const vendorReview = {
                socType1StartDate: '2024-02-29', // valid leap year date
                socType1EndDate: '2024-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(true, vendorReview);

            expect(result).toStrictEqual({
                start: '2024-02-29',
                end: '2024-12-31',
            });
        });

        test('should reject invalid leap year dates', () => {
            const vendorReview = {
                socType1StartDate: '2023-02-29', // invalid leap year date
                socType1EndDate: '2023-12-31',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(true, vendorReview);

            expect(result).toStrictEqual({
                start: null,
                end: '2023-12-31',
            });
        });

        test('should handle empty string dates', () => {
            const vendorReview = {
                socType1StartDate: '',
                socType1EndDate: '',
            } as VendorReviewResponseDto;

            const result = getInitialAuditPeriod(true, vendorReview);

            expect(result).toStrictEqual({
                start: null,
                end: null,
            });
        });
    });
});
