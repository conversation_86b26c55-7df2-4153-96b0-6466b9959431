import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { VendorsSettingsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

interface Props {
    vendorSettings: VendorsSettingsResponseDto | null;
}

export const VendorsSettingsRecurringReviewsReadOnlyComponent = observer(
    ({ vendorSettings }: Props): React.JSX.Element => {
        const defaultReviewPeriod = vendorSettings?.defaultReviewPeriod || 15;

        return (
            <Stack
                direction="column"
                gap="lg"
                data-testid="VendorsSettingsRecurringReviewsReadOnlyComponent"
                data-id="recurring-reviews-read-only"
            >
                <Stack direction="column" gap="sm">
                    <Text allowBold type="body" size="100">
                        {t`Recurring reviews and questionnaire timeframe`}
                    </Text>
                </Stack>

                <Text type="body" size="200" colorScheme="faded">
                    {t`${defaultReviewPeriod} days before the deadline`}
                </Text>
            </Stack>
        );
    },
);
