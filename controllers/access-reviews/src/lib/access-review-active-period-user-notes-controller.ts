import { isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerPostReviewPeriodNoteMutation,
    accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    accessReviewPeriodControllerGetReviewApplicationUserDetailsOptions,
    accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    accessReviewUserControllerDownloadUserNoteFileOptions,
} from '@globals/api-sdk/queries';
import type { NoteResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class AccessReviewActiveOverviewNotesController {
    activeNotesQuery = new ObservedQuery(
        accessReviewPeriodControllerGetReviewApplicationUserDetailsOptions,
    );
    activeNoteCreateMutation = new ObservedMutation(
        accessReviewApplicationControllerPostReviewPeriodNoteMutation,
    );
    activeNoteUpdateMutation = new ObservedMutation(
        accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    );
    activeNoteDeleteMutation = new ObservedMutation(
        accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    );
    activeDownloadAttachmentQuery = new ObservedQuery(
        accessReviewUserControllerDownloadUserNoteFileOptions,
    );

    periodId: string | null = null;
    reviewAppId: string | null = null;
    userId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get list(): NoteResponseDto[] {
        const mappedNotes = (this.activeNotesQuery.data?.notes ?? []).map(
            (note) => {
                return {
                    ...note,
                    comment: note.message || '',
                    createdAt: note.sentAt || '',
                    updatedAt: note.sentAt || '',
                    owner: {
                        id: note.ownerId,
                        firstName: note.ownerName,
                        lastName: '',
                        avatarUrl: note.ownerAvatar,
                    },
                    noteFiles: note.files,
                };
            },
        );

        return mappedNotes.map((note) => note) as unknown as NoteResponseDto[];
    }

    get total(): number {
        return this.activeNotesQuery.data?.notes.length ?? 0;
    }

    get isLoading(): boolean {
        return this.activeNotesQuery.isLoading;
    }

    loadNotes = ({
        periodId,
        reviewAppId,
        userId,
    }: {
        periodId?: string;
        reviewAppId?: string;
        userId?: string;
    }): void => {
        if (isNil(periodId) || isNil(reviewAppId) || isNil(userId)) {
            throw new Error(t`Period ID and App ID params are required`);
        }
        this.periodId = periodId;
        this.reviewAppId = reviewAppId;
        this.userId = userId;

        this.activeNotesQuery.load({
            path: {
                periodId: Number(periodId),
                reviewAppId: Number(reviewAppId),
                userId: Number(userId),
            },
        });
    };

    createNote = ({
        comment,
        files,
    }: {
        comment: string;
        files: (File | Blob)[];
    }): void => {
        if (!this.periodId || !this.reviewAppId || !this.userId) {
            throw new Error(t`Event ID is not set`);
        }
        const timestamp = new Date().toISOString();

        this.activeNoteCreateMutation
            .mutateAsync({
                path: {
                    periodId: Number(this.periodId),
                    reviewAppId: Number(this.reviewAppId),
                    userId: Number(this.userId),
                },
                body: {
                    comment,
                    'files[]': files,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeNotesQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-event-note-success`,
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-event-note-error`,
                    props: {
                        title: t`Unable to create note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    updateNote = (
        noteId: string,
        {
            comment,
            files,
        }: {
            comment: string;
            files: (File | Blob)[];
        },
    ): void => {
        const timestamp = new Date().toISOString();

        this.activeNoteUpdateMutation
            .mutateAsync({
                path: {
                    noteId,
                    periodId: Number(this.periodId),
                    reviewAppId: Number(this.reviewAppId),
                },
                body: {
                    comment,
                    'files[]': files,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeNotesQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-event-note-success`,
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-event-note-error`,
                    props: {
                        title: t`Unable to update note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    deleteNote = (noteId: string): void => {
        openConfirmationModal({
            title: t`Delete Note`,
            body: t`Are you sure you want to delete this note?`,
            confirmText: t`Yes, delete note`,
            cancelText: t`No, go back`,
            type: 'danger',
            onConfirm: () => {
                const timestamp = new Date().toISOString();

                this.activeNoteDeleteMutation
                    .mutateAsync({
                        path: {
                            noteId,
                            periodId: Number(this.periodId),
                            reviewAppId: Number(this.reviewAppId),
                        },
                    })
                    .then(() => {
                        runInAction(() => {
                            this.activeNotesQuery.invalidate();
                        });
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch((error: Error) => {
                        const title =
                            (error as { statusCode?: number }).statusCode ===
                            403
                                ? t`Only the same owner can delete a note`
                                : t`Unable to delete note at the moment`;

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-error`,
                            props: {
                                title,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    downloadNoteAttachment = (noteFileId: string, noteId: string): void => {
        this.activeDownloadAttachmentQuery.load({
            path: {
                periodId: Number(this.periodId),
                reviewAppId: Number(this.reviewAppId),
                userId: Number(this.userId),
            },
            query: {
                noteId,
                fileId: noteFileId,
            },
        });
        when(() => !this.activeDownloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.activeDownloadAttachmentQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedAccessReviewActivePeriodUserNotesController =
    new AccessReviewActiveOverviewNotesController();
