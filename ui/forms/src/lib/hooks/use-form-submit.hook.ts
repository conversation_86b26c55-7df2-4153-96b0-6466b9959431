import { useCallback, useMemo, useRef } from 'react';
import type { FormValues } from '../types/form-values.type';

interface FormRefHandle extends HTMLFormElement {
    submitForm: () => Promise<boolean>;
    clearForm: () => void;
    getValues: () => FormValues;
}

export const useFormSubmit = (): {
    formRef: React.RefObject<FormRefHandle>;
    triggerSubmit: () => Promise<boolean>;
    triggerClearForm: () => void;
    getFormValues: () => FormValues | null;
} => {
    const formRef = useRef<FormRefHandle>(null);

    const triggerSubmit = useCallback(async (): Promise<boolean> => {
        if (!formRef.current?.submitForm) {
            return Promise.resolve(false);
        }

        // Still return the promise, allowing callers to handle errors if needed
        return formRef.current.submitForm();
    }, []);

    const triggerClearForm = useCallback(() => {
        const form = formRef.current;

        if (form) {
            form.clearForm();
        }
    }, []);

    const getFormValues = useCallback((): FormValues | null => {
        const form = formRef.current;

        if (form?.getValues) {
            return form.getValues();
        }

        return null;
    }, []);

    return useMemo(
        () => ({
            formRef,
            triggerSubmit,
            triggerClearForm,
            getFormValues,
        }),
        [triggerClearForm, triggerSubmit, getFormValues],
    );
};
