import { sharedControlsDetailsStatsController } from '@controllers/controls';
import {
    sharedMonitorsController,
    sharedMonitorsInfiniteController,
} from '@controllers/monitors';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerBulkPutControlTestsMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringControlTestComparisonController } from './monitoring-control-test-comparsion-controller';

class MonitoringMapTestsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    mapTestsMutation = new ObservedMutation(
        grcControllerBulkPutControlTestsMutation,
    );

    get isMapping(): boolean {
        return this.mapTestsMutation.isPending;
    }

    get hasError(): boolean {
        return this.mapTestsMutation.hasError;
    }

    mapTestsToControl = (controlId: number, testIds: number[]): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        this.mapTestsMutation.mutate({
            path: { xProductId: workspaceId as number },
            body: { testIds, controlIds: [controlId] },
        });

        when(
            () => !this.isMapping,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: 'map-tests-error',
                        props: {
                            title: t`Failed to map tests`,
                            description: t`An error occurred while mapping the tests. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    sharedMonitoringControlTestComparisonController.invalidate();
                    sharedControlsDetailsStatsController.invalidate();
                    sharedMonitorsController.invalidate();
                    sharedMonitorsInfiniteController.clearSelectedMonitors();

                    snackbarController.addSnackbar({
                        id: 'map-tests-success',
                        props: {
                            title: t`Tests mapped`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };
}

export const sharedMonitoringMapTestsMutationController =
    new MonitoringMapTestsMutationController();
