{"compilerOptions": {"paths": {"@app/types": ["./apps/drata/app/types.ts"], "@assets/*": ["./assets/*"], "@components/access-review": ["./components/access-review/src/index.ts"], "@components/access-review-create-period": ["./components/access-review-create-period/src/index.ts"], "@components/access-review-details": ["./components/access-review-details/src/index.ts"], "@components/access-review-empty-state": ["./components/access-review-empty-state/src/index.ts"], "@components/access-review-personnel": ["./components/access-review-personnel/src/index.ts"], "@components/add-asset-form": ["./components/add-asset-form/src/index.ts"], "@components/api-keys-details": ["./components/api-keys-details/src/index.ts"], "@components/app-datatable": ["./components/app-datatable/src/index.ts"], "@components/app-header": ["./components/app-header/src/index.ts"], "@components/app-help-content": ["./components/app-help-content/src/index.ts"], "@components/assets": ["./components/assets/src/index.ts"], "@components/assets-details": ["./components/assets-details/src/index.ts"], "@components/audits": ["./components/audits/src/index.ts"], "@components/compliance-check-exclusion-details-modal": ["./components/compliance-check-exclusion-details-modal/src/index.ts"], "@components/connections": ["./components/connections/src/index.ts"], "@components/control-details-accordion": ["./components/control-details-accordion/src/index.ts"], "@components/controls": ["./components/controls/src/index.ts"], "@components/controls-manage-evidences": ["./components/controls-manage-evidences/src/index.ts"], "@components/create-connection-wizard": ["./components/create-connection-wizard/src/index.ts"], "@components/create-risk-wizard": ["./components/create-risk-wizard/src/index.ts"], "@components/create-ticket-wizard": ["./components/create-ticket-wizard/src/index.ts"], "@components/dashboard": ["./components/dashboard/src/index.ts"], "@components/document-viewer": ["./components/document-viewer/src/index.ts"], "@components/droppable": ["./components/droppable/src/index.ts"], "@components/early-access-dropdown": ["./components/early-access-dropdown/src/index.ts"], "@components/events": ["./components/events/src/index.ts"], "@components/events-detail-card": ["./components/events-detail-card/src/index.ts"], "@components/evidence-library": ["./components/evidence-library/src/index.ts"], "@components/flatfile-portal": ["./components/flatfile-portal/src/index.ts"], "@components/frameworks": ["./components/frameworks/src/index.ts"], "@components/governance-access-review": ["./components/governance-access-review/src/index.ts"], "@components/language-picker": ["./components/language-picker/src/index.ts"], "@components/library-test-active-tests-card": ["./components/library-test-active-tests-card/src/index.ts"], "@components/library-test-details-card": ["./components/library-test-details-card/src/index.ts"], "@components/library-test-logic-card": ["./components/library-test-logic-card/src/index.ts"], "@components/library-test-manage-active-test": ["./components/library-test-manage-active-test/src/index.ts"], "@components/library-test-mappings-card": ["./components/library-test-mappings-card/src/index.ts"], "@components/login": ["./components/login/src/index.ts"], "@components/main-app-domains-nav": ["./components/main-app-domains-nav/src/index.ts"], "@components/main-app-topics-nav": ["./components/main-app-topics-nav/src/index.ts"], "@components/map-controls-step": ["./components/map-controls-step/src/index.ts"], "@components/map-tests-modal": ["./components/map-tests-modal/src/index.ts"], "@components/mapped-controls-list": ["./components/mapped-controls-list/src/index.ts"], "@components/mapped-object-box": ["./components/mapped-object-box/src/index.ts"], "@components/monitoring": ["./components/monitoring/src/index.ts"], "@components/monitoring-details-controls": ["./components/monitoring-details-controls/src/index.ts"], "@components/monitoring-details-exclusions": ["./components/monitoring-details-exclusions/src/index.ts"], "@components/monitoring-details-findings": ["./components/monitoring-details-findings/src/index.ts"], "@components/monitoring-details-overview": ["./components/monitoring-details-overview/src/index.ts"], "@components/monitoring-personnel-exclusions": ["./components/monitoring-personnel-exclusions/src/index.ts"], "@components/monitoring-pipeline": ["./components/monitoring-pipeline/src/index.ts"], "@components/object-picker-modal": ["./components/object-picker-modal/src/index.ts"], "@components/out-of-scope-modal": ["./components/out-of-scope-modal/src/index.ts"], "@components/policies": ["./components/policies/src/index.ts"], "@components/policies-builder-empty-state": ["./components/policies-builder-empty-state/src/index.ts"], "@components/policies-builder-history": ["./components/policies-builder-history/src/index.ts"], "@components/policies-editor": ["./components/policies-editor/src/index.ts"], "@components/quick-start": ["./components/quick-start/src/index.ts"], "@components/requirements": ["./components/requirements/src/index.ts"], "@components/risk-details-accordion": ["./components/risk-details-accordion/src/index.ts"], "@components/risk-register": ["./components/risk-register/src/index.ts"], "@components/risk-vulnerabilities": ["./components/risk-vulnerabilities/src/index.ts"], "@components/risks": ["./components/risks/src/index.ts"], "@components/role-administration-add-user-modal": ["./components/role-administration-add-user-modal/src/index.ts"], "@components/tasks-link": ["./components/tasks-link/src/index.ts"], "@components/text-break-line": ["./components/text-break-line/src/index.ts"], "@components/theme": ["./components/theme/src/index.ts"], "@components/utilities": ["./components/utilities/src/index.ts"], "@components/vendor-hub-questionnaire": ["./components/vendor-hub-questionnaire/src/index.ts"], "@components/vendor-questionnaires": ["./components/vendor-questionnaires/src/index.ts"], "@components/vendors-current": ["./components/vendors-current/src/index.ts"], "@components/vendors-current-add-vendor": ["./components/vendors-current-add-vendor/src/index.ts"], "@components/vendors-current-archive-vendor": ["./components/vendors-current-archive-vendor/src/index.ts"], "@components/vendors-current-bulk-add-vendors": ["./components/vendors-current-bulk-add-vendors/src/index.ts"], "@components/vendors-current-delete-vendor": ["./components/vendors-current-delete-vendor/src/index.ts"], "@components/vendors-profile": ["./components/vendors-profile/src/index.ts"], "@components/vendors-prospective": ["./components/vendors-prospective/src/index.ts"], "@components/vendors-prospective-add": ["./components/vendors-prospective-add/src/index.ts"], "@components/vendors-recurring-reviews": ["./components/vendors-recurring-reviews/src/index.ts"], "@components/vendors-risks": ["./components/vendors-risks/src/index.ts"], "@components/vendors-risks-overview-panel": ["./components/vendors-risks-overview-panel/src/index.ts"], "@components/vendors-security-reviews": ["./components/vendors-security-reviews/src/index.ts"], "@components/vendors-security-reviews-soc": ["./components/vendors-security-reviews-soc/src/index.ts"], "@components/vendors-settings": ["./components/vendors-settings/src/index.ts"], "@components/view-edit-card": ["./components/view-edit-card/src/index.ts"], "@configs/*": ["./configs/*"], "@controllers/access-reviews": ["./controllers/access-reviews/src/index.ts"], "@controllers/access-reviews-applications": ["./controllers/access-reviews-applications/src/index.ts"], "@controllers/add-test-to-program": ["./controllers/add-test-to-program/src/index.ts"], "@controllers/ai-execution-group": ["./controllers/ai-execution-group/src/index.ts"], "@controllers/api-keys": ["./controllers/api-keys/src/index.ts"], "@controllers/api-keys-permissions": ["./controllers/api-keys-permissions/src/index.ts"], "@controllers/asset": ["./controllers/asset/src/index.ts"], "@controllers/assets": ["./controllers/assets/src/index.ts"], "@controllers/audit-hub": ["./controllers/audit-hub/src/index.ts"], "@controllers/audit-hub-auditor-client-actions": ["./controllers/audit-hub-auditor-client-actions/src/index.ts"], "@controllers/audit-hub-auditor-client-audit": ["./controllers/audit-hub-auditor-client-audit/src/index.ts"], "@controllers/audit-hub-auditor-client-evidence-package-table-controller": ["./controllers/audit-hub-auditor-client-evidence-package-table-controller/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-assets": ["./controllers/audit-hub-auditor-client-methods-assets/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-connections": ["./controllers/audit-hub-auditor-client-methods-connections/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-control-mapping": ["./controllers/audit-hub-auditor-client-methods-control-mapping/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-evidence-library": ["./controllers/audit-hub-auditor-client-methods-evidence-library/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-human-resources": ["./controllers/audit-hub-auditor-client-methods-human-resources/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-infrastructure": ["./controllers/audit-hub-auditor-client-methods-infrastructure/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-request-package": ["./controllers/audit-hub-auditor-client-methods-request-package/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-vendors": ["./controllers/audit-hub-auditor-client-methods-vendors/src/index.ts"], "@controllers/audit-hub-auditor-client-methods-version-control": ["./controllers/audit-hub-auditor-client-methods-version-control/src/index.ts"], "@controllers/audit-hub-auditor-polling-package-status": ["./controllers/audit-hub-auditor-polling-package-status/src/index.ts"], "@controllers/audit-hub-auditor-refresh-package": ["./controllers/audit-hub-auditor-refresh-package/src/index.ts"], "@controllers/audit-hub-auditor-validate-personnel": ["./controllers/audit-hub-auditor-validate-personnel/src/index.ts"], "@controllers/audit-hub-control-details": ["./controllers/audit-hub-control-details/src/index.ts"], "@controllers/audit-hub-evidence-viewer": ["./controllers/audit-hub-evidence-viewer/src/index.ts"], "@controllers/auditor": ["./controllers/auditor/src/index.ts"], "@controllers/audits": ["./controllers/audits/src/index.ts"], "@controllers/auth": ["./controllers/auth/src/index.ts"], "@controllers/background-checks": ["./controllers/background-checks/src/index.ts"], "@controllers/cloud-storage": ["./controllers/cloud-storage/src/index.ts"], "@controllers/companies": ["./controllers/companies/src/index.ts"], "@controllers/compliance-as-code-list-repositories": ["./controllers/compliance-as-code-list-repositories/src/index.ts"], "@controllers/compliance-as-code-repository": ["./controllers/compliance-as-code-repository/src/index.ts"], "@controllers/compliance-check-exclusions": ["./controllers/compliance-check-exclusions/src/index.ts"], "@controllers/connection-manage-accounts-access-reviews": ["./controllers/connection-manage-accounts-access-reviews/src/index.ts"], "@controllers/connection-manage-accounts-observability": ["./controllers/connection-manage-accounts-observability/src/index.ts"], "@controllers/connections": ["./controllers/connections/src/index.ts"], "@controllers/connections-manage-accounts-infrastructure": ["./controllers/connections-manage-accounts-infrastructure/src/index.ts"], "@controllers/connections-manage-accounts-version-control": ["./controllers/connections-manage-accounts-version-control/src/index.ts"], "@controllers/content-nav": ["./controllers/content-nav/src/index.ts"], "@controllers/controls": ["./controllers/controls/src/index.ts"], "@controllers/controls-owners-candidates": ["./controllers/controls-owners-candidates/src/index.ts"], "@controllers/create-ticket": ["./controllers/create-ticket/src/index.ts"], "@controllers/custom-fields": ["./controllers/custom-fields/src/index.ts"], "@controllers/custom-workflows": ["./controllers/custom-workflows/src/index.ts"], "@controllers/customer-request-details": ["./controllers/customer-request-details/src/index.ts"], "@controllers/customer-requests": ["./controllers/customer-requests/src/index.ts"], "@controllers/dashboard-connections": ["./controllers/dashboard-connections/src/index.ts"], "@controllers/dashboard-insights": ["./controllers/dashboard-insights/src/index.ts"], "@controllers/dashboard-notifications": ["./controllers/dashboard-notifications/src/index.ts"], "@controllers/dashboard-personnel": ["./controllers/dashboard-personnel/src/index.ts"], "@controllers/dashboard-policies": ["./controllers/dashboard-policies/src/index.ts"], "@controllers/dashboard-task-forecast": ["./controllers/dashboard-task-forecast/src/index.ts"], "@controllers/dashboard-test-trends": ["./controllers/dashboard-test-trends/src/index.ts"], "@controllers/dashboard-vendor-risks": ["./controllers/dashboard-vendor-risks/src/index.ts"], "@controllers/device-compliance-check-evidence": ["./controllers/device-compliance-check-evidence/src/index.ts"], "@controllers/edr": ["./controllers/edr/src/index.ts"], "@controllers/entitlements-quota": ["./controllers/entitlements-quota/src/index.ts"], "@controllers/events": ["./controllers/events/src/index.ts"], "@controllers/events-details": ["./controllers/events-details/src/index.ts"], "@controllers/evidence-library": ["./controllers/evidence-library/src/index.ts"], "@controllers/feature-announcement-dismissals": ["./controllers/feature-announcement-dismissals/src/index.ts"], "@controllers/fields-and-formulas-field-details": ["./controllers/fields-and-formulas-field-details/src/index.ts"], "@controllers/fields-and-formulas-formula-details": ["./controllers/fields-and-formulas-formula-details/src/index.ts"], "@controllers/fields-and-formulas-formula-list": ["./controllers/fields-and-formulas-formula-list/src/index.ts"], "@controllers/flatfile": ["./controllers/flatfile/src/index.ts"], "@controllers/flatfile-jobs": ["./controllers/flatfile-jobs/src/index.ts"], "@controllers/frameworks": ["./controllers/frameworks/src/index.ts"], "@controllers/governance-personnel": ["./controllers/governance-personnel/src/index.ts"], "@controllers/governance-policies-history": ["./controllers/governance-policies-history/src/index.ts"], "@controllers/library-test": ["./controllers/library-test/src/index.ts"], "@controllers/library-test-template": ["./controllers/library-test-template/src/index.ts"], "@controllers/library-tests": ["./controllers/library-tests/src/index.ts"], "@controllers/modal": ["./controllers/modal/src/index.ts"], "@controllers/monitoring": ["./controllers/monitoring/src/index.ts"], "@controllers/monitoring-details": ["./controllers/monitoring-details/src/index.ts"], "@controllers/monitoring-test-details": ["./controllers/monitoring-test-details/src/index.ts"], "@controllers/monitors": ["./controllers/monitors/src/index.ts"], "@controllers/my-drata-agent": ["./controllers/my-drata-agent/src/index.ts"], "@controllers/my-drata-background-check": ["./controllers/my-drata-background-check/src/index.ts"], "@controllers/my-drata-devices": ["./controllers/my-drata-devices/src/index.ts"], "@controllers/my-drata-hipaa-training": ["./controllers/my-drata-hipaa-training/src/index.ts"], "@controllers/my-drata-mfa": ["./controllers/my-drata-mfa/src/index.ts"], "@controllers/my-drata-nist-ai-training": ["./controllers/my-drata-nist-ai-training/src/index.ts"], "@controllers/my-drata-policies": ["./controllers/my-drata-policies/src/index.ts"], "@controllers/my-drata-security-training": ["./controllers/my-drata-security-training/src/index.ts"], "@controllers/notification-rule": ["./controllers/notification-rule/src/index.ts"], "@controllers/onboarding": ["./controllers/onboarding/src/index.ts"], "@controllers/onboarding-employee-security": ["./controllers/onboarding-employee-security/src/index.ts"], "@controllers/page-aside": ["./controllers/page-aside/src/index.ts"], "@controllers/page-header": ["./controllers/page-header/src/index.ts"], "@controllers/page-nav": ["./controllers/page-nav/src/index.ts"], "@controllers/panel": ["./controllers/panel/src/index.ts"], "@controllers/personnel": ["./controllers/personnel/src/index.ts"], "@controllers/personnel-me": ["./controllers/personnel-me/src/index.ts"], "@controllers/pipeline": ["./controllers/pipeline/src/index.ts"], "@controllers/pipeline-api-keys": ["./controllers/pipeline-api-keys/src/index.ts"], "@controllers/plan-and-usage": ["./controllers/plan-and-usage/src/index.ts"], "@controllers/policies": ["./controllers/policies/src/index.ts"], "@controllers/policy-builder": ["./controllers/policy-builder/src/index.ts"], "@controllers/product": ["./controllers/product/src/index.ts"], "@controllers/product-frameworks": ["./controllers/product-frameworks/src/index.ts"], "@controllers/programmatic-navigation": ["./controllers/programmatic-navigation/src/index.ts"], "@controllers/requirements": ["./controllers/requirements/src/index.ts"], "@controllers/risk": ["./controllers/risk/src/index.ts"], "@controllers/risk-details": ["./controllers/risk-details/src/index.ts"], "@controllers/risk-vulnerabilities": ["./controllers/risk-vulnerabilities/src/index.ts"], "@controllers/role-administration": ["./controllers/role-administration/src/index.ts"], "@controllers/route": ["./controllers/route/src/index.ts"], "@controllers/service-user": ["./controllers/service-user/src/index.ts"], "@controllers/settings": ["./controllers/settings/src/index.ts"], "@controllers/settings-api-keys": ["./controllers/settings-api-keys/src/index.ts"], "@controllers/settings-user": ["./controllers/settings-user/src/index.ts"], "@controllers/snackbar": ["./controllers/snackbar/src/index.ts"], "@controllers/synchronizations": ["./controllers/synchronizations/src/index.ts"], "@controllers/tasks": ["./controllers/tasks/src/index.ts"], "@controllers/tenant-frameworks": ["./controllers/tenant-frameworks/src/index.ts"], "@controllers/ticket-automation": ["./controllers/ticket-automation/src/index.ts"], "@controllers/ticket-automation-assignees": ["./controllers/ticket-automation-assignees/src/index.ts"], "@controllers/ticket-automation-details": ["./controllers/ticket-automation-details/src/index.ts"], "@controllers/ticket-automation-labels": ["./controllers/ticket-automation-labels/src/index.ts"], "@controllers/ticket-automation-projects": ["./controllers/ticket-automation-projects/src/index.ts"], "@controllers/ticket-automation-ticket-fields": ["./controllers/ticket-automation-ticket-fields/src/index.ts"], "@controllers/ticket-automation-ticket-types": ["./controllers/ticket-automation-ticket-types/src/index.ts"], "@controllers/ticket-download": ["./controllers/ticket-download/src/index.ts"], "@controllers/upsell": ["./controllers/upsell/src/index.ts"], "@controllers/user-details": ["./controllers/user-details/src/index.ts"], "@controllers/user-devices": ["./controllers/user-devices/src/index.ts"], "@controllers/user-policies-policy-versions": ["./controllers/user-policies-policy-versions/src/index.ts"], "@controllers/user-risks": ["./controllers/user-risks/src/index.ts"], "@controllers/users": ["./controllers/users/src/index.ts"], "@controllers/utilities": ["./controllers/utilities/src/index.ts"], "@controllers/vendors": ["./controllers/vendors/src/index.ts"], "@controllers/vulnerabilities-settings-modal": ["./controllers/vulnerabilities-settings-modal/src/index.ts"], "@controllers/workspace-details": ["./controllers/workspace-details/src/index.ts"], "@controllers/workspace-monitors": ["./controllers/workspace-monitors/src/index.ts"], "@controllers/workspaces": ["./controllers/workspaces/src/index.ts"], "@controllers/workspaces-frameworks": ["./controllers/workspaces-frameworks/src/index.ts"], "@controllers/workspaces-user-roles": ["./controllers/workspaces-user-roles/src/index.ts"], "@cosmos-lab/components/ai-card": ["./cosmos-lab/components/ai-card/src/index.ts"], "@cosmos-lab/components/ai-toolbar": ["./cosmos-lab/components/ai-toolbar/src/index.ts"], "@cosmos-lab/components/attached-file": ["./cosmos-lab/components/attached-file/src/index.ts"], "@cosmos-lab/components/avatar-stack": ["./cosmos-lab/components/avatar-stack/src/index.ts"], "@cosmos-lab/components/breadcrumbs": ["./cosmos-lab/components/breadcrumbs/src/index.ts"], "@cosmos-lab/components/callout": ["./cosmos-lab/components/callout/src/index.ts"], "@cosmos-lab/components/choice-card": ["./cosmos-lab/components/choice-card/src/index.ts"], "@cosmos-lab/components/choice-card-group": ["./cosmos-lab/components/choice-card-group/src/index.ts"], "@cosmos-lab/components/code-viewer": ["./cosmos-lab/components/code-viewer/src/index.ts"], "@cosmos-lab/components/confirmation": ["./cosmos-lab/components/confirmation/src/index.ts"], "@cosmos-lab/components/copy-field": ["./cosmos-lab/components/copy-field/src/index.ts"], "@cosmos-lab/components/data-bar": ["./cosmos-lab/components/data-bar/src/index.ts"], "@cosmos-lab/components/data-donut": ["./cosmos-lab/components/data-donut/src/index.ts"], "@cosmos-lab/components/data-gauge": ["./cosmos-lab/components/data-gauge/src/index.ts"], "@cosmos-lab/components/data-legend": ["./cosmos-lab/components/data-legend/src/index.ts"], "@cosmos-lab/components/data-meter": ["./cosmos-lab/components/data-meter/src/index.ts"], "@cosmos-lab/components/data-posture": ["./cosmos-lab/components/data-posture/src/index.ts"], "@cosmos-lab/components/date-range-field": ["./cosmos-lab/components/date-range-field/src/index.ts"], "@cosmos-lab/components/date-time": ["./cosmos-lab/components/date-time/src/index.ts"], "@cosmos-lab/components/divider": ["./cosmos-lab/components/divider/src/index.ts"], "@cosmos-lab/components/empty-value": ["./cosmos-lab/components/empty-value/src/index.ts"], "@cosmos-lab/components/framework-badge": ["./cosmos-lab/components/framework-badge/src/index.ts"], "@cosmos-lab/components/framework-badge-stack": ["./cosmos-lab/components/framework-badge-stack/src/index.ts"], "@cosmos-lab/components/gallery-card": ["./cosmos-lab/components/gallery-card/src/index.ts"], "@cosmos-lab/components/gallery-layout": ["./cosmos-lab/components/gallery-layout/src/index.ts"], "@cosmos-lab/components/heatmap": ["./cosmos-lab/components/heatmap/src/index.ts"], "@cosmos-lab/components/heatmap-box": ["./cosmos-lab/components/heatmap-box/src/index.ts"], "@cosmos-lab/components/highlight": ["./cosmos-lab/components/highlight/src/index.ts"], "@cosmos-lab/components/identity": ["./cosmos-lab/components/identity/src/index.ts"], "@cosmos-lab/components/image-upload-field": ["./cosmos-lab/components/image-upload-field/src/index.ts"], "@cosmos-lab/components/list": ["./cosmos-lab/components/list/src/index.ts"], "@cosmos-lab/components/logo-loader": ["./cosmos-lab/components/logo-loader/src/index.ts"], "@cosmos-lab/components/markdown-viewer": ["./cosmos-lab/components/markdown-viewer/src/index.ts"], "@cosmos-lab/components/navigation-menu": ["./cosmos-lab/components/navigation-menu/src/index.ts"], "@cosmos-lab/components/organization": ["./cosmos-lab/components/organization/src/index.ts"], "@cosmos-lab/components/organization-stack": ["./cosmos-lab/components/organization-stack/src/index.ts"], "@cosmos-lab/components/pagination-controls": ["./cosmos-lab/components/pagination-controls/src/index.ts"], "@cosmos-lab/components/panel-section": ["./cosmos-lab/components/panel-section/src/index.ts"], "@cosmos-lab/components/pdf-viewer": ["./cosmos-lab/components/pdf-viewer/src/index.ts"], "@cosmos-lab/components/risk-score": ["./cosmos-lab/components/risk-score/src/index.ts"], "@cosmos-lab/components/show-more": ["./cosmos-lab/components/show-more/src/index.ts"], "@cosmos-lab/components/simple-table": ["./cosmos-lab/components/simple-table/src/index.ts"], "@cosmos-lab/components/spot-illustration": ["./cosmos-lab/components/spot-illustration/src/index.ts"], "@cosmos-lab/components/stacked-list": ["./cosmos-lab/components/stacked-list/src/index.ts"], "@cosmos-lab/components/stat-block": ["./cosmos-lab/components/stat-block/src/index.ts"], "@cosmos-lab/components/status-dot": ["./cosmos-lab/components/status-dot/src/index.ts"], "@cosmos-lab/components/text-editor": ["./cosmos-lab/components/text-editor/src/index.ts"], "@cosmos-lab/components/threshold": ["./cosmos-lab/components/threshold/src/index.ts"], "@cosmos-lab/components/truncation": ["./cosmos-lab/components/truncation/src/index.ts"], "@cosmos-lab/components/wizard": ["./cosmos-lab/components/wizard/src/index.ts"], "@cosmos-lab/components/wizard-step": ["./cosmos-lab/components/wizard-step/src/index.ts"], "@cosmos-lab/components/wordmark": ["./cosmos-lab/components/wordmark/src/index.ts"], "@cosmos/components/accordion": ["./cosmos/components/accordion/src/index.ts"], "@cosmos/components/action-stack": ["./cosmos/components/action-stack/src/index.ts"], "@cosmos/components/avatar": ["./cosmos/components/avatar/src/index.ts"], "@cosmos/components/banner": ["./cosmos/components/banner/src/index.ts"], "@cosmos/components/box": ["./cosmos/components/box/src/index.ts"], "@cosmos/components/button": ["./cosmos/components/button/src/index.ts"], "@cosmos/components/card": ["./cosmos/components/card/src/index.ts"], "@cosmos/components/checkbox": ["./cosmos/components/checkbox/src/index.ts"], "@cosmos/components/checkbox-field": ["./cosmos/components/checkbox-field/src/index.ts"], "@cosmos/components/checkbox-field-group": ["./cosmos/components/checkbox-field-group/src/index.ts"], "@cosmos/components/combobox": ["./cosmos/components/combobox/src/index.ts"], "@cosmos/components/combobox-field": ["./cosmos/components/combobox-field/src/index.ts"], "@cosmos/components/datatable": ["./cosmos/components/datatable/src/index.ts"], "@cosmos/components/date-picker-field": ["./cosmos/components/date-picker-field/src/index.ts"], "@cosmos/components/dropdown": ["./cosmos/components/dropdown/src/index.ts"], "@cosmos/components/empty-state": ["./cosmos/components/empty-state/src/index.ts"], "@cosmos/components/feedback": ["./cosmos/components/feedback/src/index.ts"], "@cosmos/components/field-feedback": ["./cosmos/components/field-feedback/src/index.ts"], "@cosmos/components/field-label": ["./cosmos/components/field-label/src/index.ts"], "@cosmos/components/file-upload": ["./cosmos/components/file-upload/src/index.ts"], "@cosmos/components/file-upload-field": ["./cosmos/components/file-upload-field/src/index.ts"], "@cosmos/components/filter-field": ["./cosmos/components/filter-field/src/index.ts"], "@cosmos/components/filter-form": ["./cosmos/components/filter-form/src/index.ts"], "@cosmos/components/filters": ["./cosmos/components/filters/src/index.ts"], "@cosmos/components/focus-scope": ["./cosmos/components/focus-scope/src/index.ts"], "@cosmos/components/form-field": ["./cosmos/components/form-field/src/index.ts"], "@cosmos/components/grid": ["./cosmos/components/grid/src/index.ts"], "@cosmos/components/icon": ["./cosmos/components/icon/src/index.ts"], "@cosmos/components/input": ["./cosmos/components/input/src/index.ts"], "@cosmos/components/interstitial-layout": ["./cosmos/components/interstitial-layout/src/index.ts"], "@cosmos/components/key-value-pair": ["./cosmos/components/key-value-pair/src/index.ts"], "@cosmos/components/link": ["./cosmos/components/link/src/index.ts"], "@cosmos/components/list-box": ["./cosmos/components/list-box/src/index.ts"], "@cosmos/components/loader": ["./cosmos/components/loader/src/index.ts"], "@cosmos/components/metadata": ["./cosmos/components/metadata/src/index.ts"], "@cosmos/components/modal": ["./cosmos/components/modal/src/index.ts"], "@cosmos/components/nav-link": ["./cosmos/components/nav-link/src/index.ts"], "@cosmos/components/no-component": ["./cosmos/components/no-component/src/index.ts"], "@cosmos/components/page-header": ["./cosmos/components/page-header/src/index.ts"], "@cosmos/components/panel": ["./cosmos/components/panel/src/index.ts"], "@cosmos/components/popover": ["./cosmos/components/popover/src/index.ts"], "@cosmos/components/radio-field": ["./cosmos/components/radio-field/src/index.ts"], "@cosmos/components/radio-field-group": ["./cosmos/components/radio-field-group/src/index.ts"], "@cosmos/components/schema-dropdown": ["./cosmos/components/schema-dropdown/src/index.ts"], "@cosmos/components/search": ["./cosmos/components/search/src/index.ts"], "@cosmos/components/select": ["./cosmos/components/select/src/index.ts"], "@cosmos/components/select-field": ["./cosmos/components/select-field/src/index.ts"], "@cosmos/components/skeleton": ["./cosmos/components/skeleton/src/index.ts"], "@cosmos/components/slider": ["./cosmos/components/slider/src/index.ts"], "@cosmos/components/slider-field": ["./cosmos/components/slider-field/src/index.ts"], "@cosmos/components/snackbar": ["./cosmos/components/snackbar/src/index.ts"], "@cosmos/components/stack": ["./cosmos/components/stack/src/index.ts"], "@cosmos/components/tabs": ["./cosmos/components/tabs/src/index.ts"], "@cosmos/components/tag-group": ["./cosmos/components/tag-group/src/index.ts"], "@cosmos/components/text": ["./cosmos/components/text/src/index.ts"], "@cosmos/components/text-field": ["./cosmos/components/text-field/src/index.ts"], "@cosmos/components/textarea-field": ["./cosmos/components/textarea-field/src/index.ts"], "@cosmos/components/theme-provider": ["./cosmos/components/theme-provider/src/index.ts"], "@cosmos/components/toggle": ["./cosmos/components/toggle/src/index.ts"], "@cosmos/components/toggle-field": ["./cosmos/components/toggle-field/src/index.ts"], "@cosmos/components/toggle-group": ["./cosmos/components/toggle-group/src/index.ts"], "@cosmos/components/tooltip": ["./cosmos/components/tooltip/src/index.ts"], "@cosmos/components/visually-hidden": ["./cosmos/components/visually-hidden/src/index.ts"], "@cosmos/constants/dropdown-constants": ["./cosmos/constants/dropdown-constants/src/index.ts"], "@cosmos/constants/tokens": ["./cosmos/constants/tokens/src/index.ts"], "@cosmos/constants/z-index": ["./cosmos/constants/z-index/src/index.ts"], "@cosmos/helpers/state-to-css": ["./cosmos/helpers/state-to-css/src/index.ts"], "@cosmos/helpers/styled-mixins": ["./cosmos/helpers/styled-mixins/src/index.ts"], "@cosmos/hooks/use-dimension-props": ["./cosmos/hooks/use-dimension-props/src/index.ts"], "@cosmos/hooks/use-downshift-options": ["./cosmos/hooks/use-downshift-options/src/index.ts"], "@cosmos/hooks/use-element-height-as-css-var": ["./cosmos/hooks/use-element-height-as-css-var/src/index.ts"], "@cosmos/hooks/use-extended-props": ["./cosmos/hooks/use-extended-props/src/index.ts"], "@cosmos/hooks/use-flat-options": ["./cosmos/hooks/use-flat-options/src/index.ts"], "@cosmos/hooks/use-match-width": ["./cosmos/hooks/use-match-width/src/index.ts"], "@cosmos/hooks/use-overflow-scroll": ["./cosmos/hooks/use-overflow-scroll/src/index.ts"], "@cosmos/hooks/use-partial-visibility": ["./cosmos/hooks/use-partial-visibility/src/index.ts"], "@cosmos/storybook/Actions": ["./cosmos/storybook/Actions/src/index.ts"], "@cosmos/storybook/AI": ["./cosmos/storybook/AI/src/index.ts"], "@cosmos/storybook/Document-Viewers": ["./cosmos/storybook/Document-Viewers/src/index.ts"], "@cosmos/storybook/Feedback": ["./cosmos/storybook/Feedback/src/index.ts"], "@cosmos/storybook/Forms": ["./cosmos/storybook/Forms/src/index.ts"], "@cosmos/storybook/Foundations": ["./cosmos/storybook/Foundations/src/index.ts"], "@cosmos/storybook/Guidelines": ["./cosmos/storybook/Guidelines/src/index.ts"], "@cosmos/storybook/Information-and-Data": ["./cosmos/storybook/Information-and-Data/src/index.ts"], "@cosmos/storybook/Layout-and-Structure": ["./cosmos/storybook/Layout-and-Structure/src/index.ts"], "@cosmos/storybook/Media-and-Imagery": ["./cosmos/storybook/Media-and-Imagery/src/index.ts"], "@cosmos/storybook/Navigation": ["./cosmos/storybook/Navigation/src/index.ts"], "@cosmos/storybook/Patterns": ["./cosmos/storybook/Patterns/src/index.ts"], "@cosmos/storybook/template-layouts": ["./cosmos/storybook/template-layouts/src/index.ts"], "@cosmos/storybook/Tokens": ["./cosmos/storybook/Tokens/src/index.ts"], "@cosmos/storybook/Typography-and-Content": ["./cosmos/storybook/Typography-and-Content/src/index.ts"], "@cosmos/storybook/Utilities": ["./cosmos/storybook/Utilities/src/index.ts"], "@globals/analytics": ["./globals/analytics/src/index.ts"], "@globals/api-sdk": ["./globals/api-sdk/src/index.ts"], "@globals/api-sdk/queries": ["./globals/api-sdk/src/lib/generated/@tanstack/react-query.gen.ts"], "@globals/api-sdk/types": ["./globals/api-sdk/src/lib/generated/types.gen.ts"], "@globals/api-sdk/zod": ["./globals/api-sdk/src/lib/generated/zod.gen.ts"], "@globals/async-events": ["./globals/async-events/src/index.ts"], "@globals/config": ["./globals/config/src/index.ts"], "@globals/current-company": ["./globals/current-company/src/index.ts"], "@globals/current-user": ["./globals/current-user/src/index.ts"], "@globals/datadog": ["./globals/datadog/src/index.ts"], "@globals/entitlement-flag": ["./globals/entitlement-flag/src/index.ts"], "@globals/feature-access": ["./globals/feature-access/src/index.ts"], "@globals/fetch-client": ["./globals/fetch-client/src/index.ts"], "@globals/i18n": ["./globals/i18n/src/index.ts"], "@globals/i18n/macro": ["./globals/i18n/src/lib/macro.ts"], "@globals/logger": ["./globals/logger/src/index.ts"], "@globals/mobx": ["./globals/mobx/src/index.ts"], "@globals/providers": ["./globals/providers/src/index.ts"], "@globals/query-client": ["./globals/query-client/src/index.ts"], "@globals/socket": ["./globals/socket/src/index.ts"], "@globals/use-query-params": ["./globals/use-query-params/src/index.ts"], "@globals/workspaces": ["./globals/workspaces/src/index.ts"], "@globals/zod": ["./globals/zod/src/index.ts"], "@helpers/bytes": ["./helpers/bytes/src/index.ts"], "@helpers/calculate-threshold-levels": ["./helpers/calculate-threshold-levels/src/index.ts"], "@helpers/control-status": ["./helpers/control-status/src/index.ts"], "@helpers/controls-evidence": ["./helpers/controls-evidence/src/index.ts"], "@helpers/create-ticket-modal": ["./helpers/create-ticket-modal/src/index.ts"], "@helpers/custom-fields": ["./helpers/custom-fields/src/index.ts"], "@helpers/date-time": ["./helpers/date-time/src/index.ts"], "@helpers/donut": ["./helpers/donut/src/index.ts"], "@helpers/download-file": ["./helpers/download-file/src/index.ts"], "@helpers/evidence": ["./helpers/evidence/src/index.ts"], "@helpers/external-integration": ["./helpers/external-integration/src/index.ts"], "@helpers/feature-announcement": ["./helpers/feature-announcement/src/index.ts"], "@helpers/formatters": ["./helpers/formatters/src/index.ts"], "@helpers/get-avatar-fallback-text": ["./helpers/get-avatar-fallback-text/src/index.ts"], "@helpers/get-company-frameworks": ["./helpers/get-company-frameworks/src/index.ts"], "@helpers/get-tenant-id-from-client-id": ["./helpers/get-tenant-id-from-client-id/src/index.ts"], "@helpers/library-test-manage-active-modal": ["./helpers/library-test-manage-active-modal/src/index.ts"], "@helpers/path": ["./helpers/path/src/index.ts"], "@helpers/risk-score": ["./helpers/risk-score/src/index.ts"], "@helpers/risk-treatment": ["./helpers/risk-treatment/src/index.ts"], "@helpers/roles": ["./helpers/roles/src/index.ts"], "@helpers/severity": ["./helpers/severity/src/index.ts"], "@helpers/string": ["./helpers/string/src/index.ts"], "@helpers/table": ["./helpers/table/src/index.ts"], "@helpers/temp-confirmation-modal": ["./helpers/temp-confirmation-modal/src/index.ts"], "@helpers/toast": ["./helpers/toast/src/index.ts"], "@helpers/upload-file": ["./helpers/upload-file/src/index.ts"], "@helpers/user": ["./helpers/user/src/index.ts"], "@models/access-review": ["./models/access-review/src/index.ts"], "@models/access-review-applications-personnel-details": ["./models/access-review-applications-personnel-details/src/index.ts"], "@models/access-review-completed-action-cell": ["./models/access-review-completed-action-cell/src/index.ts"], "@models/access-review-completed-details-header": ["./models/access-review-completed-details-header/src/index.ts"], "@models/access-review-personnel": ["./models/access-review-personnel/src/index.ts"], "@models/access-review-personnel-details": ["./models/access-review-personnel-details/src/index.ts"], "@models/access-review-request-changes": ["./models/access-review-request-changes/src/index.ts"], "@models/assets": ["./models/assets/src/index.ts"], "@models/audit-details-overview": ["./models/audit-details-overview/src/index.ts"], "@models/audit-hub-wizard-sample": ["./models/audit-hub-wizard-sample/src/index.ts"], "@models/auditor-client-audit": ["./models/auditor-client-audit/src/index.ts"], "@models/auditor-client-page-header": ["./models/auditor-client-page-header/src/index.ts"], "@models/auditor-settings-page-header": ["./models/auditor-settings-page-header/src/index.ts"], "@models/audits-details": ["./models/audits-details/src/index.ts"], "@models/connection-details-page-header": ["./models/connection-details-page-header/src/index.ts"], "@models/connections": ["./models/connections/src/index.ts"], "@models/control-details": ["./models/control-details/src/index.ts"], "@models/controls": ["./models/controls/src/index.ts"], "@models/custom-fields": ["./models/custom-fields/src/index.ts"], "@models/dashboard": ["./models/dashboard/src/index.ts"], "@models/domains-nav": ["./models/domains-nav/src/index.ts"], "@models/event-details-page-header": ["./models/event-details-page-header/src/index.ts"], "@models/events": ["./models/events/src/index.ts"], "@models/evidence": ["./models/evidence/src/index.ts"], "@models/evidence-library-details": ["./models/evidence-library-details/src/index.ts"], "@models/evidence-viewer-page-header": ["./models/evidence-viewer-page-header/src/index.ts"], "@models/fields-and-formulas-field": ["./models/fields-and-formulas-field/src/index.ts"], "@models/fields-and-formulas-formula": ["./models/fields-and-formulas-formula/src/index.ts"], "@models/framework-create": ["./models/framework-create/src/index.ts"], "@models/framework-details": ["./models/framework-details/src/index.ts"], "@models/frameworks": ["./models/frameworks/src/index.ts"], "@models/frameworks-page-header": ["./models/frameworks-page-header/src/index.ts"], "@models/library-test": ["./models/library-test/src/index.ts"], "@models/library-tests": ["./models/library-tests/src/index.ts"], "@models/monitoring-code-details": ["./models/monitoring-code-details/src/index.ts"], "@models/monitoring-details": ["./models/monitoring-details/src/index.ts"], "@models/my-drata": ["./models/my-drata/src/index.ts"], "@models/notification-rule": ["./models/notification-rule/src/index.ts"], "@models/personnel-compliance": ["./models/personnel-compliance/src/index.ts"], "@models/plan": ["./models/plan/src/index.ts"], "@models/policies": ["./models/policies/src/index.ts"], "@models/policy-builder": ["./models/policy-builder/src/index.ts"], "@models/quick-start": ["./models/quick-start/src/index.ts"], "@models/request-details-page-header": ["./models/request-details-page-header/src/index.ts"], "@models/requirement-details": ["./models/requirement-details/src/index.ts"], "@models/requirements": ["./models/requirements/src/index.ts"], "@models/risk-details": ["./models/risk-details/src/index.ts"], "@models/risk-settings": ["./models/risk-settings/src/index.ts"], "@models/risk-vulnerabilities": ["./models/risk-vulnerabilities/src/index.ts"], "@models/settings-api-keys-details": ["./models/settings-api-keys-details/src/index.ts"], "@models/settings-compliance-as-code-pipeline-details-page-header": ["./models/settings-compliance-as-code-pipeline-details-page-header/src/index.ts"], "@models/tasks": ["./models/tasks/src/index.ts"], "@models/ticket-automation-details": ["./models/ticket-automation-details/src/index.ts"], "@models/upsell": ["./models/upsell/src/index.ts"], "@models/vendor-security-reviews": ["./models/vendor-security-reviews/src/index.ts"], "@models/vendors-profile": ["./models/vendors-profile/src/index.ts"], "@models/workspace-details": ["./models/workspace-details/src/index.ts"], "@ui/app-button": ["./ui/app-button/src/index.ts"], "@ui/app-link": ["./ui/app-link/src/index.ts"], "@ui/content-aside": ["./ui/content-aside/src/index.ts"], "@ui/error-boundary": ["./ui/error-boundary/src/index.ts"], "@ui/forms": ["./ui/forms/src/index.ts"], "@ui/layout-landmarks": ["./ui/layout-landmarks/src/index.ts"], "@ui/modal-root": ["./ui/modal-root/src/index.ts"], "@ui/nav-app-link": ["./ui/nav-app-link/src/index.ts"], "@ui/page-aside": ["./ui/page-aside/src/index.ts"], "@ui/page-content": ["./ui/page-content/src/index.ts"], "@ui/page-header": ["./ui/page-header/src/index.ts"], "@ui/panel-root": ["./ui/panel-root/src/index.ts"], "@ui/skip-link": ["./ui/skip-link/src/index.ts"], "@ui/snackbar-root": ["./ui/snackbar-root/src/index.ts"], "@ui/user-avatar": ["./ui/user-avatar/src/index.ts"], "@ui/workspace-picker": ["./ui/workspace-picker/src/index.ts"], "@views/accept-terms": ["./views/accept-terms/src/index.ts"], "@views/access-review-application-details": ["./views/access-review-application-details/src/index.ts"], "@views/access-review-application-details-personnel-details": ["./views/access-review-application-details-personnel-details/src/index.ts"], "@views/access-review-application-period-personnel": ["./views/access-review-application-period-personnel/src/index.ts"], "@views/access-review-applications": ["./views/access-review-applications/src/index.ts"], "@views/access-review-completed-reviews": ["./views/access-review-completed-reviews/src/index.ts"], "@views/access-review-create-period": ["./views/access-review-create-period/src/index.ts"], "@views/access-review-personnel": ["./views/access-review-personnel/src/index.ts"], "@views/add-asset": ["./views/add-asset/src/index.ts"], "@views/add-test-to-program": ["./views/add-test-to-program/src/index.ts"], "@views/asset-details": ["./views/asset-details/src/index.ts"], "@views/assets": ["./views/assets/src/index.ts"], "@views/audit-details-download-only-page": ["./views/audit-details-download-only-page/src/index.ts"], "@views/audit-details-overview": ["./views/audit-details-overview/src/index.ts"], "@views/audit-details-related-controls": ["./views/audit-details-related-controls/src/index.ts"], "@views/audit-hub-auditor-clients": ["./views/audit-hub-auditor-clients/src/index.ts"], "@views/audit-hub-auditor-settings-apikeys": ["./views/audit-hub-auditor-settings-apikeys/src/index.ts"], "@views/audit-hub-auditor-settings-profile": ["./views/audit-hub-auditor-settings-profile/src/index.ts"], "@views/audit-hub-evidence-request-details-controls": ["./views/audit-hub-evidence-request-details-controls/src/index.ts"], "@views/audit-hub-evidence-request-details-evidence": ["./views/audit-hub-evidence-request-details-evidence/src/index.ts"], "@views/audit-hub-evidence-request-details-overview": ["./views/audit-hub-evidence-request-details-overview/src/index.ts"], "@views/audit-hub-evidence-viewer": ["./views/audit-hub-evidence-viewer/src/index.ts"], "@views/audit-hub-wizard-sample": ["./views/audit-hub-wizard-sample/src/index.ts"], "@views/auditor-audits-view": ["./views/auditor-audits-view/src/index.ts"], "@views/auditor-client-audit-details": ["./views/auditor-client-audit-details/src/index.ts"], "@views/audits-auditors-list": ["./views/audits-auditors-list/src/index.ts"], "@views/audits-details-page": ["./views/audits-details-page/src/index.ts"], "@views/audits-gallery": ["./views/audits-gallery/src/index.ts"], "@views/audits-new-audit": ["./views/audits-new-audit/src/index.ts"], "@views/available-frameworks": ["./views/available-frameworks/src/index.ts"], "@views/compliance-as-code-repository-detail": ["./views/compliance-as-code-repository-detail/src/index.ts"], "@views/connection": ["./views/connection/src/index.ts"], "@views/connection-active": ["./views/connection-active/src/index.ts"], "@views/connection-available": ["./views/connection-available/src/index.ts"], "@views/connection-manage-accounts-infrastructure": ["./views/connection-manage-accounts-infrastructure/src/index.ts"], "@views/connections-manage-accounts-access-review": ["./views/connections-manage-accounts-access-review/src/index.ts"], "@views/connections-manage-accounts-background-checks": ["./views/connections-manage-accounts-background-checks/src/index.ts"], "@views/connections-manage-accounts-devices": ["./views/connections-manage-accounts-devices/src/index.ts"], "@views/connections-manage-accounts-observability": ["./views/connections-manage-accounts-observability/src/index.ts"], "@views/connections-manage-accounts-version-control": ["./views/connections-manage-accounts-version-control/src/index.ts"], "@views/connections-setup-aws-wizard-configure-settings": ["./views/connections-setup-aws-wizard-configure-settings/src/index.ts"], "@views/connections-setup-aws-wizard-connect-integration": ["./views/connections-setup-aws-wizard-connect-integration/src/index.ts"], "@views/connections-setup-aws-wizard-set-scope": ["./views/connections-setup-aws-wizard-set-scope/src/index.ts"], "@views/control-workflows": ["./views/control-workflows/src/index.ts"], "@views/controls": ["./views/controls/src/index.ts"], "@views/controls-add-control": ["./views/controls-add-control/src/index.ts"], "@views/controls-evidence": ["./views/controls-evidence/src/index.ts"], "@views/controls-evidence-details": ["./views/controls-evidence-details/src/index.ts"], "@views/controls-frameworks": ["./views/controls-frameworks/src/index.ts"], "@views/controls-manage-evidences": ["./views/controls-manage-evidences/src/index.ts"], "@views/controls-monitoring": ["./views/controls-monitoring/src/index.ts"], "@views/controls-overview": ["./views/controls-overview/src/index.ts"], "@views/controls-policies": ["./views/controls-policies/src/index.ts"], "@views/controls-risks": ["./views/controls-risks/src/index.ts"], "@views/create-control-info": ["./views/create-control-info/src/index.ts"], "@views/create-framework": ["./views/create-framework/src/index.ts"], "@views/create-framework-requirement": ["./views/create-framework-requirement/src/index.ts"], "@views/create-ticket-modal": ["./views/create-ticket-modal/src/index.ts"], "@views/current-frameworks": ["./views/current-frameworks/src/index.ts"], "@views/dashboard-all-workspaces": ["./views/dashboard-all-workspaces/src/index.ts"], "@views/dashboard-insights": ["./views/dashboard-insights/src/index.ts"], "@views/event-details": ["./views/event-details/src/index.ts"], "@views/events": ["./views/events/src/index.ts"], "@views/evidence-add-evidence": ["./views/evidence-add-evidence/src/index.ts"], "@views/evidence-details-artifacts": ["./views/evidence-details-artifacts/src/index.ts"], "@views/evidence-details-linked-controls": ["./views/evidence-details-linked-controls/src/index.ts"], "@views/evidence-details-overview": ["./views/evidence-details-overview/src/index.ts"], "@views/evidence-details-test-evidence": ["./views/evidence-details-test-evidence/src/index.ts"], "@views/evidence-library": ["./views/evidence-library/src/index.ts"], "@views/framework-requirements-upload": ["./views/framework-requirements-upload/src/index.ts"], "@views/framework-start-activation": ["./views/framework-start-activation/src/index.ts"], "@views/frameworks-baseline": ["./views/frameworks-baseline/src/index.ts"], "@views/frameworks-controls": ["./views/frameworks-controls/src/index.ts"], "@views/frameworks-details": ["./views/frameworks-details/src/index.ts"], "@views/frameworks-overview": ["./views/frameworks-overview/src/index.ts"], "@views/governance-access-review-active": ["./views/governance-access-review-active/src/index.ts"], "@views/library-test-mappings": ["./views/library-test-mappings/src/index.ts"], "@views/library-test-overview": ["./views/library-test-overview/src/index.ts"], "@views/library-test-template-details": ["./views/library-test-template-details/src/index.ts"], "@views/library-tests": ["./views/library-tests/src/index.ts"], "@views/login": ["./views/login/src/index.ts"], "@views/login-account-type": ["./views/login-account-type/src/index.ts"], "@views/login-auditor": ["./views/login-auditor/src/index.ts"], "@views/login-customer": ["./views/login-customer/src/index.ts"], "@views/login-email-sent": ["./views/login-email-sent/src/index.ts"], "@views/login-service-user": ["./views/login-service-user/src/index.ts"], "@views/monitoring": ["./views/monitoring/src/index.ts"], "@views/monitoring-builder": ["./views/monitoring-builder/src/index.ts"], "@views/monitoring-code": ["./views/monitoring-code/src/index.ts"], "@views/monitoring-details": ["./views/monitoring-details/src/index.ts"], "@views/monitoring-details-controls": ["./views/monitoring-details-controls/src/index.ts"], "@views/monitoring-details-controls-panel": ["./views/monitoring-details-controls-panel/src/index.ts"], "@views/monitoring-details-event-panel": ["./views/monitoring-details-event-panel/src/index.ts"], "@views/monitoring-details-exclusions": ["./views/monitoring-details-exclusions/src/index.ts"], "@views/monitoring-details-findings": ["./views/monitoring-details-findings/src/index.ts"], "@views/monitoring-details-findings-panel": ["./views/monitoring-details-findings-panel/src/index.ts"], "@views/monitoring-details-overview": ["./views/monitoring-details-overview/src/index.ts"], "@views/monitoring-pipeline": ["./views/monitoring-pipeline/src/index.ts"], "@views/monitoring-pipeline-run-details-causes": ["./views/monitoring-pipeline-run-details-causes/src/index.ts"], "@views/monitoring-pipeline-run-details-causes-panel": ["./views/monitoring-pipeline-run-details-causes-panel/src/index.ts"], "@views/monitoring-pipeline-run-details-exclusions": ["./views/monitoring-pipeline-run-details-exclusions/src/index.ts"], "@views/monitoring-pipeline-run-details-failures": ["./views/monitoring-pipeline-run-details-failures/src/index.ts"], "@views/monitoring-pipeline-run-details-failures-panel": ["./views/monitoring-pipeline-run-details-failures-panel/src/index.ts"], "@views/monitoring-pipeline-run-details-previous": ["./views/monitoring-pipeline-run-details-previous/src/index.ts"], "@views/my-drata": ["./views/my-drata/src/index.ts"], "@views/personnel": ["./views/personnel/src/index.ts"], "@views/personnel-add-exclusion": ["./views/personnel-add-exclusion/src/index.ts"], "@views/personnel-add-former": ["./views/personnel-add-former/src/index.ts"], "@views/personnel-details": ["./views/personnel-details/src/index.ts"], "@views/personnel-exclusion-add": ["./views/personnel-exclusion-add/src/index.ts"], "@views/personnel-exclusion-edit": ["./views/personnel-exclusion-edit/src/index.ts"], "@views/policies": ["./views/policies/src/index.ts"], "@views/policies-add": ["./views/policies-add/src/index.ts"], "@views/policies-add-external": ["./views/policies-add-external/src/index.ts"], "@views/policies-builder-history": ["./views/policies-builder-history/src/index.ts"], "@views/policies-builder-overview": ["./views/policies-builder-overview/src/index.ts"], "@views/policies-builder-policy": ["./views/policies-builder-policy/src/index.ts"], "@views/policy-details-panel": ["./views/policy-details-panel/src/index.ts"], "@views/quick-start": ["./views/quick-start/src/index.ts"], "@views/requirement-details-panel": ["./views/requirement-details-panel/src/index.ts"], "@views/risk-insights": ["./views/risk-insights/src/index.ts"], "@views/risk-register-add-owners": ["./views/risk-register-add-owners/src/index.ts"], "@views/risk-register-assign-category": ["./views/risk-register-assign-category/src/index.ts"], "@views/risk-register-create-risk": ["./views/risk-register-create-risk/src/index.ts"], "@views/risk-register-details-panel": ["./views/risk-register-details-panel/src/index.ts"], "@views/risk-register-library": ["./views/risk-register-library/src/index.ts"], "@views/risk-register-management": ["./views/risk-register-management/src/index.ts"], "@views/risk-register-mitigation-controls": ["./views/risk-register-mitigation-controls/src/index.ts"], "@views/risk-register-overview": ["./views/risk-register-overview/src/index.ts"], "@views/risk-register-settings-panel": ["./views/risk-register-settings-panel/src/index.ts"], "@views/risk-register-settings-scoring": ["./views/risk-register-settings-scoring/src/index.ts"], "@views/risk-register-settings-threshold": ["./views/risk-register-settings-threshold/src/index.ts"], "@views/risk-vulnerabilities": ["./views/risk-vulnerabilities/src/index.ts"], "@views/risk-vulnerabilities-details": ["./views/risk-vulnerabilities-details/src/index.ts"], "@views/settings-ai": ["./views/settings-ai/src/index.ts"], "@views/settings-api-keys": ["./views/settings-api-keys/src/index.ts"], "@views/settings-api-keys-create": ["./views/settings-api-keys-create/src/index.ts"], "@views/settings-api-keys-details": ["./views/settings-api-keys-details/src/index.ts"], "@views/settings-company-details": ["./views/settings-company-details/src/index.ts"], "@views/settings-compliance-as-code-pipelines": ["./views/settings-compliance-as-code-pipelines/src/index.ts"], "@views/settings-compliance-as-code-pipelines-create": ["./views/settings-compliance-as-code-pipelines-create/src/index.ts"], "@views/settings-compliance-as-code-pipelines-detail-view": ["./views/settings-compliance-as-code-pipelines-detail-view/src/index.ts"], "@views/settings-compliance-as-code-repositories": ["./views/settings-compliance-as-code-repositories/src/index.ts"], "@views/settings-connections-create": ["./views/settings-connections-create/src/index.ts"], "@views/settings-connections-manage": ["./views/settings-connections-manage/src/index.ts"], "@views/settings-connections-overview": ["./views/settings-connections-overview/src/index.ts"], "@views/settings-fields-and-formulas-fields": ["./views/settings-fields-and-formulas-fields/src/index.ts"], "@views/settings-fields-and-formulas-fields-create": ["./views/settings-fields-and-formulas-fields-create/src/index.ts"], "@views/settings-fields-and-formulas-fields-edit": ["./views/settings-fields-and-formulas-fields-edit/src/index.ts"], "@views/settings-fields-and-formulas-formula-create": ["./views/settings-fields-and-formulas-formula-create/src/index.ts"], "@views/settings-fields-and-formulas-formula-edit": ["./views/settings-fields-and-formulas-formula-edit/src/index.ts"], "@views/settings-fields-and-formulas-formulas": ["./views/settings-fields-and-formulas-formulas/src/index.ts"], "@views/settings-language": ["./views/settings-language/src/index.ts"], "@views/settings-my-language": ["./views/settings-my-language/src/index.ts"], "@views/settings-notifications": ["./views/settings-notifications/src/index.ts"], "@views/settings-organization-details-info": ["./views/settings-organization-details-info/src/index.ts"], "@views/settings-organization-details-key-personnel": ["./views/settings-organization-details-key-personnel/src/index.ts"], "@views/settings-organizational-notifications": ["./views/settings-organizational-notifications/src/index.ts"], "@views/settings-organizational-notifications-edit": ["./views/settings-organizational-notifications-edit/src/index.ts"], "@views/settings-personnel-compliance-human-resources": ["./views/settings-personnel-compliance-human-resources/src/index.ts"], "@views/settings-personnel-compliance-internal-security": ["./views/settings-personnel-compliance-internal-security/src/index.ts"], "@views/settings-personnel-compliance-training": ["./views/settings-personnel-compliance-training/src/index.ts"], "@views/settings-plan-and-usage": ["./views/settings-plan-and-usage/src/index.ts"], "@views/settings-role-administration": ["./views/settings-role-administration/src/index.ts"], "@views/settings-role-administration-guests": ["./views/settings-role-administration-guests/src/index.ts"], "@views/settings-role-administration-user-details": ["./views/settings-role-administration-user-details/src/index.ts"], "@views/settings-role-administration-users": ["./views/settings-role-administration-users/src/index.ts"], "@views/settings-ticket-automation": ["./views/settings-ticket-automation/src/index.ts"], "@views/settings-ticket-automation-basic": ["./views/settings-ticket-automation-basic/src/index.ts"], "@views/settings-ticket-automation-details": ["./views/settings-ticket-automation-details/src/index.ts"], "@views/settings-ticket-automation-edit": ["./views/settings-ticket-automation-edit/src/index.ts"], "@views/settings-ticket-automation-scope": ["./views/settings-ticket-automation-scope/src/index.ts"], "@views/settings-workflow": ["./views/settings-workflow/src/index.ts"], "@views/settings-workflow-active": ["./views/settings-workflow-active/src/index.ts"], "@views/settings-workflow-archived": ["./views/settings-workflow-archived/src/index.ts"], "@views/settings-workflow-definition": ["./views/settings-workflow-definition/src/index.ts"], "@views/settings-workflow-runs": ["./views/settings-workflow-runs/src/index.ts"], "@views/settings-workspaces": ["./views/settings-workspaces/src/index.ts"], "@views/settings-workspaces-create": ["./views/settings-workspaces-create/src/index.ts"], "@views/settings-workspaces-edit": ["./views/settings-workspaces-edit/src/index.ts"], "@views/tasks": ["./views/tasks/src/index.ts"], "@views/vendor-hub-questionnaire": ["./views/vendor-hub-questionnaire/src/index.ts"], "@views/vendors-add-soc-report-modal": ["./views/vendors-add-soc-report-modal/src/index.ts"], "@views/vendors-current": ["./views/vendors-current/src/index.ts"], "@views/vendors-current-add-vendor": ["./views/vendors-current-add-vendor/src/index.ts"], "@views/vendors-current-bulk-add-vendors": ["./views/vendors-current-bulk-add-vendors/src/index.ts"], "@views/vendors-insights": ["./views/vendors-insights/src/index.ts"], "@views/vendors-mark-vendor-as-active": ["./views/vendors-mark-vendor-as-active/src/index.ts"], "@views/vendors-profile-overview": ["./views/vendors-profile-overview/src/index.ts"], "@views/vendors-profile-reports-and-documents": ["./views/vendors-profile-reports-and-documents/src/index.ts"], "@views/vendors-profile-risks": ["./views/vendors-profile-risks/src/index.ts"], "@views/vendors-profile-risks-add": ["./views/vendors-profile-risks-add/src/index.ts"], "@views/vendors-profile-security-review": ["./views/vendors-profile-security-review/src/index.ts"], "@views/vendors-profile-security-review-files": ["./views/vendors-profile-security-review-files/src/index.ts"], "@views/vendors-profile-security-review-soc": ["./views/vendors-profile-security-review-soc/src/index.ts"], "@views/vendors-profile-security-reviews": ["./views/vendors-profile-security-reviews/src/index.ts"], "@views/vendors-profile-subprocessors": ["./views/vendors-profile-subprocessors/src/index.ts"], "@views/vendors-profile-trust-center": ["./views/vendors-profile-trust-center/src/index.ts"], "@views/vendors-profile-trust-center-document-detail": ["./views/vendors-profile-trust-center-document-detail/src/index.ts"], "@views/vendors-prospective": ["./views/vendors-prospective/src/index.ts"], "@views/vendors-prospective-add": ["./views/vendors-prospective-add/src/index.ts"], "@views/vendors-prospective-security-review-files": ["./views/vendors-prospective-security-review-files/src/index.ts"], "@views/vendors-prospective-settings": ["./views/vendors-prospective-settings/src/index.ts"], "@views/vendors-questionnaire-completed": ["./views/vendors-questionnaire-completed/src/index.ts"], "@views/vendors-questionnaire-send-via-drata-modal": ["./views/vendors-questionnaire-send-via-drata-modal/src/index.ts"], "@views/vendors-questionnaires": ["./views/vendors-questionnaires/src/index.ts"], "@views/vendors-questionnaires-add": ["./views/vendors-questionnaires-add/src/index.ts"], "@views/vendors-questionnaires-import-questions-modal": ["./views/vendors-questionnaires-import-questions-modal/src/index.ts"], "@views/vendors-risks": ["./views/vendors-risks/src/index.ts"], "@views/vendors-security-review-completed": ["./views/vendors-security-review-completed/src/index.ts"], "@views/vendors-security-review-questionnaire": ["./views/vendors-security-review-questionnaire/src/index.ts"], "@views/vendors-settings": ["./views/vendors-settings/src/index.ts"], "@views/vendors-upload-response-files-modal": ["./views/vendors-upload-response-files-modal/src/index.ts"]}}}