import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import type { UpdateCustomerRequestStatusesRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getStatusOptions } from '../constants/status.map.constants';

export function updateStatus(id: string, requestId: number): void {
    if (!requestId) {
        return;
    }

    const customerRequestId = Number(requestId);
    const statusOptions = getStatusOptions();

    switch (id) {
        case statusOptions.IN_REVIEW.id: {
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus({
                requestIds: [customerRequestId],
                status: statusOptions.IN_REVIEW
                    .id as UpdateCustomerRequestStatusesRequestDto['status'],
            });
            break;
        }

        case statusOptions.OUTSTANDING.id: {
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus({
                requestIds: [customerRequestId],
                status: statusOptions.OUTSTANDING
                    .id as UpdateCustomerRequestStatusesRequestDto['status'],
            });
            break;
        }

        case statusOptions.DELETE.id: {
            sharedCustomerRequestDetailsController.deleteCustomerRequests({
                requestIdList: [customerRequestId],
            });
            window.location.href = `/audit-hub/clients/${sharedCustomerRequestDetailsController.clientId}/audits/${sharedCustomerRequestDetailsController.auditorFrameworkId}/details`;

            break;
        }

        case statusOptions.ACCEPTED.id: {
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus({
                requestIds: [customerRequestId],
                status: statusOptions.ACCEPTED
                    .id as UpdateCustomerRequestStatusesRequestDto['status'],
            });
            break;
        }

        default: {
            snackbarController.addSnackbar({
                id: 'invalid-status-error',
                props: {
                    title: t`Invalid Status`,
                    description: t`Please select a valid status.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            break;
        }
    }
}
