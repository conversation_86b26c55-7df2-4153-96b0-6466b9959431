import type { DatatableProps } from '@cosmos/components/datatable';
import type { ControlTestResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { MonitorActionsCell } from '../components/monitor-action-cell.component';
import { MonitorCategoryCell } from '../components/monitor-category-cell.component';
import { MonitorConnectionCell } from '../components/monitor-connection-cell.component';
import { MonitorFindingsCell } from '../components/monitor-findings-cell.component';
import { MonitorNameCell } from '../components/monitor-name-cell.component';
import { MonitorResultCell } from '../components/monitor-result-cell.component';
import { canManageControlTests } from '../helpers/can-manage-control-tests.helpers';
import { CONTROL_COLUMN_SIZES } from './columns-size.constant';

export const getControlsMonitoringColumns =
    (): DatatableProps<ControlTestResponseDto>['columns'] => {
        return [
            ...(canManageControlTests()
                ? [
                      {
                          accessorKey: '',
                          id: 'actions',
                          cell: MonitorActionsCell,
                          enableHiding: true,
                          enableSorting: false,
                          isActionColumn: true,
                          maxSize: CONTROL_COLUMN_SIZES.SMALL,
                          meta: {
                              shouldIgnoreRowClick: true,
                          },
                          minSize: CONTROL_COLUMN_SIZES.SMALL,
                      },
                  ]
                : []),
            {
                accessorKey: 'name',
                header: t`Name`,
                id: 'name',
                enableSorting: true,
                cell: MonitorNameCell,
                minSize: CONTROL_COLUMN_SIZES.LARGE,
            },
            {
                accessorKey: 'checkResultStatus',
                header: t`Result`,
                id: 'result',
                enableSorting: true,
                cell: MonitorResultCell,
                minSize: CONTROL_COLUMN_SIZES.SMALL,
                maxSize: CONTROL_COLUMN_SIZES.SMALL,
            },
            {
                header: t`Finding`,
                id: 'finding',
                accessorKey: 'testId',
                enableSorting: true,
                cell: MonitorFindingsCell,
                minSize: CONTROL_COLUMN_SIZES.SMALL,
                maxSize: CONTROL_COLUMN_SIZES.SMALL,
            },
            {
                header: t`Category`,
                id: 'testId',
                accessorKey: 'testId',
                enableSorting: true,
                cell: MonitorCategoryCell,
                minSize: CONTROL_COLUMN_SIZES.MEDIUM,
            },
            {
                accessorKey: 'testId',
                header: t`Connections`,
                id: 'connection',
                enableSorting: true,
                cell: MonitorConnectionCell,
                minSize: CONTROL_COLUMN_SIZES.MEDIUM,
            },
        ];
    };
