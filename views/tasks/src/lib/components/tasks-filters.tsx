import { constant, isEmpty } from 'lodash-es';
import { useCallback, useEffect, useMemo } from 'react';
import { TasksLink } from '@components/tasks-link';
import {
    customTasksOwnerController,
    sharedCustomTasksController,
    sharedCustomTasksUsersController,
} from '@controllers/tasks';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useQueryParams } from '@globals/use-query-params';
import { getMonthLabel } from '@helpers/date-time';
import { getTaskTypeOptions } from '@models/tasks';
import { TASK_MONTH_PARAM } from '../constants/tasks.constants';
import { getTasksMonthLinks } from '../helpers/tasks-month-links.helper';

const TASKS_FILTER_ID = 'tasks-filter';

// TODO: Migrate this into a task-filter-model ENG-71499
export const TasksFilters = observer(() => {
    const { params: parsedQueryParams, updateParams } = useQueryParams();
    const TASKS_TYPE_OPTIONS = useMemo(() => getTaskTypeOptions(), []);

    const {
        tasksYearOptions: yearOptions,
        hasErrorInTasksYearRange,
        tasksOverview,
        hasErrorInTasksOverview,
        filteredYear,
        setFilters,
    } = sharedCustomTasksController;

    const {
        customTasksUsers,
        hasNextPage: hasMoreUsers,
        isFetching: isFetchingUsers,
        isLoading: isLoadingUsers,
        onFetchUsers,
    } = sharedCustomTasksUsersController;

    const { owner, customTasksUsersQuery, loadOwner } =
        customTasksOwnerController;

    const defaultTypes = useMemo(() => {
        const types = parsedQueryParams.taskTypes;

        if (!Array.isArray(types)) {
            return undefined;
        }

        return TASKS_TYPE_OPTIONS.filter((option) =>
            types.includes(option.value),
        );
    }, [TASKS_TYPE_OPTIONS, parsedQueryParams.taskTypes]);

    const defaultYear = useMemo(() => {
        return yearOptions.find(
            (option) => option.value === String(filteredYear),
        );
    }, [yearOptions, filteredYear]);

    const handleTypeChange = useCallback(
        (value: ListBoxItemData[] | ListBoxItemData) => {
            if (!Array.isArray(value)) {
                return;
            }

            const types = value.map((item) => item.value);

            updateParams({ taskTypes: types });
        },
        [updateParams],
    );

    const handleUserChange = useCallback(
        (value: ListBoxItemData | ListBoxItemData[] | undefined) => {
            if (Array.isArray(value)) {
                return;
            }

            const ownerId = value?.id ? Number(value.id) : undefined;

            updateParams({ ownerId });
        },
        [updateParams],
    );

    const handleYearChange = useCallback(
        (year: ListBoxItemData) => {
            updateParams({ taskYear: year.value });
        },
        [updateParams],
    );

    useEffect(() => {
        const userId = Number(parsedQueryParams.ownerId);

        const ownerId = Number.isNaN(userId) ? undefined : userId;

        if (ownerId) {
            loadOwner(ownerId);
        } else {
            customTasksUsersQuery.unload();
        }
    }, [parsedQueryParams.ownerId, loadOwner, customTasksUsersQuery]);

    useEffect(() => {
        const yearValue = Number(defaultYear?.value);
        const year = Number.isNaN(yearValue) ? undefined : yearValue;

        const types = isEmpty(defaultTypes)
            ? undefined
            : defaultTypes?.map((option) => option.value);

        const ownerValue = Number(owner?.id);
        const ownerId = Number.isNaN(ownerValue) ? undefined : ownerValue;

        setFilters({ year, types, ownerId });
    }, [setFilters, defaultYear, defaultTypes, owner]);

    const showMonth = useCallback(
        (month: number) => updateParams({ [TASK_MONTH_PARAM]: month }),
        [updateParams],
    );

    return (
        <Box
            borderRadius="borderRadiusLg"
            borderWidth="borderWidthSm"
            borderColor="neutralBorderFaded"
            overflow="auto"
            data-id="QiIlFBGG"
        >
            <Stack direction="column" p="xl" gap="xl">
                <ComboboxField
                    isMultiSelect
                    key={`task-type-${defaultTypes?.map((option) => option.value).join('-')}`}
                    formId={TASKS_FILTER_ID}
                    name="TYPE"
                    label={t`Type`}
                    placeholder={t`All types`}
                    loaderLabel={t`Loading...`}
                    removeAllSelectedItemsLabel={t`Remove all`}
                    data-id={`${TASKS_FILTER_ID}-typeField`}
                    options={TASKS_TYPE_OPTIONS}
                    defaultSelectedOptions={defaultTypes}
                    getSearchEmptyState={constant(t`No types found`)}
                    getRemoveIndividualSelectedItemClickLabel={({
                        itemLabel,
                    }) => t`Remove ${itemLabel}`}
                    onChange={handleTypeChange}
                />

                <ComboboxField
                    key={`task-owner-${owner?.id}`}
                    formId={TASKS_FILTER_ID}
                    name="OWNER"
                    label={t`Owner`}
                    placeholder={t`All owners`}
                    loaderLabel={t`Loading personnel...`}
                    data-id={`${TASKS_FILTER_ID}-ownerField`}
                    defaultValue={owner}
                    options={customTasksUsers}
                    clearSelectedItemButtonLabel={t`Clear`}
                    hasMore={hasMoreUsers}
                    isLoading={isFetchingUsers || isLoadingUsers}
                    getSearchEmptyState={constant(t`No personnel found`)}
                    onFetchOptions={onFetchUsers}
                    onChange={handleUserChange}
                />

                {!hasErrorInTasksYearRange && (
                    <SelectField
                        key={`task-year-${defaultYear?.label}`}
                        formId={TASKS_FILTER_ID}
                        name="YEAR"
                        label={t`Year`}
                        loaderLabel={t`Loading...`}
                        value={undefined}
                        options={yearOptions}
                        defaultValue={defaultYear}
                        onChange={handleYearChange}
                    />
                )}

                {!hasErrorInTasksOverview && (
                    <Stack direction="column">
                        {getTasksMonthLinks(tasksOverview).map(
                            ({ month, count, overdueCount }) => (
                                <TasksLink
                                    key={month}
                                    label={getMonthLabel(month)}
                                    count={count}
                                    overdueCount={overdueCount}
                                    data-id="Sb3v_kRv"
                                    onClick={() => showMonth(month)}
                                />
                            ),
                        )}
                    </Stack>
                )}
            </Stack>
        </Box>
    );
});
