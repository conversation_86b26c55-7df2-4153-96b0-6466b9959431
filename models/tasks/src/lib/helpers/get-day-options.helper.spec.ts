import { describe, expect, test } from 'vitest';
import { getDayOptions } from './get-day-options.helper';

describe('getDayOptions', () => {
    test('returns array with correct length (32 items - days 1-31 plus LAST)', () => {
        const result = getDayOptions();

        expect(result).toHaveLength(32);
    });

    test('generates correct options for first, middle, and last numeric days', () => {
        const result = getDayOptions();

        // First day
        expect(result[0]).toStrictEqual({
            id: '1',
            label: 'Day 1',
            value: '1',
        });

        // Middle day (15)
        expect(result[14]).toStrictEqual({
            id: '15',
            label: 'Day 15',
            value: '15',
        });

        // Last numeric day (31)
        expect(result[30]).toStrictEqual({
            id: '31',
            label: 'Day 31',
            value: '31',
        });
    });

    test('includes "Last day" option as the final item', () => {
        const result = getDayOptions();
        const lastOption = result[result.length - 1];

        expect(lastOption).toStrictEqual({
            id: 'LAST',
            label: 'Last day',
            value: 'LAST',
        });
    });

    test('all numeric days are properly formatted strings', () => {
        const result = getDayOptions();
        const numericDays = result.slice(0, 31);

        const numericDaysList = numericDays.filter(
            (
                option,
            ): option is { id: string; label: string; value: string } => {
                return 'id' in option && 'label' in option && 'value' in option;
            },
        );

        numericDaysList.forEach((option, index) => {
            const expectedDay = String(index + 1);

            expect(option.id).toBe(expectedDay);
            expect(option.value).toBe(expectedDay);
            expect(option.label).toBe(`Day ${expectedDay}`);
        });
    });

    test('multiple calls return different array instances with same values', () => {
        const firstCall = getDayOptions();
        const secondCall = getDayOptions();

        expect(firstCall).not.toBe(secondCall);
        expect(firstCall).toStrictEqual(secondCall);
    });
});
