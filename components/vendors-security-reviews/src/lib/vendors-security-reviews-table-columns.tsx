import {
    type DatatableProps,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import type { VendorSecurityReviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { VendorSecurityReviewsActionsCellComponent } from './vendors-security-reviews-actions-cell-component';
import { VendorSecurityReviewsCompletionDateCellComponent } from './vendors-security-reviews-completion-date-cell-component';
import { VendorSecurityReviewsNameCellComponent } from './vendors-security-reviews-name-cell-component';
import { VendorSecurityReviewsReviewerCellComponent } from './vendors-security-reviews-reviewer-cell-component';
import { VendorsSecurityReviewsStatusCellComponent } from './vendors-security-reviews-status-cell-component';

export const getVendorsSecurityReviewsTableColumns =
    (): DatatableProps<VendorSecurityReviewResponseDto>['columns'] => [
        {
            id: 'SECURITY_REVIEW_NAME',
            header: t`Security review`,
            enableSorting: true,
            accessorKey: 'requestedAt',
            cell: VendorSecurityReviewsNameCellComponent,
        },
        {
            id: 'SECURITY_REVIEW_STATUS',
            header: t`Status`,
            enableSorting: false,
            accessorKey: 'status',
            cell: VendorsSecurityReviewsStatusCellComponent,
        },
        {
            id: 'SECURITY_REVIEW_COMPLETION_DATE',
            header: t`Completion date`,
            enableSorting: false,
            accessorKey: 'reviewDeadlineAt',
            cell: VendorSecurityReviewsCompletionDateCellComponent,
        },
        {
            id: 'SECURITY_REVIEW_REVIEWER',
            header: t`Reviewer`,
            enableSorting: false,
            accessorKey: 'user',
            cell: VendorSecurityReviewsReviewerCellComponent,
        },
        {
            id: 'SECURITY_REVIEW_ACTIONS',
            enableSorting: false,
            cell: VendorSecurityReviewsActionsCellComponent,
            meta: {
                shouldIgnoreRowClick: true,
            },
            isActionColumn: true,
        },
    ];

/**
 * Not as const, VENDORS_SECURITY_REVIEWS_DEFAULT_SORTING must be mutable.
 */
export const VENDORS_SECURITY_REVIEWS_DEFAULT_SORTING = [
    {
        id: 'REQUEST_DATE',
        desc: true,
    },
];

/**
 * Not as const, VENDORS_SECURITY_REVIEWS_DEFAULT_PAGINATION_OPTIONS must be mutable.
 */
export const VENDORS_SECURITY_REVIEWS_DEFAULT_PAGINATION_OPTIONS = {
    pageIndex: 0,
    pageSize: DEFAULT_PAGE_SIZE,
    pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
};
