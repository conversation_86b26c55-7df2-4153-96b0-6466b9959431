import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { panelController } from '@controllers/panel';
import type { CustomerRequestEvidenceResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { openControlDetailsPanelPaginated } from '../helpers/open-control-details-panel.helper';

class ControlDetailsPanelController {
    constructor() {
        makeAutoObservable(this);
    }

    get currentControlId(): number | undefined {
        return panelController.queryParams.controlId as number | undefined;
    }

    get customerRequestEvidences(): CustomerRequestEvidenceResponseDto[] {
        return sharedCustomerRequestDetailsController.customerRequestEvidences;
    }

    get currentControlIndex(): number {
        return this.customerRequestEvidences.findIndex(
            (control) => Number(control.id) === this.currentControlId,
        );
    }

    get currentControlData(): CustomerRequestEvidenceResponseDto {
        return this.customerRequestEvidences[this.currentControlIndex];
    }

    get hasValidControlData(): boolean {
        return Boolean(this.currentControlData);
    }

    loadControlEvidences = (): void => {
        if (!this.currentControlId) {
            return;
        }
        sharedCustomerRequestDetailsController.loadControlEvidences(
            this.currentControlId,
        );
    };

    get canNavigateNext(): boolean {
        return (
            this.currentControlIndex < this.customerRequestEvidences.length - 1
        );
    }

    get canNavigatePrev(): boolean {
        return this.currentControlIndex > 0;
    }

    get totalControls(): number {
        return this.customerRequestEvidences.length;
    }

    get currentControlPosition(): number {
        return this.currentControlIndex + 1;
    }

    handleClosePanel = (): void => {
        panelController.closePanel();
    };

    handleNextPage = (): void => {
        if (!this.canNavigateNext) {
            return;
        }

        const nextControl =
            this.customerRequestEvidences[this.currentControlIndex + 1];

        openControlDetailsPanelPaginated(Number(nextControl.id), 1);
    };

    handlePrevPage = (): void => {
        if (!this.canNavigatePrev) {
            return;
        }

        const prevControl =
            this.customerRequestEvidences[this.currentControlIndex - 1];

        openControlDetailsPanelPaginated(Number(prevControl.id), -1);
    };
}

export const sharedControlDetailsPanelController =
    new ControlDetailsPanelController();
