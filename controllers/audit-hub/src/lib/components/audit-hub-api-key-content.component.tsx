import { Banner } from '@cosmos/components/banner';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Stack } from '@cosmos/components/stack';
import { CopyField } from '@cosmos-lab/components/copy-field';
import { t } from '@globals/i18n/macro';
import type { ApiKeyContentProps } from '../types';

export const ApiKeyContent = ({
    apiKey,
    isConfirmed,
    onConfirmationChange,
}: ApiKeyContentProps): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="xl"
            data-testid="ApiKeyContent"
            data-id="n9j6UY3V"
        >
            <Banner
                severity="warning"
                title={t`Copy your API key now`}
                body={t`You cannot recover this information. If you lose or forget this information you will need to create a new API key.`}
                data-id="api-key-warning-banner"
            />
            <CopyField
                label={t`API Key`}
                value={apiKey}
                formId="api-key-modal-form"
                name="apiKey"
                data-id="api-key-copy-field"
            />
            <CheckboxField
                label={t`I have saved my API key in a secure location`}
                checked={isConfirmed}
                formId="api-key-modal-form"
                name="apiKeyConfirmation"
                value="confirmed"
                data-id="api-key-confirmation-checkbox"
                onChange={onConfirmationChange}
            />
        </Stack>
    );
};
