import { LoginEmailSentComponent } from '@components/login';
import { sharedAuthController } from '@controllers/auth';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import type { Regions } from '@globals/config';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { Form, type FormSchema, useFormSubmit } from '@ui/forms';

const AUDITOR_LOGIN_FORM_ID = 'auditor-login-form';

export const LoginAuditorView = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { email, isAttemptingLogin, hasAttemptedLogin } =
        sharedAuthController;

    const FORM_SCHEMA: FormSchema = {
        email: {
            type: 'text',
            initialValue: email || '',
            label: t`Email address`,
        },
    };

    return (
        <Stack
            gap="2xl"
            direction="column"
            data-testid="LoginAuditorView"
            data-id="954t0RNZ"
        >
            <Stack gap="4x" direction="column">
                {isAttemptingLogin && <Text>{t`Attempting login...`}</Text>}
                {hasAttemptedLogin && email && (
                    <LoginEmailSentComponent
                        email={email}
                        onGoBack={() => {
                            sharedAuthController.logout();
                        }}
                    />
                )}
                {!isAttemptingLogin && !hasAttemptedLogin && (
                    <>
                        <Box>
                            <Text size="400">{t`Welcome auditors!`}</Text>
                        </Box>
                        <Stack my="4x" gap="xl" direction="column">
                            <Button
                                data-id="login-auditor-sign-in-google-button"
                                startIconName="Google"
                                colorScheme="neutral"
                                width="full-width"
                                label={t`Sign in with Google`}
                                onClick={() => {
                                    /* TODO: Implement google login https://drata.atlassian.net/browse/ENG-62811 */
                                    alert(`Google login clicked`);
                                }}
                            />
                            <Button
                                data-id="login-auditor-sign-in-microsoft-button"
                                startIconName="Microsoft"
                                colorScheme="neutral"
                                width="full-width"
                                label={t`Sign in with Microsoft`}
                                onClick={() => {
                                    /* TODO: Implement google login https://drata.atlassian.net/browse/ENG-62811  */
                                    alert(`Microsoft login clicked`);
                                }}
                            />
                        </Stack>

                        <Stack gap="xl" direction="row" align="center">
                            <Box flexGrow="1">
                                <Divider orientation="horizontal" size="sm" />
                            </Box>
                            <Box px="md">
                                <Text
                                    as="p"
                                    type="body"
                                    size="200"
                                    colorScheme="neutral"
                                >
                                    {t`or`}
                                </Text>
                            </Box>
                            <Box flexGrow="1">
                                <Divider orientation="horizontal" size="sm" />
                            </Box>
                        </Stack>

                        <Form
                            hasExternalSubmitButton
                            formId={AUDITOR_LOGIN_FORM_ID}
                            schema={FORM_SCHEMA}
                            data-id="auditor-login-form"
                            ref={formRef}
                            onSubmit={(values) => {
                                sharedAuthController.updateAuthMode('AUDITOR');
                                sharedAuthController.updateAuthType(
                                    'MAGIC_LINK',
                                );
                                sharedAuthController.attemptLogin(
                                    values.email as string,
                                    // Force region for now.
                                    'NA' as Regions,
                                );
                            }}
                        />
                        <Box pt="4x">
                            <Button
                                type="button"
                                label={t`Sign in`}
                                data-id="auditor-login-form-submit"
                                width="auto"
                                onClick={triggerSubmit}
                            />
                        </Box>
                        <Divider />
                        <Stack gap="4x" direction="row" pt="4x">
                            <Text
                                as="p"
                                type="body"
                                size="200"
                                colorScheme="neutral"
                            >
                                {t`Are you a customer?`}
                            </Text>
                            <AppLink
                                href="/auth/login"
                                label={t`Go to customer login`}
                            />
                        </Stack>
                    </>
                )}
            </Stack>
        </Stack>
    );
});
