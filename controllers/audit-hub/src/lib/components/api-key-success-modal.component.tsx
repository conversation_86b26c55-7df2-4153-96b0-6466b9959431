import { useState } from 'react';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import type { ApiKeySuccessModalProps } from '../types';
import { ApiKeyContent } from './audit-hub-api-key-content.component';

const API_KEY_SUCCESS_MODAL_ID = 'api-key-success-modal';

export const ApiKeySuccessModal = ({
    apiKey,
    onClose,
}: ApiKeySuccessModalProps): React.JSX.Element => {
    const [isConfirmed, setIsConfirmed] = useState(false);

    const handleClose = () => {
        modalController.closeModal(API_KEY_SUCCESS_MODAL_ID);
        onClose();
    };

    const handleConfirmationChange = (checked: boolean) => {
        setIsConfirmed(checked);
    };

    return (
        <>
            <Modal.Header
                title={t`Your new API Key is ready`}
                size="md"
                closeButtonAriaLabel={t`Close API Key Success Modal`}
                onClose={() => {
                    if (isConfirmed) {
                        handleClose();
                    }
                }}
            />
            <Modal.Body>
                <ApiKeyContent
                    apiKey={apiKey}
                    isConfirmed={isConfirmed}
                    data-id="api-key-content"
                    onConfirmationChange={handleConfirmationChange}
                />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Done`,
                        level: 'primary',
                        cosmosUseWithCaution_isDisabled: !isConfirmed,
                        onClick: handleClose,
                    },
                ]}
            />
        </>
    );
};
