import { AppDatatable } from '@components/app-datatable';
import { openMapAutomatedTestModal } from '@components/map-tests-modal';
import { sharedMonitorsController } from '@controllers/monitors';
import { panelController } from '@controllers/panel';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { MonitoringDetailsPanelComponent } from '../components/monitor-details-panel.component';
import { getControlsMonitoringColumns } from '../constants/columns.constant';
import { FORM_ID, MAP_TESTS_MODAL_ID } from '../constants/modal.constant';
import { canManageControlTests } from '../helpers/can-manage-control-tests.helpers';
import { openControlsMonitoringCompareDefaultModal } from '../helpers/controls-monitoring-compare-default-modal.helper';
import { mapSelectedTests } from '../helpers/map-selected-tests.helper';
import { sharedControlsMonitoringActionsModel } from '../models/controls-monitoring-actions.model';
import { sharedMonitoringDetailsPanelModel } from '../models/monitoring-details-panel.model';

const openMonitoringPanel = action((testId: number): void => {
    sharedMonitoringDetailsPanelModel.loadPanelInfo(testId);

    panelController.openPanel({
        id: 'monitoring-panel',
        content: () => <MonitoringDetailsPanelComponent data-id="r4-3_HsY" />,
    });
});

export const ControlsMonitoringView = observer((): React.JSX.Element => {
    const { monitors, isLoading, monitorsTotal } = sharedMonitorsController;
    const { tableActions } = sharedControlsMonitoringActionsModel;

    return (
        <Stack gap="4x" direction="column" data-id="36rgNcba" align="end">
            <AppDatatable
                tableId="datatable-monitoring-list"
                data-testid="ControlsMonitoringView"
                data-id="cLwGEtjx"
                isLoading={isLoading}
                total={monitorsTotal}
                data={monitors}
                columns={getControlsMonitoringColumns()}
                tableActions={tableActions}
                emptyStateProps={{
                    title: t`Continuously monitor the effectiveness of your control`,
                    description: t`See a history of how tests mapped to this control have performed over time.`,
                    illustrationName: 'MonitoringTest',
                    leftAction: canManageControlTests() && (
                        <Button
                            label={t`Map tests`}
                            colorScheme="primary"
                            size="md"
                            onClick={() => {
                                const { controlId } = sharedMonitorsController;

                                openMapAutomatedTestModal(
                                    FORM_ID,
                                    MAP_TESTS_MODAL_ID,
                                    mapSelectedTests,
                                    controlId,
                                );
                            }}
                        />
                    ),
                    rightAction: canManageControlTests() && (
                        <Button
                            label={t`Compare to defaults`}
                            colorScheme="neutral"
                            level="secondary"
                            size="md"
                            onClick={openControlsMonitoringCompareDefaultModal}
                        />
                    ),
                }}
                onRowClick={({ row }) => {
                    openMonitoringPanel(row.testId);
                }}
                onFetchData={(params) => {
                    sharedMonitorsController.loadMonitors(undefined, params);
                }}
            />
        </Stack>
    );
});
