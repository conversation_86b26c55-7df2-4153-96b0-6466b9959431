import type { AuditorDataType } from '@controllers/audit-hub-auditor-client-audit';
import type { Datatable, DatatableProps } from '@cosmos/components/datatable';
import {
    dimension6x,
    dimension10x,
    dimension16x,
    dimension32x,
    dimension64x,
} from '@cosmos/constants/tokens';
import { DIMENSION_VALUES } from '@cosmos-lab/components/data-donut';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ActionsCell } from './requests-table-cells/actions-cell';
import { CellTable } from './requests-table-cells/cell-table';
import { ControlsCell } from './requests-table-cells/controls-cell';
import { MessagesCell } from './requests-table-cells/messages-cell';
import { StatusCell } from './requests-table-cells/status-cell';

export const getAuditHubAuditorsListTableColumns =
    (): DatatableProps<CustomerRequestListItemResponseDto>['columns'] => [
        {
            accessorKey: 'id',
            header: '',
            id: 'actions',
            enableSorting: false,
            enableHiding: true,
            cell: ActionsCell,
            isActionColumn: true,
            maxSize: DIMENSION_VALUES[dimension6x],
        },
        {
            accessorKey: 'unreadMessages',
            header: t`Message`,
            id: 'message',
            enableSorting: false,
            cell: MessagesCell,
            size: DIMENSION_VALUES[dimension10x],
        },
        {
            accessorKey: 'status',
            header: t`Status`,
            id: 'status',
            enableSorting: true,
            cell: StatusCell,
            size: DIMENSION_VALUES[dimension16x],
        },
        {
            accessorKey: 'code',
            header: t`ID`,
            id: 'code',
            enableSorting: true,
            size: DIMENSION_VALUES[dimension32x],
        },
        {
            accessorKey: 'title',
            header: t`Title`,
            id: 'title',
            enableSorting: true,
            size: DIMENSION_VALUES[dimension64x],
        },
        {
            accessorKey: 'controls',
            header: t`Controls`,
            id: 'controls',
            enableSorting: true,
            cell: ControlsCell,
            size: DIMENSION_VALUES[dimension64x],
        },
    ];

export const ASSIGNED_AUDITORS_COLUMNS = [
    {
        id: 'name',
        size: 100,
        isActionColumn: false,
        cell: CellTable,
        meta: {
            shouldIgnoreRowClick: true,
        },
        accessorFn: ({
            avatarUrl,
            content,
            id,
        }: AuditorDataType): AuditorDataType => {
            return { avatarUrl, content, id };
        },
    },
];

export function getTableSearchProps(): {
    isEnabled: boolean;
    placeholder: string;
    defaultValue: string;
} {
    return {
        isEnabled: true,
        placeholder: t`Search`,
        defaultValue: '',
    };
}

export const FILTER_VIEW_MODE_PROPS: DatatableProps<
    typeof Datatable
>['filterViewModeProps'] = {
    props: {
        selectedOption: 'unpinned',
        initialSelectedOption: 'unpinned',
        togglePinnedLabel: 'Pin filters to page',
        toggleUnpinnedLabel: 'Move filters to dropdown',
    },
    viewMode: 'toggleable',
};

export function getEmptyStateProps(): {
    title: string;
    description: string;
} {
    return {
        title: t`We couldn't find any matches`,
        description: t`Try expanding your filter or search criteria.`,
    };
}
