import { isNil } from 'lodash-es';
import { sharedAccessReviewPeriodApplicationController } from '@controllers/access-reviews';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerUnlinkReviewPeriodEvidenceMutation,
    accessReviewApplicationControllerUploadReviewPeriodEvidenceMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

class AccessReviewApplicationEvidenceController {
    constructor() {
        makeAutoObservable(this);
    }

    accessReviewApplicationEvidenceRemover = new ObservedMutation(
        accessReviewApplicationControllerUnlinkReviewPeriodEvidenceMutation,
    );

    accessReviewApplicationEvidenceUploader = new ObservedMutation(
        accessReviewApplicationControllerUploadReviewPeriodEvidenceMutation,
    );

    periodId: number | null = null;
    applicationId: number | null = null;

    setPeriodId(id: number) {
        this.periodId = id;
    }

    setApplicationId(id: number) {
        this.applicationId = id;
    }

    get isUploading(): boolean {
        return this.accessReviewApplicationEvidenceUploader.isPending;
    }

    uploadEvidence = (data: FormData[], onClose: () => void): void => {
        if (isNil(this.periodId) || isNil(this.applicationId)) {
            throw new Error('Period ID and App ID are required');
        }

        if (isNil(data)) {
            return;
        }

        this.accessReviewApplicationEvidenceUploader
            .mutateAsync({
                body: {
                    'files[]': data.map((file) => file.get('file') as File),
                },
                path: {
                    periodId: this.periodId,
                    reviewAppId: this.applicationId,
                },
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: 'access-review-upload-evidence-success',
                    hasTimeout: true,
                    props: {
                        title: t`Evidence uploaded successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
                onClose();
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `upload-evidence-error`,
                    props: {
                        title: t`Unable to upload evidence`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            })
            .finally(() => {
                sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplication.invalidate();
            });
    };

    removeEvidence = (evidenceId: number): void => {
        if (isNil(this.periodId) || isNil(this.applicationId)) {
            throw new Error('Period ID and App ID are required');
        }

        this.accessReviewApplicationEvidenceRemover
            .mutateAsync({
                path: {
                    periodId: this.periodId,
                    reviewAppId: this.applicationId,
                    evidenceId,
                },
            })
            .then(() => {
                sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplication.invalidate();
                snackbarController.addSnackbar({
                    id: `delete-evidence-success`,
                    props: {
                        title: t`Evidence deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `delete-evidence-error`,
                    props: {
                        title: t`Unable to delete evidence`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            });
    };
}

export const sharedAccessReviewApplicationEvidenceController =
    new AccessReviewApplicationEvidenceController();
