import { AppDatatable } from '@components/app-datatable';
import { sharedAuditHubSettingsController } from '@controllers/audit-hub';
import { Button } from '@cosmos/components/button';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import { sharedConfigController } from '@globals/config';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { getApiKeysTableColumns } from '../constants/table-columns.constant';
import { openCreateApiKeyModal } from '../helpers/open-create-api-key-modal';

export const AuditorSettingsProfileView = observer((): JSX.Element => {
    const {
        loadApiKeys,
        auditorApiKeysIsLoading,
        auditorApiKeysTotal,
        auditorApiKeys,
    } = sharedAuditHubSettingsController;

    const { configs } = sharedConfigController;
    const apiDocsUrl = configs.url?.publicApiDocumentation;

    const emptyStateProps: EmptyStateProps = {
        illustrationName: 'ApiKey',
        imageSize: 'md',
        title: t`Ready to build your first custom integration with Drata?`,
        description: t`Generate a new API key to get started.`,
        rightAction: apiDocsUrl && (
            <AppLink isExternal href={apiDocsUrl}>
                {t`View API documentation`}
            </AppLink>
        ),
        leftAction: (
            <Button
                label={t`Create API key`}
                level="primary"
                onClick={openCreateApiKeyModal}
            />
        ),
    };

    return (
        <AppDatatable
            isLoading={auditorApiKeysIsLoading}
            tableId="auditors-apikeys-list"
            total={auditorApiKeysTotal}
            data={auditorApiKeys}
            columns={getApiKeysTableColumns()}
            data-testid="AuditorClientsView"
            data-id="WG5SuueY"
            emptyStateProps={
                auditorApiKeysTotal === 0 ? emptyStateProps : undefined
            }
            tableActions={[
                {
                    actionType: 'button',
                    id: 'view-api-docs-button',
                    typeProps: {
                        label: t`View API documentation`,
                        level: 'tertiary',
                        endIconName: 'LinkOut',
                        href: apiDocsUrl || '#',
                        target: '_blank',
                    },
                },
                {
                    actionType: 'button',
                    id: 'create-api-key-button',
                    typeProps: {
                        label: t`Create API key`,
                        level: 'secondary',
                        onClick: openCreateApiKeyModal,
                    },
                },
            ]}
            tableSearchProps={{
                hideSearch: false,
                placeholder: t`Search by API key name`,
            }}
            onFetchData={loadApiKeys}
        />
    );
});
