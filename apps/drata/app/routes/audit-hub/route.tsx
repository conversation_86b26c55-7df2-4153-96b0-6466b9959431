import { isEmpty } from 'lodash-es';
import { MainAppTopicsNavComponent } from '@components/main-app-topics-nav';
import { sharedAuthController } from '@controllers/auth';
import { routeController } from '@controllers/route';
import { Box } from '@cosmos/components/box';
import { Icon, type IconName } from '@cosmos/components/icon';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { breakpointLg } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import {
    type ClientLoaderFunction,
    Outlet,
    useLocation,
    useNavigate,
} from '@remix-run/react';
import { PageAsideUi } from '@ui/page-aside';
import { ContentNavigationMenu } from '@ui/page-content';
import { PageHeaderUi } from '@ui/page-header';

export const meta: MetaFunction = () => [{ title: t`Audit Hub` }];

export const clientLoader: ClientLoaderFunction = () => {
    return {
        subdomainConfig: {
            id: 'audit-hub',
            userPart: `/audit-hub`,
        },
    };
};

export interface Tab {
    topicPath: string;
    label: string;
    iconName?: IconName;
}

const AuditHub = observer(() => {
    const navigate = useNavigate();
    const location = useLocation();
    const { contentNavItems, layout } = routeController;
    const isSettingsPage = location.pathname.includes('/audit-hub/settings');
    const isCenterLayout = layout?.centered ?? false;

    return (
        <Stack
            direction="column"
            display="flex"
            data-id="QqMJifIh"
            data-testid={isSettingsPage ? 'AuditHubSettings' : 'AuditHub'}
            height="100%"
            width="100%"
            minHeight="0"
        >
            <Box
                backgroundColor="neutralBackgroundNone"
                borderColor="neutralBorderFaded"
                borderWidth="borderWidthSm"
                data-id="Header"
                width="100%"
            >
                <Stack
                    direction="row"
                    align="center"
                    justify="between"
                    p="md"
                    data-id="HeaderNav"
                >
                    <Stack direction="row" align="center" gap="2x">
                        <Icon name="DrataFilled" size="500" />
                        <Text type="title">{t`Audit Hub`}</Text>
                    </Stack>
                    <Stack direction="row" align="center" gap="2x">
                        <SchemaDropdown
                            isIconOnly
                            label={t`User menu`}
                            startIconName="UserCircleSingle"
                            level="tertiary"
                            colorScheme="neutral"
                            data-testid="UserMenu"
                            data-id="user-dropdown"
                            items={[
                                {
                                    id: 'setting',
                                    label: t`Setting`,
                                    onSelect: () => {
                                        navigate('/audit-hub/settings/profile');
                                    },
                                },
                                {
                                    id: 'signout',
                                    label: t`Sign out`,
                                    onSelect: () => {
                                        sharedAuthController.logout();
                                    },
                                },
                            ]}
                        />
                    </Stack>
                </Stack>
            </Box>
            <Stack
                direction="row"
                display="flex"
                height="100%"
                minHeight="0"
                width="100%"
            >
                <Box width="auto">
                    <MainAppTopicsNavComponent />
                </Box>

                <Stack direction="row" width="100%" height="100%">
                    <Stack
                        data-id="domains-flat-child-stack"
                        display="flex"
                        direction="column"
                        height="100%"
                        width="100%"
                        gap={isCenterLayout ? 'lg' : undefined}
                        align={isCenterLayout ? 'center' : undefined}
                        p={isCenterLayout ? '2xl' : '3xl'}
                        overflow="auto"
                        flexGrow="1"
                    >
                        <Box
                            width={isCenterLayout ? '100%' : undefined}
                            maxWidth={isCenterLayout ? breakpointLg : undefined}
                        >
                            <PageHeaderUi />

                            {!isEmpty(contentNavItems) && (
                                <ContentNavigationMenu
                                    data-id="page-header-ui-content-navigation-menu"
                                    value={location.pathname}
                                    items={contentNavItems}
                                />
                            )}
                        </Box>
                        <Box
                            data-id="domains-flat-content-box"
                            height={isCenterLayout ? undefined : '100%'}
                            minHeight={isCenterLayout ? undefined : '0'}
                            width={isCenterLayout ? '100%' : undefined}
                            maxWidth={isCenterLayout ? breakpointLg : undefined}
                            overflow={isCenterLayout ? undefined : 'scroll'}
                            p="3xl"
                        >
                            <Outlet />
                        </Box>
                    </Stack>

                    <PageAsideUi />
                </Stack>
            </Stack>
        </Stack>
    );
});

export default AuditHub;
