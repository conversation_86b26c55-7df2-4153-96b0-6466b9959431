import type { MouseEventHandler, ReactNode } from 'react';
import { styled } from 'styled-components';
import {
    borderRadiusMd,
    borderWidthMd,
    primaryBorderFocus,
} from '@cosmos/constants/tokens';

/**
 * TODO: Copied from banner
 * Create neutral button colorScheme and use it here.
 */
export const CloseButtonWrapper = styled.button`
    // reset
    padding: 0;
    background-color: unset;
    border: unset;
    border-radius: ${borderRadiusMd};
    height: fit-content;
    cursor: pointer;

    &:focus,
    &:focus-visible {
        outline: ${borderWidthMd} solid ${primaryBorderFocus};
    }
`;

export interface ModalCloseProps {
    children: ReactNode;
    onClick: MouseEventHandler<HTMLButtonElement>;
    'data-id'?: string;
    'aria-label'?: string;
}

export const ModalClose = ({
    children,
    onClick,
    'data-id': dataId = undefined,
    'aria-label': ariaLabel,
}: ModalCloseProps): React.JSX.Element => {
    return (
        <CloseButtonWrapper
            data-id={dataId}
            type="button"
            aria-label={ariaLabel || undefined}
            data-testid="ModalClose"
            onClick={onClick}
        >
            {children}
        </CloseButtonWrapper>
    );
};
