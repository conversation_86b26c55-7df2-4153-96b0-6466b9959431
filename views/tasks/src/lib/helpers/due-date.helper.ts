import { isNil } from 'lodash-es';
import { compareDayIsPast } from '@helpers/date-time';

export const isDateBeforeYesterday = (date: string): boolean => {
    if (isNil(date)) {
        return false;
    }

    const dateProvided = new Date(date);

    if (isNaN(dateProvided.getTime())) {
        return false; // Invalid date
    }

    const yesterday = new Date();

    yesterday.setDate(yesterday.getDate() - 1);

    return compareDayIsPast(dateProvided, yesterday);
};
