import { isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerGetApplicationPeriodOptions,
    accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    accessReviewPeriodControllerPostReviewPeriodNoteMutation,
    accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    accessReviewUserControllerDownloadPeriodApplicationNoteFileOptions,
} from '@globals/api-sdk/queries';
import type {
    AccessReviewNoteResponseDto,
    NoteResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class AccessReviewActiveApplicationNotesController {
    activeApplicationQuery = new ObservedQuery(
        accessReviewApplicationControllerGetApplicationPeriodOptions,
    );
    activeNoteCreateMutation = new ObservedMutation(
        accessReviewPeriodControllerPostReviewPeriodNoteMutation,
    );
    activeNoteUpdateMutation = new ObservedMutation(
        accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    );
    activeNoteDeleteMutation = new ObservedMutation(
        accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    );
    activeDownloadAttachmentQuery = new ObservedQuery(
        accessReviewUserControllerDownloadPeriodApplicationNoteFileOptions,
    );

    periodId: string | null = null;
    reviewAppId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get list(): NoteResponseDto[] {
        const mappedNotes = (
            this.activeApplicationQuery.data?.application.notes ?? []
        ).map((note: AccessReviewNoteResponseDto) => {
            return {
                ...note,
                comment: note.message || '',
                createdAt: note.sentAt || '',
                updatedAt: note.sentAt || '',
                owner: {
                    id: note.ownerId,
                    firstName: note.ownerName,
                    lastName: '',
                    avatarUrl: note.ownerAvatar,
                },
                noteFiles: note.files,
            };
        });

        return mappedNotes.map((note) => note) as unknown as NoteResponseDto[];
    }

    get total(): number {
        return this.activeApplicationQuery.data?.application.notes.length ?? 0;
    }

    get isLoading(): boolean {
        return this.activeApplicationQuery.isLoading;
    }

    get applicationReviewStatus(): string | null {
        return this.activeApplicationQuery.data?.application.status ?? null;
    }

    get isReadOnly(): boolean {
        return this.applicationReviewStatus === 'COMPLETE';
    }

    loadNotes = ({
        periodId,
        reviewAppId,
    }: {
        periodId?: string;
        reviewAppId?: string;
    }): void => {
        if (isNil(periodId) || isNil(reviewAppId)) {
            throw new Error(t`Period ID and App ID params are required`);
        }
        this.periodId = periodId;
        this.reviewAppId = reviewAppId;

        this.activeApplicationQuery.load({
            path: {
                periodId: Number(periodId),
                reviewAppId: Number(reviewAppId),
            },
        });
    };

    /**
     * Placeholder methods for future implementation.
     */
    createNote = ({
        comment,
        files,
    }: {
        comment: string;
        files: (File | Blob)[];
    }): void => {
        if (!this.periodId || !this.reviewAppId) {
            throw new Error(t`Period ID and App ID are required`);
        }
        const timestamp = new Date().toISOString();

        this.activeNoteCreateMutation
            .mutateAsync({
                path: {
                    periodId: Number(this.periodId),
                },
                body: {
                    comment,
                    'files[]': files,
                    reviewPeriodApplicationId: this.reviewAppId,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeApplicationQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-application-note-success`,
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-application-note-error`,
                    props: {
                        title: t`Unable to create note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    updateNote = (
        noteId: string,
        {
            comment,
            files,
        }: {
            comment: string;
            files: (File | Blob)[];
        },
    ): void => {
        const timestamp = new Date().toISOString();

        this.activeNoteUpdateMutation
            .mutateAsync({
                path: {
                    noteId,
                    periodId: Number(this.periodId),
                    reviewAppId: Number(this.reviewAppId),
                },
                body: {
                    comment,
                    'files[]': files,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeApplicationQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-application-note-success`,
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-application-note-error`,
                    props: {
                        title: t`Unable to update note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    deleteNote = (noteId: string): void => {
        const timestamp = new Date().toISOString();

        openConfirmationModal({
            title: t`Delete note`,
            body: t`Are you sure you want to delete this note? This action cannot be undone.`,
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.activeNoteDeleteMutation
                    .mutateAsync({
                        path: {
                            noteId,
                            periodId: Number(this.periodId),
                            reviewAppId: Number(this.reviewAppId),
                        },
                    })
                    .then(() => {
                        runInAction(() => {
                            this.activeApplicationQuery.invalidate();
                        });
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-application-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch((error: Error) => {
                        const title =
                            (error as { statusCode?: number }).statusCode ===
                            403
                                ? t`Only the same owner can delete a note`
                                : t`Unable to delete note at the moment`;

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-application-note-error`,
                            props: {
                                title,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    downloadNoteAttachment = (noteFileId: string, noteId: string): void => {
        this.activeDownloadAttachmentQuery.load({
            path: {
                periodId: Number(this.periodId),
                reviewAppId: Number(this.reviewAppId),
            },
            query: {
                noteId,
                fileId: noteFileId,
            },
        });
        when(() => !this.activeDownloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.activeDownloadAttachmentQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedAccessReviewActiveApplicationNotesController =
    new AccessReviewActiveApplicationNotesController();
