import type { FilterProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';

export const getFilterProps = (totalUnreadMessages: number): FilterProps => {
    const messagesLabel =
        totalUnreadMessages > 0 ? t`Unread: ${totalUnreadMessages}` : t`Unread`;

    return {
        clearAllButtonLabel: t`Reset`,
        filters: [
            {
                filterType: 'checkbox',
                id: 'onlyWithNewMessages',
                label: t`Messages`,
                options: [
                    {
                        label: messagesLabel,
                        value: 'true',
                    },
                ],
            },
            {
                filterType: 'radio',
                id: 'status',
                label: t`Status`,
                options: [
                    {
                        label: t`Completed`,
                        value: 'ACCEPTED',
                    },
                    {
                        label: t`Prepared`,
                        value: 'IN_REVIEW',
                    },
                    {
                        label: t`New`,
                        value: 'OUTSTANDING',
                    },
                ],
            },
            {
                filterType: 'radio',
                id: 'isOwned',
                label: t`Request owners`,
                options: [
                    {
                        label: t`Owners assigned`,
                        value: 'true',
                    },
                    {
                        label: t`No owners assigned`,
                        value: 'false',
                    },
                ],
            },
            {
                filterType: 'combobox',
                id: 'filterByUserIds',
                label: t`Filter by request owner`,
                // TODO: Remove this later https://drata.atlassian.net/browse/ENG-70498
                isMultiSelect: true,
                options: [
                    {
                        id: 'EESF',
                        label: 'Arnold Silvester',
                        value: 'EESF',
                    },
                    {
                        id: 'OJSDR',
                        label: 'Sam Macguier',
                        value: 'OJSDR',
                    },
                    {
                        id: 'ADFD',
                        label: 'Luis Souza',
                        value: 'ADFD',
                    },
                ],
            },
        ],
        triggerLabel: t`Filters`,
    };
};
