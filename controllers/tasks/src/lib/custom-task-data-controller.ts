import { customTasksControllerGetCustomTaskOptions } from '@globals/api-sdk/queries';
import type { CustomTaskFullResponseDto } from '@globals/api-sdk/types';
import {
    action,
    computed,
    makeAutoObservable,
    ObservedQuery,
} from '@globals/mobx';
import type { TaskGetParams } from './types/task-get-params.types';

export class CustomTaskDataController {
    taskDataQuery = new ObservedQuery(
        customTasksControllerGetCustomTaskOptions,
    );
    constructor() {
        makeAutoObservable(this, {
            load: action,
            isLoading: computed,
            taskData: computed,
        });
    }
    load(params: TaskGetParams): void {
        const { taskId } = params;

        if (taskId) {
            this.taskDataQuery.load({
                path: {
                    taskId,
                },
            });
        }
        // TODO: handle getting configId
    }

    get isLoading(): boolean {
        return this.taskDataQuery.isLoading;
    }

    get taskData(): CustomTaskFullResponseDto | null {
        return this.taskDataQuery.data;
    }
}
