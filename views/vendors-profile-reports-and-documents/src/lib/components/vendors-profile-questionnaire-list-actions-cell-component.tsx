import {
    sharedVendorsDetailsController,
    sharedVendorsProfileReportsAndDocumentsMutationController,
    sharedVendorsQuestionnairesController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import type { QuestionnaireSentResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';

export const VendorsProfileQuestionnaireListActionsCellComponent = observer(
    ({
        row,
    }: {
        row: { original: QuestionnaireSentResponseDto };
    }): React.JSX.Element => {
        const { isCompleted, responseId, id } = row.original;

        const shouldShowView = isCompleted;

        const canDelete =
            sharedFeatureAccessModel.isVendorEditable && !isCompleted;

        const { isQuestionnaireDownloadPending, downloadQuestionnaire } =
            sharedVendorsProfileReportsAndDocumentsMutationController;

        const handleDownloadQuestionnaire = () => {
            if (isQuestionnaireDownloadPending || !responseId) {
                return;
            }

            downloadQuestionnaire(responseId);
        };

        const handleDeleteQuestionnaire = () => {
            if (!canDelete || !id) {
                return;
            }

            sharedVendorsQuestionnairesController.deleteQuestionnaire(id);
        };

        const buildQuestionnaireDetailsUrl = (): string | null => {
            const { vendorDetails } = sharedVendorsDetailsController;
            const { currentWorkspace } = sharedWorkspacesController;

            if (!vendorDetails?.id || !currentWorkspace?.id || !responseId) {
                return null;
            }

            const vendorType =
                vendorDetails.status === 'PROSPECTIVE'
                    ? 'prospective'
                    : 'current';

            return `/workspaces/${currentWorkspace.id}/vendors/${vendorType}/${vendorDetails.id}/reports-and-documents/questionnaires/${responseId}`;
        };

        const questionnaireDetailsUrl = buildQuestionnaireDetailsUrl();

        // For incomplete questionnaires, show only delete button (if user has permission)
        if (!isCompleted) {
            return (
                <Stack
                    data-testid="VendorsProfileQuestionnaireListActionsCellComponent"
                    data-id="7xbJM0NT"
                    justify="end"
                >
                    {canDelete && (
                        <Button
                            isIconOnly
                            label={t`Delete`}
                            colorScheme="danger"
                            startIconName="Trash"
                            level="tertiary"
                            onClick={handleDeleteQuestionnaire}
                        />
                    )}
                </Stack>
            );
        }

        // For completed questionnaires, show view (conditionally) and download buttons
        return (
            <Stack
                gap="3x"
                data-testid="VendorsProfileQuestionnaireListActionsCellComponent"
                data-id="7xbJM0NT"
                justify="end"
                align="center"
            >
                {shouldShowView && questionnaireDetailsUrl && (
                    <AppLink
                        href={questionnaireDetailsUrl}
                        label={t`View`}
                        data-testid="VendorsProfileQuestionnaireListActionsCellComponent-view-link"
                        data-id="questionnaire-view-link"
                    />
                )}
                <Button
                    isIconOnly
                    label={t`Download`}
                    colorScheme="neutral"
                    startIconName="Download"
                    level="tertiary"
                    cosmosUseWithCaution_isDisabled={
                        isQuestionnaireDownloadPending
                    }
                    onClick={handleDownloadQuestionnaire}
                />
            </Stack>
        );
    },
);
