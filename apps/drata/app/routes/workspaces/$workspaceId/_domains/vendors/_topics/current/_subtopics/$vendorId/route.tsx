import type { ClientLoader } from '@app/types';
import {
    sharedVendorsDetailsController,
    sharedVendorsFeatureDismissalController,
    sharedVendorsProfileReportsAndDocumentsController,
    sharedVendorsProfileRisksController,
    sharedVendorsProfileSecurityReviewsController,
    sharedVendorsRisksController,
    sharedVendorsSchedulesQuestionnairesController,
    sharedVendorsSettingsController,
    VENDORS_DETAILS_TABS_DEFAULT_PARAMS,
} from '@controllers/vendors';
import { action, when } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import {
    VendorsProfilePageHeaderModel,
    VendorsProfileTabsModel,
} from '@models/vendors-profile';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const clientLoader = action(
    ({ params, request }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId } = params;

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsDetailsController.loadVendorDetails(parseInt(vendorId));
        sharedVendorsSettingsController.loadVendorsSettings();
        sharedVendorsRisksController.loadVendorRisks();
        sharedVendorsProfileRisksController.setCurrentVendorId(
            Number(vendorId),
        );

        // Reactively load additional data when vendor details become available
        when(
            () => Boolean(sharedVendorsDetailsController.vendorDetails?.id),
            () => {
                const vendorDetailsId =
                    sharedVendorsDetailsController.vendorDetails?.id;

                if (!vendorDetailsId) {
                    return;
                }

                // Load security reviews, documents and questionnaires data
                sharedVendorsProfileSecurityReviewsController.loadPaginatedSecurityReviews(
                    VENDORS_DETAILS_TABS_DEFAULT_PARAMS,
                );
                sharedVendorsProfileReportsAndDocumentsController.loadPaginatedDocuments(
                    VENDORS_DETAILS_TABS_DEFAULT_PARAMS,
                );
                sharedVendorsProfileReportsAndDocumentsController.loadPaginatedQuestionnaires(
                    VENDORS_DETAILS_TABS_DEFAULT_PARAMS,
                );

                // Load feature dismissal state for the vendor
                sharedVendorsFeatureDismissalController.loadFeatureDismissals(
                    vendorDetailsId,
                    'SCHEDULE_QUESTIONNAIRE',
                );

                // Load schedule questionnaires to check if vendor has existing configurations
                sharedVendorsSchedulesQuestionnairesController.loadSchedulesVendorQuestionnaires(
                    {
                        vendorIds: [vendorDetailsId],
                    },
                );
            },
        );

        const parentHref = new URL(getParentRoute(request.url, 2)).pathname;

        const pageHeaderModel = new VendorsProfilePageHeaderModel();
        const tabsModel = new VendorsProfileTabsModel();

        pageHeaderModel.setParentUrl(parentHref);
        tabsModel.setVendorId(Number(vendorId));
        tabsModel.setVendorType('current');

        return {
            pageHeader: pageHeaderModel,
            contentNav: tabsModel,
        };
    },
);

const VendorsCurrent = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrent"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrent;
