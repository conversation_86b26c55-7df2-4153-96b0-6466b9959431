import { isNil } from 'lodash-es';
import { openAccessReviewActiveUpdatePersonnelModal } from '@components/access-review-empty-state';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Text } from '@cosmos/components/text';
import {
    criticalBackgroundStrongInitial,
    successBackgroundModerate,
} from '@cosmos/constants/tokens';
import {
    DataDonut,
    type DataDonutSliceData,
} from '@cosmos-lab/components/data-donut';
import { DateTime } from '@cosmos-lab/components/date-time';
import type {
    AccessReviewApplicationDetailsResponseDto,
    AccessReviewApplicationSummaryResponseDto,
    ClientTypeEnum,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { providers } from '@globals/providers';
import {
    determineModalToShow,
    openAppropriateModal,
} from '@views/access-review-application-period-personnel';

const getApplicationClientType = (
    application: AccessReviewApplicationDetailsResponseDto['application'],
): ClientTypeEnum | null | undefined =>
    application.clientType || application.connections[0]?.clientType;

const PROVIDER_LOOKUP = Object.freeze(
    Object.fromEntries(
        Object.values(providers).map((provider) => [provider.id, provider]),
    ) as Record<string, (typeof providers)[keyof typeof providers]>,
);

const getProviderByClientType = (
    clientType: AccessReviewApplicationDetailsResponseDto['application']['clientType'],
): (typeof providers)[keyof typeof providers] | null => {
    if (isNil(clientType)) {
        return null;
    }

    return PROVIDER_LOOKUP[clientType] ?? null;
};

const getStatusTag = (
    application: AccessReviewApplicationDetailsResponseDto['application'],
): KeyValuePairProps['value'] => {
    switch (application.status) {
        case 'PREPARING': {
            return (
                <Metadata
                    label={t`Syncing`}
                    type="status"
                    colorScheme="education"
                    iconName="Circle"
                />
            );
        }
        case 'NOT_STARTED': {
            return (
                <Metadata
                    label={t`Not started`}
                    type="status"
                    colorScheme="neutral"
                    iconName="Circle"
                />
            );
        }
        case 'IN_PROGRESS': {
            return (
                <Metadata
                    label={t`In progress`}
                    type="status"
                    colorScheme="neutral"
                    iconName="InProgress"
                />
            );
        }
        case 'COMPLETED': {
            return (
                <Metadata
                    label={t`Completed`}
                    type="status"
                    colorScheme="success"
                    iconName="CheckCircle"
                />
            );
        }
        case 'OVERDUE': {
            return (
                <Metadata
                    label={t`Overdue`}
                    type="status"
                    colorScheme="critical"
                    iconName="WarningDiamond"
                />
            );
        }
        case 'NEEDS_REVISION': {
            return (
                <Metadata
                    label={t`Needs review`}
                    type="status"
                    colorScheme="warning"
                    iconName="WarningDiamond"
                />
            );
        }
        default: {
            return (
                <Metadata
                    label={t`Deleted`}
                    type="status"
                    colorScheme="critical"
                    iconName="WarningDiamond"
                />
            );
        }
    }
};

const getStatusKVP = (
    application: AccessReviewApplicationDetailsResponseDto['application'],
): KeyValuePairProps => {
    return {
        id: 'status',
        type: 'REACT_NODE',
        label: t`Status`,
        value: getStatusTag(application),
    };
};

const getDonutValues = (
    completed: number,
    notCompleted: number,
): DataDonutSliceData[] & { length: 1 | 2 } => {
    const ONE_HUNDRED = 100;

    if (completed === 0 && notCompleted === 0) {
        return [
            {
                label: t`completed`,
                value: ONE_HUNDRED,
                color: criticalBackgroundStrongInitial,
            },
        ];
    }

    return [
        {
            label: t`Not completed`,
            value: completed,
            color: successBackgroundModerate,
        },
        {
            label: t`Not completed`,
            value: notCompleted,
            color: criticalBackgroundStrongInitial,
        },
    ];
};

const getProgressKVP = (
    summary: AccessReviewApplicationSummaryResponseDto | null,
    totalUsers: number,
): KeyValuePairProps => {
    const progressValues = {
        notReviewed: 0,
        rejected: 0,
        approved: 0,
        outOfScope: 0,
    };

    const getTotalCount = () => {
        if (isNil(summary)) {
            return 0;
        }
        const {
            notReviewed: notRev = 0,
            rejected: reject = 0,
            approved: approve = 0,
            outOfScope: unscope = 0,
        } = summary;

        progressValues.notReviewed = notRev;
        progressValues.rejected = reject;
        progressValues.approved = approve;
        progressValues.outOfScope = unscope;

        return notRev + reject + approve + unscope;
    };

    const total = getTotalCount();

    const { notReviewed, rejected, approved, outOfScope } = progressValues;

    const completeTasks = approved + rejected + outOfScope;

    const getCompleted =
        totalUsers < 1
            ? 0
            : Math.ceil(total ? (completeTasks * 100) / total : 100);

    const donutValues = getDonutValues(completeTasks, notReviewed);

    return {
        id: 'progress',
        type: 'REACT_NODE',
        label: t`Progress`,
        value: (
            <>
                <DataDonut
                    data-id="access-review-progress"
                    size="sm"
                    unit="status"
                    values={
                        donutValues as DataDonutSliceData[] & {
                            length: 1 | 2;
                        }
                    }
                />
                <Text> {t`${getCompleted}% complete`} </Text>
            </>
        ),
    };
};

const getReviewPeriodKVP = (reviewPeriod: {
    startDate: string;
    endDate: string;
}): KeyValuePairProps => {
    return {
        id: 'access-review-application-details-review-period-kvp',
        type: 'REACT_NODE',
        label: t`Review period`,
        value: (
            <DateTime
                date={reviewPeriod.startDate.split('T')[0]}
                format="table_range"
                data-id="access-review-application-details-review-period-kvp"
                endDate={reviewPeriod.endDate.split('T')[0]}
            />
        ),
    };
};

const getApplicationSourceLabel = (
    source: AccessReviewApplicationDetailsResponseDto['application']['source'],
    application: AccessReviewApplicationDetailsResponseDto['application'],
): string => {
    switch (source) {
        case 'DIRECT_CONNECTION': {
            return t`Direct connection`;
        }
        case 'MANUALLY_ADDED': {
            return t`Manually added`;
        }
        case 'PARTNER_CONNECTION': {
            const clientType = getApplicationClientType(application);

            if (!isNil(clientType)) {
                const providerName = getProviderByClientType(clientType)?.name;

                return providerName
                    ? t`${providerName} connection`
                    : t`Partner connection`;
            }

            return t`Partner connection`;
        }
        default: {
            return '-';
        }
    }
};

const getApplicationSourceKVP = (
    application: AccessReviewApplicationDetailsResponseDto['application'],
): KeyValuePairProps => {
    if (application.source === 'PARTNER_CONNECTION') {
        const clientType = getApplicationClientType(application);

        if (!isNil(clientType)) {
            const applicationProvider = getProviderByClientType(clientType);
            const providerName = applicationProvider?.name;

            return {
                id: 'type',
                type: 'TEXT',
                label: t`Type`,
                value: providerName
                    ? t`${providerName} connection`
                    : t`Partner connection`,
            };
        }
    }

    const applicationSourceLabel = getApplicationSourceLabel(
        application.source,
        application,
    );

    return {
        id: 'type',
        type: 'TEXT',
        label: t`Type`,
        value: applicationSourceLabel,
    };
};

export const buildKeyValuePairs = (
    applicationPeriod: {
        startDate?: string;
        endDate?: string;
    },
    application: AccessReviewApplicationDetailsResponseDto | null,
    summary: AccessReviewApplicationSummaryResponseDto | null,
    totalUsers: number,
): KeyValuePairProps[] => {
    if (
        isNil(application?.application) ||
        isNil(applicationPeriod.startDate) ||
        isNil(applicationPeriod.endDate)
    ) {
        return [];
    }

    const { application: app } = application;

    return [
        getStatusKVP(app),
        getProgressKVP(summary, totalUsers),
        getReviewPeriodKVP(
            applicationPeriod as { startDate: string; endDate: string },
        ),
        getApplicationSourceKVP(app),
    ];
};

export const buildActionStack = (
    applicationDetails: AccessReviewApplicationDetailsResponseDto | null,
    personnelList: UserResponseDto[] | undefined,
    reSyncByClientType: () => void,
    isSyncing: boolean,
): React.JSX.Element => {
    const buttonSyncLabel = isSyncing ? t`Loading` : t`Sync`;

    const isEqualSource: boolean =
        applicationDetails?.application.source === 'MANUALLY_ADDED';

    const shouldShowCompleteReviewModal = determineModalToShow(
        personnelList ?? [],
        applicationDetails?.application.reviewers ?? ([] as UserResponseDto[]),
    );

    const primaryActions: Action[] = [
        {
            actionType: 'button',
            id: 'access-review-application-details-action-stack-complete-review',
            typeProps: {
                label: t`Complete review`,
                level: 'primary',
                onClick: () => {
                    openAppropriateModal(shouldShowCompleteReviewModal);
                },
            },
        },
    ];

    const manualSourceActions: Action[] = [
        {
            actionType: 'button',
            id: 'access-review-application-details-action-stack-upload-csv',
            typeProps: {
                label: t`Upload personnel`,
                level: 'secondary',
                onClick: openAccessReviewActiveUpdatePersonnelModal,
            },
        },
    ];

    const autoSourceActions: Action[] = [
        {
            actionType: 'button',
            id: 'access-review-application-details-action-stack-sync',
            typeProps: {
                label: buttonSyncLabel,
                level: 'secondary',
                onClick: reSyncByClientType,
                isLoading: isSyncing,
            },
        },
    ];

    const actions: Action[] = [
        ...(isEqualSource ? manualSourceActions : autoSourceActions),
        ...primaryActions,
    ];

    return (
        <ActionStack
            data-id="access-review-application-details-action-stack"
            gap="sm"
            data-testid="buildActionStack"
            actions={actions}
        />
    );
};
