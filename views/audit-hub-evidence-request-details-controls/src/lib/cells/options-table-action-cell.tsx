import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { CustomerRequestEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const OptionsTableActionCell = ({
    row,
}: {
    row: { original: CustomerRequestEvidenceResponseDto };
}): React.JSX.Element => {
    const { id } = row.original;

    return (
        <SchemaDropdown
            isIconOnly
            key={id}
            size="sm"
            startIconName="HorizontalMenu"
            level="tertiary"
            label={t`Horizontal menu`}
            colorScheme="neutral"
            data-id="6GvBWQ_R"
            data-testid="OptionsTableActionCell"
            items={[
                {
                    id: 'download-evidence-option',
                    label: t`Download Evidence`,
                    type: 'item',
                    onClick: () => {
                        sharedCustomerRequestDetailsController
                            .generateRequestControlEvidencePackage([Number(id)])
                            .catch(() => {
                                snackbarController.addSnackbar({
                                    id: 'generate-evidence-package-error',
                                    props: {
                                        title: t`Generation Failed`,
                                        description: t`An error occurred while generating the evidence package. Try again later.`,
                                        severity: 'critical',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });
                            });
                    },
                },
            ]}
        />
    );
};
