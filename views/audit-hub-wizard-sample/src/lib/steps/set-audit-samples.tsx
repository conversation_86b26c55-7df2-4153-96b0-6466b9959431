import {
    sharedAuditHub<PERSON>uditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { Banner } from '@cosmos/components/banner';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AuditHubWizardSampleModel } from '@models/audit-hub-wizard-sample';
import { Form } from '@ui/forms';

export interface SetAuditSamplesProps {
    formRef?: React.RefObject<HTMLFormElement>;
}

export const SetAuditSamples = observer(
    ({ formRef }: SetAuditSamplesProps): React.JSX.Element => {
        const formId = 'set-audit-samples';
        const { auditByIdData } = sharedAuditHubController;
        const { addSampleWizardData } = sharedAuditHubAuditController;
        const { sampleFormSchema } = new AuditHubWizardSampleModel();

        return (
            <Stack direction="column" gap="2xl" data-id="zuBjcl-q">
                <Text type="subheadline" size="400" as="p">
                    {t`Set audit samples`}
                </Text>

                <Text type="body" size="100" as="p">
                    <Trans>
                        Set your audit samples. Samples and audit evidence will
                        be included up to today. You can update your evidence
                        package later if you need more evidence.
                    </Trans>
                </Text>

                {!auditByIdData?.hasControls && (
                    <Banner
                        severity="warning"
                        title={t`The selected framework has no mapped controls. The control evidence package will be empty.`}
                    />
                )}

                <Form
                    hasExternalSubmitButton
                    formId={formId}
                    ref={formRef}
                    data-testid="SetAuditSamples"
                    data-id="gvqSzqOA"
                    schema={sampleFormSchema}
                    onSubmit={addSampleWizardData}
                />
            </Stack>
        );
    },
);
