import type { ReactNode } from 'react';
import { Confirmation } from '@cosmos-lab/components/confirmation';
import { t } from '@globals/i18n/macro';
import { computed, observer } from '@globals/mobx';
import { sharedTaskModalState } from '@models/tasks';
import { TASK_MODAL_STATE } from '../constants/tasks.constants';

export const TaskModalState = observer(
    ({ defaultContent }: { defaultContent: ReactNode }): React.JSX.Element => {
        const { currentKey } = sharedTaskModalState;
        const content = computed(() => {
            if (currentKey === TASK_MODAL_STATE.DELETE) {
                return (
                    <Confirmation
                        type="danger"
                        confirmText={t`Delete`}
                        title={t`Confirm deleting task`}
                        body={t`Are you sure you want to delete the task?`}
                        onConfirm={() => {
                            sharedTaskModalState.closeModal();
                        }}
                        onCancel={() => {
                            sharedTaskModalState.setCurrent(
                                TASK_MODAL_STATE.FORM,
                            );
                        }}
                    />
                );
            }

            if (currentKey === TASK_MODAL_STATE.CANCEL) {
                return (
                    <Confirmation
                        type="danger"
                        confirmText={t`Discard`}
                        title={t`Discard changes?`}
                        body={t`You will lose all unsaved data.`}
                        onConfirm={() => {
                            sharedTaskModalState.closeModal();
                        }}
                        onCancel={() => {
                            sharedTaskModalState.setCurrent(
                                TASK_MODAL_STATE.FORM,
                            );
                        }}
                    />
                );
            }

            return defaultContent;
        });

        return <>{content.get()}</>;
    },
);
