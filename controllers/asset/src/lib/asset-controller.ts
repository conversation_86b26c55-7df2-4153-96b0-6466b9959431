import { isEmpty, isNil, uniqueId } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { snackbarController } from '@controllers/snackbar';
import {
    assetsControllerGetOneAssetOptions,
    assetsControllerUpdateAssetMutation,
    assetsControllerUpdateAssetNotesMutation,
    assetsControllerUpdateAssetOwnerMutation,
} from '@globals/api-sdk/queries';
import type {
    AssetNotesRequestDto,
    AssetOwnerRequestDto,
    AssetRequestDto,
    AssetResponseDto,
    ComplianceCheckResponseDto,
    ConnectionResponseDto,
    DeviceResponseDto,
    EdrAgentResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { providers } from '@globals/providers';
import { getTimeDiff } from '@helpers/date-time';
import type { FormValues } from '@ui/forms';
import {
    ASSET_CLASS_TYPE,
    ASSET_TYPE,
    COMPLIANCE_CHECK_STATUS,
    DEVICE_COMPLIANCE_COMPUTED_STATUS,
    DEVICE_VALID_COMPLIANCE_CHECKS,
} from './asset.constants';
import type {
    AssetDeviceValidComplianceChecks,
    DeviceCompliance,
    DeviceComplianceCheckWithExclusion,
    DeviceValidComplianceCheckType,
} from './asset.types';

class AssetController {
    constructor() {
        makeAutoObservable(this);
    }

    asset = new ObservedQuery(assetsControllerGetOneAssetOptions);

    updateAssetDetailsMutation = new ObservedMutation(
        assetsControllerUpdateAssetMutation,
    );

    updateAssetNotesMutation = new ObservedMutation(
        assetsControllerUpdateAssetNotesMutation,
    );

    updateAssetOwnerMutation = new ObservedMutation(
        assetsControllerUpdateAssetOwnerMutation,
    );

    get assetDetails(): AssetResponseDto | null {
        return this.asset.data ?? null;
    }

    get assetDevice(): DeviceResponseDto | null {
        return this.assetDetails?.device ?? null;
    }

    get assetDeviceEdrAgent(): EdrAgentResponseDto | null {
        return this.assetDevice?.agentAssignation?.agent ?? null;
    }

    get assetDeviceValidComplianceChecks(): AssetDeviceValidComplianceChecks | null {
        const device = this.asset.data?.device;

        if (isNil(device) || !isNil(device.unlinkedAt)) {
            return null;
        }

        const { complianceChecks, complianceChecksWithExclusions } = device;

        const deviceValidComplianceChecks =
            this.filterDeviceValidComplianceChecks(complianceChecks);

        if (isEmpty(deviceValidComplianceChecks)) {
            return null;
        }

        const complianceChecksWithExclusionsMap = Object.fromEntries(
            (complianceChecksWithExclusions ?? []).map((check) => [
                check.type,
                check,
            ]),
        ) as Record<
            ComplianceCheckResponseDto['type'],
            ComplianceCheckResponseDto
        >;

        return Object.fromEntries(
            deviceValidComplianceChecks.map((deviceCheck) => {
                const checkWithExclusion =
                    complianceChecksWithExclusionsMap[deviceCheck.type];

                return [
                    deviceCheck.type,
                    checkWithExclusion.status ===
                    COMPLIANCE_CHECK_STATUS.EXCLUDED
                        ? {
                              ...deviceCheck,
                              status: checkWithExclusion.status,
                              exclusion: checkWithExclusion.exclusion,
                          }
                        : { ...deviceCheck, exclusion: null },
                ];
            }),
        ) as Record<
            DeviceValidComplianceCheckType,
            DeviceComplianceCheckWithExclusion
        >;
    }

    get deviceCompliance(): DeviceCompliance {
        const device = this.asset.data?.device;

        if (isNil(device)) {
            return null;
        }

        const {
            complianceChecks,
            complianceChecksWithExclusions,
            failed: isDeviceFailed,
            lastCheckedAt,
            sourceType,
        } = device;

        const deviceComplianceChecksWithExclusions =
            this.filterDeviceValidComplianceChecks(
                complianceChecksWithExclusions ?? [],
            );

        if (isEmpty(deviceComplianceChecksWithExclusions)) {
            return null;
        }

        const deviceValidComplianceChecks =
            this.filterDeviceValidComplianceChecks(complianceChecks);

        const deviceComplianceChecksWithExclusionsMap = Object.fromEntries(
            deviceComplianceChecksWithExclusions.map((check) => [
                check.type,
                check,
            ]),
        ) as Record<DeviceValidComplianceCheckType, ComplianceCheckResponseDto>;

        const filteredDeviceComplianceChecks =
            deviceValidComplianceChecks.filter(
                (dc) => dc.type in deviceComplianceChecksWithExclusionsMap,
            );

        const deviceComplianceChecksUnionByExcluded =
            filteredDeviceComplianceChecks.map((dc) => {
                const excludedCheck =
                    deviceComplianceChecksWithExclusionsMap[dc.type];

                return excludedCheck.status === COMPLIANCE_CHECK_STATUS.EXCLUDED
                    ? { ...dc, status: excludedCheck.status }
                    : dc;
            });

        if (
            this.isEveryComplianceCheckExcluded(
                deviceComplianceChecksUnionByExcluded,
            )
        ) {
            return {
                status: DEVICE_COMPLIANCE_COMPUTED_STATUS.EXCLUDED,
            };
        }

        if (isDeviceFailed) {
            return {
                status: DEVICE_COMPLIANCE_COMPUTED_STATUS.UNABLE_TO_GET_DATA,
            };
        }

        const isDeviceCompliant = this.isDeviceCompliant(
            deviceComplianceChecksUnionByExcluded,
        );

        const MAX_HOURS_FOR_NEXT_CHECK = 26;
        const isAgentDevice = sourceType === 'AGENT';

        const deviceMissingRecentCheck =
            isAgentDevice &&
            !isNil(lastCheckedAt) &&
            getTimeDiff(lastCheckedAt) > MAX_HOURS_FOR_NEXT_CHECK;

        if (deviceMissingRecentCheck) {
            return {
                status: DEVICE_COMPLIANCE_COMPUTED_STATUS.UNABLE_TO_GET_DATA,
            };
        }

        return {
            status: isDeviceCompliant
                ? DEVICE_COMPLIANCE_COMPUTED_STATUS.COMPLIANT
                : DEVICE_COMPLIANCE_COMPUTED_STATUS.NON_COMPLIANT,
        };
    }

    get edrConfiguredConnection(): ConnectionResponseDto | null {
        return (
            sharedConnectionsController.allConfiguredConnectionsQuery.data
                ?.edr[0] ?? null
        );
    }

    get edrProviderName(): string {
        const defaultProviderName = 'EDR';

        if (!this.isEdrConnectionEnabled) {
            return defaultProviderName;
        }

        const clientType = this.edrConfiguredConnection?.clientType;
        const provider = Object.values(providers).find(
            (p) => p.id === clientType,
        );

        return provider?.name ?? defaultProviderName;
    }

    get assetDeviceHasAntivirusEvidence(): boolean {
        return (
            !isEmpty(this.assetDevice?.documents) &&
            Boolean(
                this.assetDevice?.documents.some(
                    (document) => document.type === 'ANTIVIRUS_EVIDENCE',
                ),
            )
        );
    }

    get assetDeviceHasEdrAgent(): boolean {
        return !isNil(this.assetDevice?.agentAssignation?.agent);
    }

    get isDrataProvider(): boolean {
        return this.asset.data?.assetProvider === 'DRATA';
    }

    get isUnlinkDeviceEnabled(): boolean {
        return (
            this.isDeviceAsset() &&
            !isNil(this.asset.data?.device) &&
            isNil(this.asset.data.device.deletedAt)
        );
    }

    get isLinkDeviceEnabled(): boolean {
        return (
            this.isDeviceAsset() &&
            !isNil(this.asset.data?.device) &&
            !isNil(this.asset.data.device.deletedAt) &&
            this.asset.data.assetProvider !== 'AGENT'
        );
    }

    get isEdrConnectionEnabled(): boolean {
        return !isNil(this.edrConfiguredConnection);
    }

    get isLoading(): boolean {
        return this.asset.isLoading || sharedConnectionsController.isLoading;
    }

    loadAsset = (id?: string): void => {
        if (isNil(id)) {
            throw new Error('Asset ID is required');
        }

        const assetId = parseInt(id, 10);

        if (isNaN(assetId)) {
            throw new TypeError('Invalid asset ID format');
        }

        this.asset.load({ path: { id: assetId } });
    };

    loadConnections = (): void => {
        when(
            () => !this.asset.isLoading,
            () => {
                if (isNil(this.assetDeviceValidComplianceChecks)) {
                    return;
                }

                sharedConnectionsController.allConfiguredConnectionsQuery.load();
            },
        );
    };

    private isDeviceAsset(): boolean {
        const { assetClassTypes, assetType } = this.asset.data ?? {};

        return (
            (assetClassTypes ?? []).some(
                ({ assetClassType }) =>
                    assetClassType === ASSET_CLASS_TYPE.HARDWARE,
            ) && assetType === ASSET_TYPE.PHYSICAL
        );
    }

    private filterDeviceValidComplianceChecks<
        T extends { type: ComplianceCheckResponseDto['type'] },
    >(complianceChecks: T[]): (T & { type: DeviceValidComplianceCheckType })[] {
        return complianceChecks.filter(
            (
                check,
            ): check is T & {
                type: DeviceValidComplianceCheckType;
            } => check.type in DEVICE_VALID_COMPLIANCE_CHECKS,
        );
    }

    private isEveryComplianceCheckExcluded<
        T extends { status: ComplianceCheckResponseDto['status'] },
    >(complianceChecks: T[]): boolean {
        return complianceChecks.every(
            (check) => check.status === COMPLIANCE_CHECK_STATUS.EXCLUDED,
        );
    }

    private isDeviceCompliant(
        complianceChecks: { status: ComplianceCheckResponseDto['status'] }[],
    ): boolean {
        return complianceChecks.every(
            ({ status }) =>
                status === COMPLIANCE_CHECK_STATUS.PASS ||
                status === COMPLIANCE_CHECK_STATUS.EXCLUDED,
        );
    }

    updateAssetDetailsMethod = (
        assetId: number,
        formValues: FormValues,
    ): void => {
        if (!assetId || this.updateAssetDetailsMutation.isPending) {
            return;
        }
        const assetClassTypes = formValues.class as { value: string }[];

        const detailsData: AssetRequestDto = {
            name: formValues.name as string,
            description: formValues.description as string,
            assetClassTypes: assetClassTypes.map(
                (item) => item.value,
            ) as AssetRequestDto['assetClassTypes'],
            assetType: formValues.type as AssetRequestDto['assetType'],
            uniqueId: formValues.uniqueId as string,
            ownerId: this.assetDetails?.owner.id || 0,
            notes: this.assetDetails?.notes || '',
        };

        this.updateAssetDetailsMutation.mutate({
            path: { id: assetId },
            body: detailsData,
        });

        when(
            () => !this.updateAssetDetailsMutation.isPending,
            () => {
                if (this.updateAssetDetailsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('asset-details-update-error'),
                        props: {
                            title: t`Unable to update asset details`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: uniqueId('asset-details-update-success'),
                        props: {
                            title: t`Asset details updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    this.asset.invalidate();
                }
            },
        );
    };

    get updateAssetDetailsIsPending(): boolean {
        return this.updateAssetDetailsMutation.isPending;
    }

    get updateAssetDetailsHasError(): boolean {
        return this.updateAssetDetailsMutation.hasError;
    }

    updateAssetOwnerMethod = (
        assetId: number,
        formValues: FormValues,
    ): void => {
        if (!assetId || this.updateAssetOwnerMutation.isPending) {
            return;
        }

        const ownerData: AssetOwnerRequestDto = {
            ownerId: parseInt(
                (formValues.ownerId as { value: string }).value,
                10,
            ),
        };

        this.updateAssetOwnerMutation.mutate({
            path: { id: assetId },
            body: ownerData,
        });

        when(
            () => !this.updateAssetOwnerMutation.isPending,
            () => {
                if (this.updateAssetOwnerMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('asset-owner-update-error'),
                        props: {
                            title: t`Unable to update asset owner`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: uniqueId('asset-owner-update-success'),
                        props: {
                            title: t`Asset owner updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    this.asset.invalidate();
                }
            },
        );
    };

    get updateAssetOwnerIsPending(): boolean {
        return this.updateAssetOwnerMutation.isPending;
    }

    get updateAssetOwnerHasError(): boolean {
        return this.updateAssetOwnerMutation.hasError;
    }

    updateAssetNotesMethod = (
        assetId: number,
        formValues: FormValues,
    ): void => {
        if (!assetId || this.updateAssetNotesMutation.isPending) {
            return;
        }

        const notesData: AssetNotesRequestDto = {
            notes: formValues.notes as string,
        };

        this.updateAssetNotesMutation.mutate({
            path: { id: assetId },
            body: notesData,
        });

        when(
            () => !this.updateAssetNotesMutation.isPending,
            () => {
                if (this.updateAssetNotesMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('asset-notes-update-error'),
                        props: {
                            title: t`Unable to update asset notes`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: uniqueId('asset-notes-update-success'),
                        props: {
                            title: t`Asset notes updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    this.asset.invalidate();
                }
            },
        );
    };

    get updateAssetNotesIsPending(): boolean {
        return this.updateAssetNotesMutation.isPending;
    }

    get updateAssetNotesHasError(): boolean {
        return this.updateAssetNotesMutation.hasError;
    }
}

export const activeAssetController = new AssetController();
