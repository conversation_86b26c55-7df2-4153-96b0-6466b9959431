import type { ComponentProps } from 'react';
import type { FileUploadField } from '@cosmos/components/file-upload-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    VendorReviewFindingResponseDto,
    VendorReviewLocationResponseDto,
    VendorReviewResponseDto,
    VendorReviewServiceResponseDto,
} from '@globals/api-sdk/types';
import type { YesNoOption } from '../types/soc-review-form-values.type';
import type {
    ComplianceReportOpinion,
    ComplianceReportOpinionOption,
    ComplianceReportScopeCertification,
    ControlsEncompassBusinessNeedsOption,
    EndUserControlDocumented,
    EndUserControlDocumentedInPlaceOption,
    EndUserControlInPlaceOption,
    ReportFindingsMaterialImpactOption,
    SocReportScopeType,
    SubserviceOrgsUseInclusiveMethods,
    SubserviceOrgsUseInclusiveMethodsOption,
} from '../types/vendors-security-reviews-soc.types';

const labelsToOptions = (labels: Readonly<Record<string, string>>) =>
    Object.entries(labels).map(([value, label], index) => {
        return {
            id: `${value}-${index}`,
            label,
            value,
        };
    });

const enumValuesToSelectOptions = <T extends string>(
    enumValues: T[],
    getLabelFunction: (value: T) => string,
) => {
    return enumValues.map((value, index) => {
        return {
            id: `${value}-${index}`,
            label: getLabelFunction(value),
            value,
        };
    });
};

const SOC_REPORT_VALUES = [
    'SOC_1',
    'SOC_2',
    'SOC_3',
] as const satisfies VendorReviewResponseDto['socReport'][];

export const getReportCertificationLabel = (
    label: VendorReviewResponseDto['socReport'],
): string => {
    switch (label) {
        case 'SOC_1': {
            return 'SOC 1';
        }
        case 'SOC_2': {
            return 'SOC 2';
        }
        case 'SOC_3': {
            return 'SOC 3';
        }
        default: {
            return '';
        }
    }
};

export const getTrustServiceCriteriaLabel = (value: string): string => {
    switch (value) {
        case '1': {
            return 'Availability';
        }
        case '2': {
            return 'Confidentiality';
        }
        case '3': {
            return 'Security';
        }
        case '4': {
            return 'Privacy';
        }
        case '5': {
            return 'Processing Integrity';
        }
        default: {
            return '';
        }
    }
};

const getScopeInternalTypeLabel = (value: string): string => {
    switch (value) {
        case 'type1': {
            return 'Type 1';
        }
        case 'type2': {
            return 'Type 2';
        }
        default: {
            return '';
        }
    }
};

const YES_NO_LABELS: Readonly<Record<YesNoOption, string>> = {
    YES: 'Yes',
    NO: 'No',
};

const SUBSERVICE_ORGS_USE_INCLUSIVE_METHODS_LABELS: Readonly<
    Record<SubserviceOrgsUseInclusiveMethods, string>
> = {
    YES: 'Yes',
    NO: 'No',
    NA: 'N/A',
};

const END_USER_CONTROL_DOCUMENTED_IN_PLACE_LABELS: Readonly<
    Record<EndUserControlDocumented, string>
> = {
    YES_TO_ALL: 'Yes to all',
    NO_TO_ALL: 'No to all',
};

const REPORT_OPINION_LABELS: Readonly<Record<ComplianceReportOpinion, string>> =
    {
        1: 'Unqualified',
        2: 'Qualified',
        3: 'Adverse',
        4: 'Disclaimer',
    };

const CONTROLS_ENCOMPASS_BUSINESS_NEEDS_LABELS = YES_NO_LABELS;
const REPORT_FINDINGS_MATERIAL_IMPACT_LABELS = YES_NO_LABELS;
const END_USER_CONTROL_IN_PLACE_LABELS = YES_NO_LABELS;

export const SOC_FORM_ID = 'vendor-security-review-soc';

export const BRIDGE_LETTER_FILE_UPLOAD_ACCEPTED_FORMATS = [
    'docx',
    'pdf',
] as const satisfies ComponentProps<typeof FileUploadField>['acceptedFormats'];

export const COMPLIANCE_REPORT_SCOPE_CERTIFICATION_OPTIONS =
    enumValuesToSelectOptions<VendorReviewResponseDto['socReport']>(
        SOC_REPORT_VALUES,
        getReportCertificationLabel,
    );

// TODO: generic type should be VendorReviewTrustServiceCategoryMapResponseDto but api response is numbers instead (ticket: https://drata.atlassian.net/browse/ENG-66687)
export const TRUST_SERVICE_CRITERIA_OPTIONS = enumValuesToSelectOptions<string>(
    ['1', '2', '3', '4', '5'],
    getTrustServiceCriteriaLabel,
);

export const COMPLIANCE_REPORT_SCOPE_TYPE_OPTIONS =
    enumValuesToSelectOptions<SocReportScopeType>(
        ['type1', 'type2'],
        getScopeInternalTypeLabel,
    );

export const REPORT_OPINION_OPTIONS: ComplianceReportOpinionOption[] =
    labelsToOptions(REPORT_OPINION_LABELS);

export const CONTROLS_ENCOMPASS_BUSINESS_NEEDS_OPTIONS: ControlsEncompassBusinessNeedsOption[] =
    labelsToOptions(CONTROLS_ENCOMPASS_BUSINESS_NEEDS_LABELS);

export const REPORT_FINDINGS_MATERIAL_IMPACT_OPTIONS: ReportFindingsMaterialImpactOption[] =
    labelsToOptions(REPORT_FINDINGS_MATERIAL_IMPACT_LABELS);

export const END_USER_CONTROL_DOCUMENTED_IN_PLACE_OPTIONS: EndUserControlDocumentedInPlaceOption[] =
    labelsToOptions(END_USER_CONTROL_DOCUMENTED_IN_PLACE_LABELS);

export const END_USER_CONTROL_IN_PLACE_OPTIONS: EndUserControlInPlaceOption[] =
    labelsToOptions(END_USER_CONTROL_IN_PLACE_LABELS);

export const SUBSERVICE_ORGS_USE_INCLUSIVE_METHODS_OPTIONS: SubserviceOrgsUseInclusiveMethodsOption[] =
    labelsToOptions(SUBSERVICE_ORGS_USE_INCLUSIVE_METHODS_LABELS);

/* TODO: Temporary Options: these options do not have constant labels as they will be dynamic content from the db
remove/update this once we have the api functionality https://drata.atlassian.net/browse/ENG-66132*/
export const STUB_REVIEWER_INFO_REVIEWER_OPTIONS = [
    {
        id: `User1`,
        label: 'User1',
        value: 'USER1',
    },
    {
        id: `User2`,
        label: 'User2',
        value: 'USER2',
    },
] as const satisfies ListBoxItemData[];

export const MIN_FINDINGS_LENGTH = 1;
export const MIN_END_USER_CONTROL_LENGTH = 1;
export const MIN_SERVICES_LENGTH = 0;
export const MIN_LOCATIONS_LENGTH = 0;
export const SOC_REPORT_TYPE_1_VALUE = 1;
export const SOC_REPORT_TYPE_2_VALUE = 2;
export const SOC_REPORT_TYPE_3_VALUE = 3;

export const DEFAULT_FINDING: VendorReviewFindingResponseDto = {
    id: 0,
    description: '',
};
export const DEFAULT_END_USER_CONTROL: {
    id: number;
    name: string;
    inPlace: YesNoOption;
} = {
    id: 0,
    name: '',
    inPlace: 'NO',
};

export const DEFAULT_SERVICE: VendorReviewServiceResponseDto = {
    id: 0,
    name: '',
};

export const DEFAULT_LOCATION: VendorReviewLocationResponseDto = {
    id: 0,
    city: '',
    stateCountry: '',
};

export const CERTIFICATION_SCOPE_COMPLETED_TOTALS = {
    SOC1: 3,
    SOC2: 4,
    SOC3: 2,
} as const satisfies Record<ComplianceReportScopeCertification, number>;

export const CERTIFICATION_SCOPE_COMPLETED_TOTALS_DEFAULT = 4;
export const REVIEWER_INFORMATION_COMPLETED_TOTAL = 2;
export const REVIEWER_INFORMATION_COMPLETED_REVIEWER = 1;
export const REVIEWER_INFORMATION_COMPLETED_NO_REVIEWER = 0;
export const SOC_COMPLIANCE_SCOPE_COMPLETED_CERTIFICATION = 1;
export const SOC_COMPLIANCE_SCOPE_COMPLETED_NO_CERTIFICATION = 0;
export const REPORT_OPINION_COMPLETED_TOTAL = 1;
export const END_USER_CONTROLS_COUNT_TOTAL = 1;
export const REPORT_OPINION_COMPLETED_NULL = 0;
export const REPORT_OPINION_COMPLETED_NOT_NULL = 1;
export const NO_REPORT_FINDINGS_NOT_CHECKED = 2;
export const NO_REPORT_FINDINGS_CHECKED = 1;
export const SOC_REPORT_SCOPE_TYPE1 = 'type1';
export const SOC_REPORT_SCOPE_TYPE2 = 'type2';
export const CERTIFICATION_TYPE_PREFIX = 'SOC_';
export const CERTIFICATION_TYPE_SOC1 = 'SOC1';
export const CERTIFICATION_TYPE_SOC2 = 'SOC2';
export const CERTIFICATION_TYPE_SOC3 = 'SOC3';
