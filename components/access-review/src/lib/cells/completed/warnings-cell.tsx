import { isNil } from 'lodash-es';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Metadata } from '@cosmos/components/metadata';
import type { AccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { sharedDatatable } from '@models/access-review';

const MINIMUM_ERROR_VALUE = 1;

interface ApplicationDataRowCellProps {
    row: {
        original: AccessReviewPeriodApplicationResponseDto;
    };
}

export const WarningsCell = ({
    row: { original },
}: ApplicationDataRowCellProps): React.JSX.Element => {
    const { hasFailed, clientType, source } = original;
    const datatableColumns = sharedDatatable;
    const totalWarnings = datatableColumns.getTotalWarningsByCustomFilters(
        clientType,
        source,
        original,
    );

    if (hasFailed) {
        return (
            <Metadata
                iconName="WarningDiamond"
                colorScheme="critical"
                label="Error"
                type="status"
                data-testid="AccessReviewApplicationWarningsCell"
            />
        );
    }

    if (isNil(totalWarnings) || totalWarnings < MINIMUM_ERROR_VALUE) {
        return (
            <EmptyStateTableCell
                data-id="warnings-column"
                data-testid="AccessReviewApplicationWarningsCell"
            />
        );
    }

    const warningPlurality =
        totalWarnings >= MINIMUM_ERROR_VALUE ? t`warnings` : t`warning`;

    const warningText = `${totalWarnings} ${warningPlurality}`;

    return (
        <Metadata
            iconName="WarningTriangle"
            colorScheme="warning"
            type="status"
            data-testid="WarningsCell"
            data-id="CVLFnlgW"
            label={warningText}
        />
    );
};
