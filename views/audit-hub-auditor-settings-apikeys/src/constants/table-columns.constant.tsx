import type { DatatableProps } from '@cosmos/components/datatable';
import type { AuditorApiKeyResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ApiKeysExpiresAtCellComponent } from '../components/api-keys-expires-at-cell.component';
import { ApiKeysOptionsCellComponent } from '../components/api-keys-options-cell.component';
import { ApiKeysStatusCellComponent } from '../components/api-keys-status-cell.component';

export function getApiKeysTableColumns(): DatatableProps<AuditorApiKeyResponseDto>['columns'] {
    return [
        {
            id: 'options',
            header: '',
            isActionColumn: true,
            enableSorting: false,
            cell: ApiKeysOptionsCellComponent,
        },
        {
            header: t`Name`,
            id: 'name',
            accessorKey: 'name',
            size: 500,
            enableSorting: true,
        },
        {
            header: t`Status`,
            id: 'status',
            accessorKey: 'status',
            size: 500,
            enableSorting: false,
            cell: ApiKeysStatusCellComponent,
        },
        {
            header: t`Expiration`,
            id: 'expiresAt',
            accessorKey: 'expiresAt',
            size: 500,
            enableSorting: false,
            cell: ApiKeysExpiresAtCellComponent,
        },
    ];
}
