import { isNil } from 'lodash-es';
import {
    AccessReviewActionCell,
    AccessReviewApplicationCell,
    type AccessReviewPeriodDatatable,
    AccessReviewStatusCell,
    AccessReviewWarningsCell,
} from '@components/access-review';
import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

class ActiveDatatableModel {
    currentPage = 1;
    pageSize = 10;

    constructor() {
        makeAutoObservable(this);
    }

    get columns(): DatatableProps<AccessReviewPeriodDatatable>['columns'] {
        return [
            {
                id: 'access-review-active-action',
                size: 60,
                isActionColumn: true,
                meta: {
                    shouldIgnoreRowClick: true,
                },
                cell: AccessReviewActionCell,
            },
            {
                id: 'access-review-active-application',
                accessorFn: ({ name, logo }) => !isNil(name) || isNil(logo),
                enableSorting: false,
                header: t`Application`,
                cell: AccessReviewApplicationCell,
            },
            {
                id: 'access-review-active-status',
                accessorKey: 'statusMetadata',
                enableSorting: false,
                header: t`Review status`,
                cell: AccessReviewStatusCell,
            },
            {
                id: 'access-review-active-warnings',
                accessorKey: 'warningsMetadata',
                enableSorting: false,
                header: t`Warnings`,
                cell: AccessReviewWarningsCell,
            },
            {
                id: 'access-review-active-type',
                accessorKey: 'type',
                enableSorting: false,
                header: t`Type`,
            },
        ];
    }
}

export const activeDatatableModel = new ActiveDatatableModel();
