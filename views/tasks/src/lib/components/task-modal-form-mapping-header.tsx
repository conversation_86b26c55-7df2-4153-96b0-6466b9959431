import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { dimension2x } from '@cosmos/constants/tokens';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedTaskForm } from '@models/tasks';

const MapGeneralHeader = observer(() => {
    const { canMapControl, canMapRisk, isRiskManagementEnabled } =
        sharedTaskForm;

    return (
        <Box
            data-id="oeZcF00E"
            px="2x"
            py="4x"
            backgroundColor="neutralBackgroundMild"
        >
            <Stack direction="column" gap="2x">
                <Text
                    shouldWrap
                    align="left"
                    as="h2"
                    colorScheme="neutral"
                    size="300"
                    type="title"
                >
                    {t`Mapped object`}
                </Text>
                <Text
                    shouldWrap
                    align="left"
                    as="p"
                    colorScheme="neutral"
                    size="200"
                >
                    {t`Track tasks for a specific control or risk`}
                </Text>
                <Stack gap="2x" direction="row" top={dimension2x}>
                    {canMapControl && (
                        <Tooltip
                            isInteractive
                            preferredSide="top"
                            text={t`Map control to task`}
                        >
                            <Button
                                id="map-control-button"
                                label={t`Map control`}
                                colorScheme="primary"
                                level="secondary"
                                size="sm"
                                type="button"
                                width="auto"
                                onClick={() => {
                                    sharedTaskForm.update('taskType')(
                                        'CONTROL',
                                    );
                                }}
                            />
                        </Tooltip>
                    )}
                    {canMapRisk && isRiskManagementEnabled && (
                        <Tooltip
                            isInteractive
                            preferredSide="top"
                            text={t`Map risk to task`}
                        >
                            <Button
                                id="map-risk-button"
                                label={t`Map risk`}
                                colorScheme="primary"
                                level="secondary"
                                size="sm"
                                type="button"
                                width="auto"
                                onClick={() => {
                                    sharedTaskForm.update('taskType')('RISK');
                                }}
                            />
                        </Tooltip>
                    )}
                </Stack>
            </Stack>
        </Box>
    );
});

const MapControlHeader = observer(() => {
    return (
        <>
            <Divider size="md" />
            <Stack direction="row" justify="between">
                <Text size="300" type="title" colorScheme="neutral" as="h2">
                    {t`Mapped control`}
                </Text>

                <Button
                    label={t`Remove`}
                    type="button"
                    colorScheme="danger"
                    level="tertiary"
                    size="sm"
                    onClick={() => {
                        sharedTaskForm.update('taskType')('GENERAL');
                    }}
                />
            </Stack>
        </>
    );
});

const MapRiskHeader = observer(() => {
    return (
        <>
            <Divider size="md" />
            <Stack direction="row" justify="between">
                <Text size="300" type="title" colorScheme="neutral" as="h2">
                    {t`Mapped risk`}
                </Text>

                <Button
                    label={`Remove`}
                    type="button"
                    colorScheme="danger"
                    level="tertiary"
                    size="sm"
                    onClick={() => {
                        sharedTaskForm.update('taskType')('GENERAL');
                    }}
                />
            </Stack>
        </>
    );
});

export const TaskModalFormMappingHeader = observer(() => {
    const { taskType = 'GENERAL' } = sharedTaskForm.currentValues;

    if (taskType === 'CONTROL') {
        return <MapControlHeader />;
    }
    if (taskType === 'RISK') {
        return <MapRiskHeader />;
    }

    return <MapGeneralHeader data-id="f0ugECzP" />;
});
