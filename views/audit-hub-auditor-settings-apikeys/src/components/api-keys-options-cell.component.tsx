import { sharedAuditHubSettingsController } from '@controllers/audit-hub';
import { modalController } from '@controllers/modal';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { AuditorApiKeyResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { RenewApiKeyModal } from './renew-api-key-modal.component';

const handleClose = () => {
    modalController.closeModal('renew-api-key-modal');
};

const openRenewApiKeyModal = (apiKey: AuditorApiKeyResponseDto): void => {
    modalController.openModal({
        id: 'renew-api-key-modal',
        content: () => (
            <RenewApiKeyModal
                apiKey={apiKey}
                data-id="7KpQr3Lm"
                onClose={handleClose}
            />
        ),
        centered: true,
        disableClickOutsideToClose: true,
        size: 'lg',
    });
};

export const ApiKeysOptionsCellComponent = ({
    row: { original },
}: {
    row: { original: AuditorApiKeyResponseDto };
}): React.JSX.Element | undefined => {
    const { revokeApiKey } = sharedAuditHubSettingsController;

    // Don't show options for expired keys
    if (original.status === 'EXPIRED') {
        return undefined;
    }

    return (
        <SchemaDropdown
            isIconOnly
            startIconName="HorizontalMenu"
            level="tertiary"
            label={t`Options`}
            colorScheme="neutral"
            data-testid="ApiKeysOptionsCellComponent"
            data-id="7KpQr3Lm"
            items={[
                {
                    id: 'renew-option',
                    label: t`Renew`,
                    type: 'item',
                    value: 'renew',
                    onSelect: () => {
                        openRenewApiKeyModal(original);
                    },
                },
                {
                    id: 'revoke-key-option',
                    label: t`Revoke`,
                    type: 'item',
                    value: 'revoke-key',
                    onSelect: () => {
                        openConfirmationModal({
                            title: t`Revoke API Key`,
                            body: t`This action cannot be undone. If you need this API key again, you will need to create a new API key.`,
                            confirmText: t`Revoke API Key`,
                            cancelText: t`Cancel`,
                            type: 'danger',
                            onConfirm: () => {
                                revokeApiKey(original.id);
                                closeConfirmationModal();
                            },
                            onCancel: () => {
                                closeConfirmationModal();
                            },
                        });
                    },
                },
            ]}
        />
    );
};
