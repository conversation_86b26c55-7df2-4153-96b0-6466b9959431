import { sharedAuthController } from '@controllers/auth';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    type ClientLoaderFunction,
    type MetaFunction,
    Navigate,
} from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: t`Drata` }];

export const clientLoader: ClientLoaderFunction = action(({ params }) => {
    const { accountId } = params;

    if (!sharedAuthController.email || !sharedAuthController.region) {
        snackbarController.addSnackbar({
            id: 'auditor-act-as-error',
            props: {
                title: t`Email and region are required`,
                description: t`An error occurred while accesing to client's page. Try again later.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });

        return null;
    }

    sharedAuthController.attemptLogin(
        sharedAuthController.email, // TODO: not needed for auditor act as
        sharedAuthController.region, // TODO: not needed for auditor act as
        { accountId }, // initiate a secondary authentication flow with act as token
    );

    return null;
});

const ActAsAuditorPage = observer((): React.JSX.Element | null => {
    const { isAttemptingLogin, hasAttemptedLogin } = sharedAuthController;

    return (
        <RouteLandmark
            as="section"
            data-testid="MagicLinkUserPage"
            data-id="EHqJtpUY"
        >
            {isAttemptingLogin && <div>{t`logging in`}</div>}
            {!isAttemptingLogin && hasAttemptedLogin && (
                <Navigate
                    replace
                    to="/workspaces/1/quick-start"
                    data-testid="Redirect"
                    data-id="zcE3bjjJ"
                />
            )}
        </RouteLandmark>
    );
});

export default ActAsAuditorPage;
