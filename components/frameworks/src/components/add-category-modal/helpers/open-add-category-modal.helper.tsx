import { modalController } from '@controllers/modal';
import { AddCategoryModal } from '../add-category-modal.component';

const ADD_CATEGORY_MODAL_ID = 'add-category-modal';

export const openAddCategoryModal = (): void => {
    modalController.openModal({
        id: ADD_CATEGORY_MODAL_ID,
        content: () => <AddCategoryModal data-id="add-category-modal" />,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'md',
    });
};

export const closeAddCategoryModal = (): void => {
    modalController.closeModal(ADD_CATEGORY_MODAL_ID);
};
