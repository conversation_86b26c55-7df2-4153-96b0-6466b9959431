import { Card } from '@cosmos/components/card';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { AssetDetailsDeviceComplianceCheckComponent } from './asset-details-device-compliance-check-component';

export const AssetsDetailsDeviceComplianceChecksCard =
    (): React.JSX.Element | null => {
        return (
            <Card
                data-testid="AssetsDetailsDeviceComplianceChecksCard"
                data-id="cTOF5RSO"
                title={t`Device compliance`}
                body={
                    <Stack gap="4x" direction="column">
                        <AssetDetailsDeviceComplianceCheckComponent
                            checkLabel={t`Password Manager`}
                            checkType="PASSWORD_MANAGER"
                        />
                        <AssetDetailsDeviceComplianceCheckComponent
                            checkLabel={t`Auto updates`}
                            checkType="AUTO_UPDATES"
                        />
                        <AssetDetailsDeviceComplianceCheckComponent
                            checkLabel={t`Disk encrypted`}
                            checkType="HDD_ENCRYPTION"
                        />
                        <AssetDetailsDeviceComplianceCheckComponent
                            checkLabel={t`Antivirus`}
                            checkType="ANTIVIRUS"
                        />
                        <AssetDetailsDeviceComplianceCheckComponent
                            checkLabel={t`Lock screen`}
                            checkType="LOCK_SCREEN"
                        />
                    </Stack>
                }
            />
        );
    };
