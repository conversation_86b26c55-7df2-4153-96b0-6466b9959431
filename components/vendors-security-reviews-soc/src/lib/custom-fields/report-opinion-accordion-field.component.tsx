import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
} from '@ui/forms';
import { REPORT_OPINION_COMPLETED_TOTAL } from '../constants/vendors-security-reviews-soc.constants';
import { getReportOpinionCompletionCounter } from '../helpers/soc-form-completion-counters.helper';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../vendors-security-reviews-soc-supporting-content-component';

export const ReportOpinionAccordionField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { watch } = useFormContext();
    const formValues: SocReviewFormValuesType = watch();

    const reportOpinionCompletedCounter =
        getReportOpinionCompletionCounter(formValues);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label="Report Opinion"
            data-testid="ReportOpinionAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title="Report opinion"
                    data-id={`${dataId}-accordion`}
                    data-testid="ReportOpinionAccordionField"
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            total={REPORT_OPINION_COMPLETED_TOTAL}
                            completed={reportOpinionCompletedCounter}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <UniversalFormField
                                name={`${name}.reportOpinion`}
                                formId={formId}
                                data-id={`${dataId}-report-opinion-field`}
                            />

                            <UniversalFormField
                                name={`${name}.encompassBusinessNeeds`}
                                formId={formId}
                                data-id={`${dataId}-encompass-business-needs-field`}
                            />

                            <UniversalFormField
                                name={`${name}.followUpActivity`}
                                formId={formId}
                                data-id={`${dataId}-follow-up-activity-field`}
                            />
                        </Stack>
                    }
                />
            )}
        />
    );
};
