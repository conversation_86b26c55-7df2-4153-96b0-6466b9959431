import { isEmpty, isNil } from 'lodash-es';
import {
    sharedPolicyBuilderController,
    sharedPolicyHeaderActionsController,
} from '@controllers/policy-builder';
import { Banner, type BannerProps } from '@cosmos/components/banner';
import { Button } from '@cosmos/components/button';
import type {
    ContentType,
    KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import { Metadata, type MetadataProps } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';

export class PolicyBuilderHeaderModel {
    readonly pageId = 'policy-builder';

    constructor() {
        makeAutoObservable(this);
    }

    private get policy() {
        return sharedPolicyBuilderController.policy;
    }

    private get currentVersion() {
        return sharedPolicyBuilderController.currentVersion;
    }

    private get shouldShowVersionBanner(): boolean {
        const { isDraft, isPublished, hasPublishedVersion, hasDraftVersion } =
            sharedPolicyBuilderModel;

        return (
            (isDraft && hasPublishedVersion) || (isPublished && hasDraftVersion)
        );
    }

    get title(): string {
        return this.policy?.name || t`Policy Builder`;
    }

    get actionStack(): React.JSX.Element {
        return this.policyHeaderActionsController.actionStack;
    }

    private get policyHeaderActionsController() {
        return sharedPolicyHeaderActionsController;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const pairs = [];

        if (this.currentVersion?.createdAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Creation date`,
                    'policy-creation-date',
                    this.currentVersion.createdAt,
                ),
            );
        }
        if (this.currentVersion?.approvedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Approved date`,
                    'policy-approved-date',
                    this.currentVersion.approvedAt,
                ),
            );
        }
        if (this.currentVersion?.publishedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Published date`,
                    'policy-published-date',
                    this.currentVersion.publishedAt,
                ),
            );
        }
        if (!isNil(this.currentVersion?.updatedAt)) {
            pairs.push(
                this.buildKeyValue(
                    t`Last saved`,
                    'policy-last-saved',
                    this.currentVersion.updatedAt,
                ),
            );
        }

        return pairs;
    }

    get slot(): React.JSX.Element | null {
        const items = [];

        if (this.currentVersion?.policyVersionStatus) {
            items.push(
                <Metadata
                    key="status"
                    type="tag"
                    label={this.getStatusLabel(
                        this.currentVersion.policyVersionStatus,
                    )}
                    colorScheme={this.getStatusColor(
                        this.currentVersion.policyVersionStatus,
                    )}
                />,
            );
        }

        if (this.policy?.policyStatus) {
            items.push(
                <Metadata
                    key="policy-status"
                    label={this.policy.policyStatus}
                    type="tag"
                    colorScheme={this.getPolicyStatusColor(
                        this.policy.policyStatus,
                    )}
                />,
            );
        }

        if (
            this.currentVersion?.composedVersion ||
            this.currentVersion?.policyVersionStatus === 'DRAFT'
        ) {
            items.push(
                <Metadata
                    key="version"
                    label={this.versionLabel}
                    colorScheme="neutral"
                    type="tag"
                />,
            );
        }

        if (isEmpty(items)) {
            return null;
        }

        return (
            <Stack direction="row" gap="2x">
                {items}
            </Stack>
        );
    }

    get banner(): React.JSX.Element | undefined {
        const { shouldShowVersionBanner, currentVersion, policy } = this;
        const { isDraft, publishedVersionId, draftVersionId } =
            sharedPolicyBuilderModel;
        const { switchToPolicyVersion } = sharedPolicyBuilderController;

        const handleGoToPublishedVersion = (): void => {
            if (publishedVersionId) {
                switchToPolicyVersion(publishedVersionId);
            }
        };

        const handleGoToDraftVersion = (): void => {
            if (draftVersionId) {
                switchToPolicyVersion(draftVersionId);
            }
        };

        const conditions: {
            when: unknown;
            data: BannerProps;
        }[] = [
            {
                when: shouldShowVersionBanner && isDraft,
                data: {
                    severity: 'primary',
                    title: t`This is a draft version`,
                    'data-id': 'policy-builder-draft-banner',
                    action: (
                        <Button
                            label={t`View latest published version`}
                            size="sm"
                            level="secondary"
                            data-id="draft-banner-published-button"
                            onClick={handleGoToPublishedVersion}
                        />
                    ),
                },
            },
            {
                when: shouldShowVersionBanner && !isDraft,
                data: {
                    severity: 'primary',
                    title: t`This is a published version`,
                    body: t`You have an unpublished draft version`,
                    'data-id': 'policy-builder-published-banner',
                    action: (
                        <Button
                            label={t`View draft version`}
                            size="sm"
                            level="secondary"
                            data-id="published-banner-draft-button"
                            onClick={handleGoToDraftVersion}
                        />
                    ),
                },
            },
            {
                when: !policy?.currentOwner,
                data: {
                    severity: 'critical',
                    title: t`Owner is missing`,
                    body: t`Drata requires an owner for all policies.`,
                },
            },
            {
                when:
                    currentVersion?.hasExpiredRenewalDate &&
                    ['DRAFT', 'NEEDS_APPROVAL', 'APPROVED'].includes(
                        currentVersion.policyVersionStatus,
                    ),
                data: {
                    severity: 'critical',
                    title: t`Update renewal date`,
                    body: t`Before finalizing, make sure your renewal date is updated.`,
                },
            },
            {
                when: currentVersion?.externalProvider,
                data: {
                    severity: 'warning',
                    title: t`External policy sync`,
                    body: t`This policy is synced with an external source.`,
                },
            },
            {
                when:
                    currentVersion?.policyVersionStatus === 'DRAFT' &&
                    currentVersion.approvedAt,
                data: {
                    severity: 'warning',
                    title: t`Changes requested`,
                    body: t`Changes have been requested for this policy version.`,
                },
            },
        ];

        const matchedCondition = conditions.find((c) => c.when);

        if (!matchedCondition) {
            return undefined;
        }

        const {
            severity,
            title,
            body,
            action,
            'data-id': dataId,
        } = matchedCondition.data;

        return (
            <Banner
                severity={severity}
                title={title}
                body={body}
                action={action}
                data-id={dataId}
                displayMode="section"
            />
        );
    }

    private get versionLabel() {
        if (this.currentVersion?.policyVersionStatus === 'DRAFT') {
            return t`Draft`;
        }
        if (this.currentVersion?.composedVersion) {
            const version = this.currentVersion.composedVersion;

            return t`Version ${version}`;
        }

        return t`Version 1.0`;
    }

    private getStatusColor(status: string) {
        const map: Record<string, MetadataProps['colorScheme']> = {
            NEEDS_APPROVAL: 'warning',
            APPROVED: 'success',
            PUBLISHED: 'success',
        };

        return map[status];
    }

    private getPolicyStatusColor(status: string): MetadataProps['colorScheme'] {
        const map: Record<string, MetadataProps['colorScheme']> = {
            ACTIVE: 'success',
            ARCHIVED: 'critical',
            REPLACED: 'critical',
        };

        return map[status];
    }

    private getStatusLabel(status: string) {
        const map: Record<string, string> = {
            NEEDS_APPROVAL: t`Needs Approval`,
            APPROVED: t`Approved`,
            PUBLISHED: t`Published`,
        };

        return map[status] || status;
    }

    private buildKeyValue(
        label: string,
        id: string,
        date: string,
    ): KeyValuePairProps {
        return {
            id,
            label,
            value: new Date(date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            }),
            type: 'TEXT' as ContentType,
        };
    }
}
