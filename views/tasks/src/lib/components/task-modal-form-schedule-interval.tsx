import { noop } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { FieldFeedback } from '@cosmos/components/field-feedback';
import { FormField } from '@cosmos/components/form-field';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getTaskFrequencyOptions } from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';

const IntervalField = observer(() => {
    const TASK_FREQUENCY_OPTIONS = getTaskFrequencyOptions();

    return (
        <Stack direction="row" gap="2x" data-id="uJf3dS2-">
            <Box width="16x">
                <TextField
                    shouldHideLabel
                    formId={TASKS_FORM_ID}
                    label={t`Interval`}
                    name="recurringSchedule.interval"
                    value={1}
                    onChange={noop}
                />
            </Box>
            <SelectField
                shouldHideLabel
                label={t`Frequency`}
                formId={TASKS_FORM_ID}
                name="recurringSchedule.type"
                options={TASK_FREQUENCY_OPTIONS}
                value={TASK_FREQUENCY_OPTIONS[0]}
                fieldWidth="fit-content"
                onChange={noop}
            />
        </Stack>
    );
});

export const TaskModalFormScheduleInterval = observer(() => {
    const error = '';

    return (
        <>
            <FormField
                formId={TASKS_FORM_ID}
                label="Repeat every"
                name="interval"
                renderInput={() => <IntervalField data-id="keXSsZQ4" />}
            />

            <FieldFeedback
                id="feedback-interval-id"
                type="error"
                message={error}
            />
        </>
    );
});
