import { sharedVendorsSettingsController } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { VendorEmailContentRequestDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type useFormSubmit } from '@ui/forms';
import { sharedVendorsSettingsEmailContentFormModel } from '../models/vendors-settings-email-content-form.model';
import { CompanyPreviewComponent } from './company-preview.component';

interface VendorsSettingsEmailContentEditComponentProps {
    formRef: ReturnType<typeof useFormSubmit>['formRef'];
    onSubmit: (values: VendorEmailContentRequestDto) => void;
}

export const VendorsSettingsEmailContentEditComponent = observer(
    ({
        formRef,
        onSubmit,
    }: VendorsSettingsEmailContentEditComponentProps): React.JSX.Element => {
        const schema = sharedVendorsSettingsEmailContentFormModel.getSchema();

        const handleSubmit = (values: Record<string, unknown>) => {
            onSubmit({ emailContent: values.emailContent as string });
        };

        return (
            <Stack direction="column" gap="lg" data-id="email-content-edit">
                <Stack display="flex" direction="column" gap="lg">
                    <Text type="body" colorScheme="faded" size="100">
                        {t`Header preview`}
                    </Text>
                    <Text type="body" size="100" colorScheme="faded">
                        <Trans>
                            Drata uses your primary workspace&apos;s logo and
                            company name from settings.
                        </Trans>
                    </Text>
                    <CompanyPreviewComponent data-id="company-preview-update" />
                </Stack>

                <Form
                    hasExternalSubmitButton
                    ref={formRef}
                    schema={schema}
                    data-id="email-content-form"
                    formId="email-content-form"
                    isReadOnly={
                        sharedVendorsSettingsController.isUpdatingEmailContent
                    }
                    onSubmit={handleSubmit}
                />
            </Stack>
        );
    },
);
