import { useCallback, useMemo } from 'react';
import { z } from 'zod';
import { sharedRequirementCreateController } from '@controllers/requirements';
import { snackbarController } from '@controllers/snackbar';
import { Modal } from '@cosmos/components/modal';
import { useLingui } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { closeAddCategoryModal } from './helpers/open-add-category-modal.helper';

interface AddCategoryModalProps {
    'data-id': string;
}

export const AddCategoryModal = observer(
    ({ 'data-id': dataId }: AddCategoryModalProps): React.JSX.Element => {
        const { t } = useLingui();
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleSubmit = useCallback(
            (values: FormValues) => {
                try {
                    sharedRequirementCreateController.addCategory(
                        (values as { categoryName: string }).categoryName,
                    );

                    closeAddCategoryModal();
                } catch (error) {
                    console.error(error);

                    snackbarController.addSnackbar({
                        id: 'category-add-error',
                        props: {
                            title: t`Error adding category`,
                            description: t`Failed to add the new category. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
            [t],
        );

        const handleAddClick = useCallback(() => {
            triggerSubmit().catch(() => {
                console.error('Failed to submit form');
            });
        }, [triggerSubmit]);

        const formSchema = useMemo(
            () =>
                ({
                    categoryName: {
                        type: 'text',
                        label: t`Category name`,
                        validator: z
                            .string()
                            .min(1, { message: t`Category name is required` }),
                        initialValue: '',
                    },
                }) as const satisfies FormSchema,
            [t],
        );

        return (
            <>
                <Modal.Header
                    title={t`Add new category`}
                    closeButtonAriaLabel={t`Close add category modal`}
                    onClose={closeAddCategoryModal}
                />
                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        data-id={`${dataId}-form`}
                        formId="add-category-form"
                        schema={formSchema}
                        onSubmit={handleSubmit}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: closeAddCategoryModal,
                        },
                        {
                            label: t`Add`,
                            level: 'primary',
                            onClick: handleAddClick,
                        },
                    ]}
                />
            </>
        );
    },
);
