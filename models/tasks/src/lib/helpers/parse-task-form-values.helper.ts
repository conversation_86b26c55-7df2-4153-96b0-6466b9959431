import type { CustomTaskBaseRequestDto } from '@globals/api-sdk/types';
import type { TaskFormValues } from '@models/tasks';

/**
 * Get entity ID based on the mapped type and selected entity.
 */
const getEntityId = (values: TaskFormValues): number | null => {
    const { entityId, taskType, control, risk } = values;

    if (entityId) {
        return entityId;
    }

    if (taskType === 'CONTROL' && control?.id) {
        return parseInt(control.id, 10);
    }
    if (taskType === 'RISK' && risk?.id) {
        return parseInt(risk.id, 10);
    }

    return null;
};

export const parseTaskFormValues = (
    values: TaskFormValues,
): CustomTaskBaseRequestDto => {
    const entityId = getEntityId(values);

    return {
        title: values.title ?? '',
        description: values.description || null,
        dueDate: values.dueDate ?? '',
        assigneeId: parseInt(values.assignee?.id ?? '', 10),
        taskType: values.taskType || 'GENERAL',
        entityId,
    };
};
