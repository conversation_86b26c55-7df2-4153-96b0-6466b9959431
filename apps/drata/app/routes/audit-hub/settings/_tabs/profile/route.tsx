import { sharedAuditHubSettingsController } from '@controllers/audit-hub';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditorSettingsProfileView } from '@views/audit-hub-auditor-settings-profile';

export const meta: MetaFunction = () => [
    { title: t`Audit hub - Settings - Profile` },
];

export const clientLoader: ClientLoaderFunction = action((): null => {
    sharedAuditHubSettingsController.loadAuditorProfile();

    return null;
});

const AuditorSettingsProfile = (): React.JSX.Element => {
    return (
        <AuditorSettingsProfileView
            data-testid="AuditorSettingsProfile"
            data-id="xP7Zt5Lq"
        />
    );
};

export default AuditorSettingsProfile;
