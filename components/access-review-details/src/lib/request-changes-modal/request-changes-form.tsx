import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import { Form, type FormValues } from '@ui/forms';

interface RequestChangesFormProps {
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: (values: FormValues) => void;
}

export const RequestChangesForm = ({
    formRef,
    onSubmit,
}: RequestChangesFormProps): React.JSX.Element => {
    return (
        <Form
            hasExternalSubmitButton
            ref={formRef}
            formId="request-changes-form"
            data-testid="RequestChangesForm"
            data-id="1I0BKVz9"
            schema={{
                note: {
                    type: 'textarea',
                    maxCharacters: 760,
                    label: t`Explanation of changes`,
                    labelStyleOverrides: {
                        size: 'md',
                    },
                    helpText: t`The explanation of changes will be displayed in the internal notes.`,
                    rows: 3,
                    validator: z.string().min(1, {
                        message: t`Explanation of changes is a required field`,
                    }),
                },
            }}
            onSubmit={onSubmit}
        />
    );
};
