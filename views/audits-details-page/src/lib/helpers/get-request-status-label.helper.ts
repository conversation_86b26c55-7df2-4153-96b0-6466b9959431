import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getRequestStatusLabel = (
    status: CustomerRequestListItemResponseDto['status'],
): string => {
    switch (status) {
        case 'ACCEPTED': {
            return t`Completed`;
        }
        case 'IN_REVIEW': {
            return t`Prepared`;
        }
        case 'OUTSTANDING': {
            return t`New`;
        }
        default: {
            return t`New`;
        }
    }
};
