import { t } from '@globals/i18n/macro';

/**
 * Returns the default email content for vendor questionnaire invitations.
 * This content is used when no custom email content is provided.
 *
 * Note: This is implemented as a function to avoid i18n race conditions
 * where translations execute before the i18n service is initialized.
 */
export const getDefaultEmailContent = (): string => {
    return t`Hi,\n\nWe'd like to conduct a security review and would like some information from you. Use this link to complete the questionnaire.\n\nThank you.`;
};
