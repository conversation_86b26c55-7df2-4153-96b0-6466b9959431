import {
    sharedMonitoringControlTestComparisonController,
    sharedMonitoringResetTestsMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitorsController } from '@controllers/monitors';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import { closeControlsMonitoringCompareDefaultModal } from '../helpers/controls-monitoring-compare-default-modal.helper';
import { ControlsMonitoringCompareDefaultListComponent } from './controls-monitoring-compare-default-list.component';

export const ControlsMonitoringCompareDefaultModal = observer(
    (): React.JSX.Element => {
        const { userMappedTests, templateMappedTests } =
            sharedMonitoringControlTestComparisonController;

        const userMappedTestsCount = userMappedTests.length;
        const templateMappedTestsCount = templateMappedTests.length;

        return (
            <>
                <Modal.Header
                    title={t`Comparison to default mapped tests`}
                    closeButtonAriaLabel="X"
                    onClose={() => {
                        closeControlsMonitoringCompareDefaultModal();
                    }}
                />
                <Modal.Body>
                    <Stack gap="lg" direction="column">
                        {userMappedTestsCount === 0 &&
                        templateMappedTestsCount === 0 ? (
                            <Stack gap="md" direction="column">
                                <Text
                                    type="body"
                                    size="100"
                                    colorScheme="neutral"
                                >
                                    {t`We periodically update Drata's default mapped tests based on industry best practices.`}
                                </Text>
                                <Text
                                    type="title"
                                    size="200"
                                    colorScheme="neutral"
                                >
                                    {t`Your tests are already mapped based on Drata's current defaults.`}
                                </Text>
                            </Stack>
                        ) : (
                            <ControlsMonitoringCompareDefaultListComponent />
                        )}
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={
                        userMappedTestsCount === 0 &&
                        templateMappedTestsCount === 0
                            ? [
                                  {
                                      label: t`Close`,
                                      level: 'secondary',
                                      onClick:
                                          closeControlsMonitoringCompareDefaultModal,
                                  },
                              ]
                            : [
                                  {
                                      label: t`Keep current tests`,
                                      level: 'secondary',
                                      onClick:
                                          closeControlsMonitoringCompareDefaultModal,
                                  },
                                  {
                                      label: t`Apply defaults`,
                                      level: 'primary',
                                      colorScheme: 'primary',
                                      onClick: action(() => {
                                          const {
                                              resetTests,
                                              isResetting,
                                              hasResetError,
                                          } =
                                              sharedMonitoringResetTestsMutationController;

                                          resetTests(
                                              sharedMonitorsController.controlId,
                                          );

                                          when(
                                              () => !isResetting,
                                              () => {
                                                  if (hasResetError) {
                                                      return;
                                                  }

                                                  closeControlsMonitoringCompareDefaultModal();
                                              },
                                          );
                                      }),
                                  },
                              ]
                    }
                />
            </>
        );
    },
);
