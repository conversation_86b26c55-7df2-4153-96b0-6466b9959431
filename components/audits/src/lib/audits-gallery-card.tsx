import { isEmpty } from 'lodash-es';
import { useMemo } from 'react';
import type { Action } from '@cosmos/components/action-stack';
import {
    KeyValuePair,
    type UserValue,
} from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import {
    FrameworkBadge,
    getFrameworkBadge,
} from '@cosmos-lab/components/framework-badge';
import { GalleryCard } from '@cosmos-lab/components/gallery-card';
import type {
    AuditListResponseDto,
    MultiProductResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { formatDate } from '@helpers/date-time';
import { getFullName, getInitials } from '@helpers/formatters';
import { useLocation } from '@remix-run/react';
import { getAuditStatusLabel } from '../helpers/audit-label-mapper.helper';

const getFullTitleWithWorkspace = (
    baseTitle: string,
    workspace?: MultiProductResponseDto,
): string => {
    const { isMultipleWorkspacesEnabled } = sharedFeatureAccessModel;

    if (!workspace || !isMultipleWorkspacesEnabled) {
        return baseTitle;
    }

    const workspaceName = workspace.name;

    return `${baseTitle} - ${workspaceName}`;
};

export const AuditGalleryCard = observer(
    ({ row: audit }: { row: AuditListResponseDto }): React.JSX.Element => {
        const { getWorkspaceById } = sharedWorkspacesController;
        const { isAuditor } = sharedCurrentUserController;
        const workspace = getWorkspaceById(audit.productId);
        const totalRequests = audit.totalRequests || 0;

        const location = useLocation();
        const auditors: UserValue[] | null = useMemo(() => {
            const allAuditors = audit.auditors;

            if (isEmpty(allAuditors)) {
                return null;
            }

            return allAuditors.map((auditor) => {
                const { firstName, lastName, avatarUrl } = auditor;
                const fallbackText = getInitials(
                    getFullName(firstName, lastName),
                );

                return {
                    avatarProps: {
                        imgSrc: avatarUrl as string,
                        imgAlt: fallbackText,
                        fallbackText,
                    },
                };
            });
        }, [audit.auditors]);

        const auditPeriod: string | null = useMemo(() => {
            if (!audit.startDate || !audit.endDate) {
                return null;
            }

            const type = audit.frameworkType?.dateType;

            if (type === 'SINGLE') {
                return formatDate('field', audit.startDate);
            }

            return formatDate('field_range', audit.startDate, audit.endDate);
        }, [audit.startDate, audit.endDate, audit.frameworkType?.dateType]);

        const getActionButton = (): Action => {
            if (
                audit.status === 'COMPLETED' ||
                !isAuditor ||
                totalRequests > 0
            ) {
                return {
                    actionType: 'button',
                    id: 'audit-gallery-action-open-audit',
                    typeProps: {
                        label: t`Open audit`,
                        endIconName: 'ChevronRight',
                        level: 'tertiary',
                        href: `${location.pathname}/${audit.id}/details`,
                    },
                };
            }

            return {
                actionType: 'button',
                id: 'audit-gallery-action-start-audit',
                typeProps: {
                    label: t`Start audit`,
                    level: 'primary',
                    href: `${location.pathname}/${audit.id}/wizard-sample`,
                },
            };
        };

        return (
            <GalleryCard
                data-id="1Wj-s40h"
                data-testid="AuditGalleryCard"
                title={getFullTitleWithWorkspace(
                    audit.frameworkType?.label ?? '',
                    workspace,
                )}
                imageSlot={
                    <FrameworkBadge badgeName={getFrameworkBadge(audit.type)} />
                }
                actionStack={[
                    {
                        id: 'audit-gallery-action-stack',
                        actions: [getActionButton()],
                    },
                ]}
                statusStack={
                    <Stack gap="md">
                        <Metadata
                            label={getAuditStatusLabel(audit.status).label}
                            type="status"
                            colorScheme={
                                getAuditStatusLabel(audit.status).colorScheme
                            }
                        />
                        {audit.unreadMessages ? (
                            <Metadata
                                colorScheme="warning"
                                type="number"
                                iconName="Messages"
                                label={audit.unreadMessages.toString()}
                            />
                        ) : null}
                    </Stack>
                }
                metadataSlot={
                    <Stack gap="2xl">
                        <KeyValuePair
                            label={t`Audit period`}
                            value={auditPeriod}
                        />
                        {auditors && (
                            <KeyValuePair
                                label={t`Auditors`}
                                type="USER"
                                value={auditors}
                            />
                        )}
                    </Stack>
                }
            />
        );
    },
);
