import { AppDatatable } from '@components/app-datatable';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import {
    AUDITOR_CLIENTS_PAGINATION_OPTIONS,
    getAuditorTableColumns,
} from '../constants/auditor-clients.constants';

export const AuditorClientsView = observer((): React.JSX.Element => {
    const {
        auditorClients,
        auditorClientsTotal,
        auditorClientListIsLoading,
        loadAuditorClientList,
    } = sharedAuditHubController;

    const navigate = useNavigate();

    return (
        <AppDatatable
            isLoading={auditorClientListIsLoading}
            tableId="auditors-list"
            total={auditorClientsTotal}
            data={auditorClients}
            columns={getAuditorTableColumns()}
            data-testid="AuditorClientsView"
            data-id="WG5SuueY"
            tableActions={[]}
            filterViewModeProps={{
                props: {
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: 'Pin filters to page',
                    toggleUnpinnedLabel: 'Move filters to dropdown',
                    selectedOption: 'pinned',
                },
                viewMode: 'toggleable',
            }}
            defaultPaginationOptions={
                AUDITOR_CLIENTS_PAGINATION_OPTIONS.defaultPaginationOptions
            }
            emptyStateProps={{
                illustrationName: 'Audit',
                title: t`No clients yet`,
                description: t`You don't have any clients assigned to you right now. Once you're added to a client engagement in Drata, you'll see it here.`,
            }}
            onFetchData={loadAuditorClientList}
            onRowClick={({ row }) => {
                navigate(`/audit-hub/clients/${row.id}/audits`);
            }}
        />
    );
});
