import { t } from '@globals/i18n/macro';

export const ERROR_MESSAGES = {
    get AUDIT_START_FAILED() {
        return t`Failed to start audit`;
    },
    get AUDIT_START_DESCRIPTION() {
        return t`An error occurred while starting the audit. Try again later.`;
    },
    get CSV_VALIDATION_FAILED() {
        return t`CSV validation failed`;
    },
    get CSV_VALIDATION_DESCRIPTION() {
        return t`Please check the file and try again`;
    },
    get CONTROL_EVIDENCE_FAILED() {
        return t`Failed to generate control evidence`;
    },
    get CONTROL_EVIDENCE_DESCRIPTION() {
        return t`An error occurred while generating the control evidence. Try again later.`;
    },
    get NO_FILE_PROVIDED() {
        return t`No file provided for validation`;
    },
    get AUDIT_DATA_UNAVAILABLE() {
        return t`Audit data is not available`;
    },
} as const;

export const ERROR_IDS = {
    AUDIT_START: 'audit-start-error',
    CSV_VALIDATION: 'csv-validation-failed',
    CONTROL_EVIDENCE: 'generate-control-evidence-error',
    EVIDENCE_GENERATION: 'evidence-generation-error',
    EVIDENCE_GENERATION_MISSING_PARAMS:
        'evidence-generation-missing-params-error',
    EVIDENCE_GENERATION_NO_URL: 'evidence-generation-no-url-error',
} as const;
