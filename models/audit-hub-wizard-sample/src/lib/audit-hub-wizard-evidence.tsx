import { sharedAuditHubAuditController } from '@controllers/audit-hub';
import { But<PERSON> } from '@cosmos/components/button';
import { DEFAULT_MAX_FILE_SIZE_IN_BYTES } from '@cosmos/components/file-upload';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import type { FormSchema } from '@ui/forms';

export class AuditHubWizardEvidenceModel {
    constructor() {
        makeAutoObservable(this);
    }

    get evidenceFormSchema(): FormSchema {
        const { requestTemplate } = sharedAuditHubAuditController;

        const handleDownloadTemplate = () => {
            if (!requestTemplate) {
                return;
            }
            downloadFileFromSignedUrl(requestTemplate);
        };

        const formSchema = {
            evidenceOption: {
                type: 'radioGroup',
                label: '',
                initialValue: 'dont-use-custom',
                options: [
                    {
                        label: t`Don't use custom evidence request list`,
                        value: 'dont-use-custom',
                        helpText: t`Organize evidence based on how framework requirements map to DCF controls`,
                    },
                    {
                        label: t`Use custom evidence request list`,
                        value: 'use-custom',
                        helpText: t`Upload a file to organize evidence based on DCF controls you specify instead of framework requirements.`,
                    },
                ],
                cosmosUseWithCaution_forceOptionOrientation:
                    'vertical' as const,
            },
            customEvidenceRequest: {
                type: 'group',
                header: '',
                showDivider: false,
                shownIf: {
                    fieldName: 'evidenceOption',
                    operator: 'equals',
                    value: 'use-custom',
                },
                fields: {
                    templateInfo: {
                        type: 'custom',
                        label: '',
                        render: () => (
                            <Stack
                                direction="column"
                                gap="xl"
                                data-id="HLdmtrdT"
                            >
                                <Text>
                                    <Trans>
                                        Download and fill out the template, then
                                        upload once you&apos;re done. Note:
                                        Values in the &quot;Mapped DCF
                                        control&quot; column of the template
                                        must be valid DCF controls.
                                    </Trans>
                                </Text>
                                <Button
                                    label={t`Download template`}
                                    level="secondary"
                                    width="auto"
                                    onClick={handleDownloadTemplate}
                                />
                            </Stack>
                        ),
                    },
                    evidenceRequestList: {
                        type: 'file',
                        label: t`Upload request list`,
                        innerLabel: t`Or drop files here`,
                        selectButtonText: t`Upload files`,
                        removeButtonText: t`Remove file`,
                        acceptedFormats: ['csv'],
                        acceptedMimeTypes: [
                            'text/csv',
                            'application/vnd.ms-excel',
                        ],
                        maxFileSizeInBytes: DEFAULT_MAX_FILE_SIZE_IN_BYTES,
                        oneFileOnly: true,
                        key: 'evidence-file-upload',
                        initialValue: [],
                        initialFiles: [],
                        initialFilesWithErrors: [],
                        errorCodeMessages: {
                            'file-invalid-type': t`Only CSV files are accepted.`,
                            'file-too-large': t`File size is too large.`,
                            'too-many-files': t`Only one file can be uploaded.`,
                        },
                    },
                },
            },
        };

        return formSchema as FormSchema;
    }
}
