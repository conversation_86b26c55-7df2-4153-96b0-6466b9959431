import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { EvidenceRequestOverview } from '@views/audit-hub-evidence-request-details-overview';

export const meta: MetaFunction = () => {
    return [{ title: t`Audit hub - Overview` }];
};

const RequestDetailsOverview = (): React.JSX.Element => {
    return (
        <EvidenceRequestOverview
            data-id="vkjXpHrX"
            data-testid="RequestDetailsOverview"
        />
    );
};

export default RequestDetailsOverview;
