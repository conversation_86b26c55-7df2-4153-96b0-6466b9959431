import { useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { sharedProgrammaticNavigationController } from './programmatic-navigation-controller';

/**
 * Hook to initialize programmatic navigation.
 * Call this once in your app root or layout component.
 */
export const useProgrammaticNavigation = (): void => {
    const navigate = useNavigate();

    useEffect(() => {
        sharedProgrammaticNavigationController.setNavigateFunction(navigate);
    }, [navigate]);
};
