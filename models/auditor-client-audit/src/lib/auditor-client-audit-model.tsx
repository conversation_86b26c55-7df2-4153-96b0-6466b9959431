import { isArray, isEmpty, isNil, isNumber, isObject } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import {
    sharedAuditHubAuditorClientEvidenceStatusController,
    sharedAuditorFrameworkEvidenceQueryController,
    sharedCompanyArchiveStatusQueryController,
    sharedCompanyStatsQueryController,
    sharedCompanySummaryQueryController,
    sharedConnectionInfoQueryController,
    sharedTotalEvidenceQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedAuditHubAuditorClientMethodsAssetsController } from '@controllers/audit-hub-auditor-client-methods-assets';
import { sharedAuditHubAuditorClientMethodsConnectionsController } from '@controllers/audit-hub-auditor-client-methods-connections';
import { sharedAuditHubAuditorClientMethodsControlMappingController } from '@controllers/audit-hub-auditor-client-methods-control-mapping';
import { sharedAuditHubAuditorClientMethodsEvidenceLibraryController } from '@controllers/audit-hub-auditor-client-methods-evidence-library';
import { sharedAuditHubAuditorClientMethodsHumanResourcesController } from '@controllers/audit-hub-auditor-client-methods-human-resources';
import { sharedAuditHubAuditorClientMethodsInfrastructureController } from '@controllers/audit-hub-auditor-client-methods-infrastructure';
import { sharedAuditHubAuditorClientMethodsRequestPackageController } from '@controllers/audit-hub-auditor-client-methods-request-package';
import { sharedAuditHubAuditorClientMethodsVendorsController } from '@controllers/audit-hub-auditor-client-methods-vendors';
import { sharedAuditHubAuditorClientMethodsVersionControlController } from '@controllers/audit-hub-auditor-client-methods-version-control';
import { sharedAuditHubAuditorRefreshPackageController } from '@controllers/audit-hub-auditor-refresh-package';
import { sharedAuditHubAuditorValidatePersonnelController } from '@controllers/audit-hub-auditor-validate-personnel';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimensionSm } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import {
    FrameworkBadge,
    type FrameworkBadgeName,
} from '@cosmos-lab/components/framework-badge';
import type {
    AuditListResponseDto,
    AuditorFrameworkSummaryResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    areSampleDatesWithinAuditorFrameworkPeriod,
    getIsArchiveExpired,
    openHandleModal,
} from '@models/auditor-client-audit';
import { MAX_EVIDENCE_DIRECT_DOWNLOAD } from './auditor-client-audit-model-constants';
import { openHandleRequestModal } from './auditor-client-audit-request-modal';
import { openHandleViewPastRequestModal } from './auditor-client-audit-view-past-request-modal';
import { openChangeSampleEvidenceModal } from './change-sample-evidence-modal';

const isValidSummaryData = (
    data: AuditorFrameworkSummaryResponseDto | null,
): data is AuditorFrameworkSummaryResponseDto => {
    return !isNil(data) && isObject(data) && 'auditorFramework' in data;
};

interface DownloadOption {
    id: string;
    label: string;
    showItem: boolean;
    type: string;
    onClick?: () => void;
}
export class AuditorClientAuditModel {
    constructor() {
        makeAutoObservable(this);
    }

    get breadcrumbs(): Breadcrumb[] {
        const { clientId } = sharedCustomerRequestDetailsController;

        if (!clientId) {
            console.warn(
                'clientId is missing from sharedCustomerRequestDetailsController',
            );

            return [];
        }

        const { company } = sharedCurrentCompanyController;

        return [
            {
                label: t`Client List`,
                pathname: '/audit-hub/clients',
            },
            {
                label: `${company?.name}`,
                pathname: `/audit-hub/clients/${clientId}/audits`,
            },
        ];
    }

    get label(): string {
        const {
            isFileGenerationPending,
            isFileGenerationSuccess,
            isFileGenerationFailed,
        } = sharedAuditHubAuditorClientEvidenceStatusController;

        if (
            isFileGenerationPending ||
            sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusIsLoading ||
            sharedAuditHubAuditorClientMethodsRequestPackageController.downloadPreAuditPackageIsLoading
        ) {
            return t`Please wait while we generate your pre-audit packages....`;
        }

        const archiveData =
            sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusData;

        if (
            sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusDataFailed ||
            !archiveData?.companyArchiveUpdatedAt ||
            getIsArchiveExpired(
                new Date(archiveData.companyArchiveUpdatedAt),
            ) ||
            isFileGenerationFailed
        ) {
            return t`Request new package`;
        }

        if (
            isFileGenerationSuccess &&
            !getIsArchiveExpired(new Date(archiveData.companyArchiveUpdatedAt))
        ) {
            return t`Download all`;
        }

        // Default fallback case
        return t`Request new package`;
    }

    get subMenuItems(): DownloadOption[] {
        const expired =
            sharedAuditHubAuditorClientEvidenceStatusController.isEvidenceExpired;

        const isHumanResourcesDataValid =
            !isNil(sharedCompanySummaryQueryController.companySummary) &&
            isArray(sharedCompanySummaryQueryController.companySummary) &&
            !isEmpty(sharedCompanySummaryQueryController.companySummary);

        if (
            sharedTotalEvidenceQueryController.totalEvidenceByFrameworkIdDataIsLoading ||
            sharedCompanyStatsQueryController.isStatsLoading ||
            sharedConnectionInfoQueryController.isConnectionInfoLoading
        ) {
            return [
                {
                    id: 'download-all-option',
                    label: t`Loading...`,
                    showItem: true,
                    type: 'item',
                },
            ];
        }

        return [
            {
                id: 'download-all-option',
                label: this.label,
                showItem: true,
                type: 'item',
                onClick: () => {
                    expired
                        ? openHandleModal()
                        : sharedAuditHubAuditorClientMethodsRequestPackageController.downloadPreAuditPackage(
                              'PRE_AUDIT',
                          );
                },
            },
            {
                id: 'control-mapping-option',
                label: t`Control mapping`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsControlMappingController.loadGrcControllerDownloadControlsOptions();
                },
                showItem: true,
            },
            {
                id: 'connections-option',
                label: t`Connections`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsConnectionsController.handleDownloadConnections();
                },
                showItem: true,
            },
            {
                id: 'human-resources-option',
                label: t`Human Resources`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsHumanResourcesController.handleHumanResourcesDownload();
                },
                showItem: isHumanResourcesDataValid,
            },
            {
                id: 'vendors-option',
                label: t`Vendors`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsVendorsController.handleDownloadVendors();
                },
                showItem:
                    !isNil(sharedCompanyStatsQueryController.stats?.vendors) &&
                    sharedCompanyStatsQueryController.stats.vendors > 0,
            },
            {
                id: 'assets-option',
                label: t`Assets`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsAssetsController.handleDownloadAssets();
                },
                showItem:
                    !isNil(sharedCompanyStatsQueryController.stats?.assets) &&
                    sharedCompanyStatsQueryController.stats.assets > 0,
            },
            {
                id: 'evidence-library-option',
                label: t`Evidence library`,
                type: 'item',
                description: t`Manual evidence only`,
                onClick: () => {
                    if (
                        !isNil(
                            sharedTotalEvidenceQueryController
                                .totalEvidenceByFrameworkIdData?.total,
                        ) &&
                        isNumber(
                            sharedAuditHubController.auditByIdData?.framework
                                .productId,
                        )
                    ) {
                        sharedTotalEvidenceQueryController
                            .totalEvidenceByFrameworkIdData.total >
                        MAX_EVIDENCE_DIRECT_DOWNLOAD
                            ? sharedAuditHubAuditorClientMethodsEvidenceLibraryController.handleSendAllEvidenceByEmail(
                                  sharedAuditHubController.auditByIdData
                                      .framework.productId,
                              )
                            : sharedAuditHubAuditorClientMethodsEvidenceLibraryController.handleDownloadAllEvidenceDirectly();
                    }
                },
                showItem:
                    !isNil(
                        sharedTotalEvidenceQueryController
                            .totalEvidenceByFrameworkIdData?.total,
                    ) &&
                    sharedTotalEvidenceQueryController
                        .totalEvidenceByFrameworkIdData.total > 0,
            },
            {
                id: 'infrastructure-access-option',
                label: t`Evidence library`,
                type: 'item',
                description: t`Infrastructure access`,
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsInfrastructureController.loadUserIdentitiesControllerGetUserInfrastructureIdentities();
                },
                showItem:
                    !isNil(
                        sharedCompanyStatsQueryController.stats
                            ?.infrastructureUserIdentities,
                    ) &&
                    sharedCompanyStatsQueryController.stats
                        .infrastructureUserIdentities > 0,
            },
            {
                id: 'version-control-option',
                label: t`Evidence library`,
                type: 'item',
                description: t`Version control`,
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsVersionControlController.loadUserVersionControlIdentities();
                },
                showItem:
                    !isNil(
                        sharedCompanyStatsQueryController.stats
                            ?.versionControlUserIdentities,
                    ) &&
                    sharedCompanyStatsQueryController.stats
                        .versionControlUserIdentities > 0,
            },
        ].filter((items) => items.showItem);
    }

    get auditStatus(): AuditListResponseDto['status'] {
        if (sharedAuditorController.auditSummaryByIdQuery.hasError) {
            return 'ACTIVE';
        }

        const summaryData = sharedAuditorController.auditSummaryByIdData;

        if (
            isValidSummaryData(summaryData) &&
            summaryData.auditorFramework.status === 'COMPLETED'
        ) {
            return 'COMPLETED';
        }

        return 'ACTIVE';
    }

    get evidenceButton(): SchemaDropdownItemData | null {
        const summaryData = sharedAuditorController.auditSummaryByIdData;

        if (!isValidSummaryData(summaryData)) {
            return null;
        }
        const { auditorFramework, totalRequests } = summaryData;

        const auditId = sharedCustomerRequestsController.frameworkId;

        const areSampleDatesWithinPeriod =
            areSampleDatesWithinAuditorFrameworkPeriod(
                auditorFramework.startDate,
                auditorFramework.endDate,
                sharedAuditHubAuditorClientAuditController.auditDatesData,
            );

        const arePersonnelWithinPeriod =
            sharedAuditHubAuditorValidatePersonnelController.validationData
                ?.arePersonnelWithinAuditPeriod;

        const disableButton =
            !areSampleDatesWithinPeriod || !arePersonnelWithinPeriod;

        const isDownloadOnlyAudit =
            sharedAuditHubController.auditByIdData?.framework.auditType ===
            'DOWNLOAD_ONLY_AUDIT';

        if (!isDownloadOnlyAudit && totalRequests > 0) {
            sharedAuditHubAuditorRefreshPackageController.auditStarted = true;
        } else if (!isDownloadOnlyAudit && totalRequests === 0) {
            sharedAuditHubAuditorRefreshPackageController.auditStarted = false;
        } else if (isDownloadOnlyAudit) {
            sharedAuditHubAuditorRefreshPackageController.auditStarted = true;
        }

        const hasFileGenerationFailed =
            sharedAuditHubAuditorRefreshPackageController.auditStarted &&
            sharedAuditorFrameworkEvidenceQueryController
                .latestAuditorFrameworkEvidencePingData
                ?.companyArchiveStatus === 'FAILED';

        const isGenerateButtonVisible =
            sharedAuditHubAuditorRefreshPackageController.auditStarted &&
            sharedAuditHubAuditorClientEvidenceStatusController.isEvidenceExpired &&
            !hasFileGenerationFailed;

        const isFileGenerationPending =
            sharedAuditHubAuditorRefreshPackageController.auditStarted &&
            sharedAuditorFrameworkEvidenceQueryController
                .latestAuditorFrameworkEvidencePingData
                ?.companyArchiveStatus === 'PENDING' &&
            !isGenerateButtonVisible;

        const hasFileGenerationSucceeded =
            sharedAuditHubAuditorRefreshPackageController.auditStarted &&
            sharedAuditorFrameworkEvidenceQueryController
                .latestAuditorFrameworkEvidencePingData
                ?.companyArchiveStatus === 'SUCCESS' &&
            !isGenerateButtonVisible;

        if (
            !sharedAuditHubAuditorRefreshPackageController.auditStarted &&
            !isDownloadOnlyAudit
        ) {
            return {
                id: `audit-request-control-evidence-not-started`,
                label: t`Control evidence will be available after the auditor sets samples.`,
                type: 'item',
            };
        }

        if (isDownloadOnlyAudit && isFileGenerationPending) {
            return {
                id: `audit-request-control-evidence-check-back`,
                label: t`Control evidence is still being prepared. Check back soon.`,
                type: 'item',
            };
        }

        if (hasFileGenerationFailed) {
            return {
                id: `audit-request-control-evidence-try-again`,
                label: t`Couldn't create control evidence. Try again`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorRefreshPackageController.refreshControlEvidencePackage(
                        auditId,
                        sharedAuditHubController.auditByIdData?.framework
                            .productId as number,
                    );
                },
                startIconName: 'Download',
            };
        }

        if (!isDownloadOnlyAudit && isFileGenerationPending) {
            return {
                id: `audit-request-control-evidence-wait`,
                label: t`Please wait while we generate your control evidence...`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorRefreshPackageController.refreshControlEvidencePackage(
                        auditId,
                        sharedAuditHubController.auditByIdData?.framework
                            .productId as number,
                    );
                },
            };
        }

        if (hasFileGenerationSucceeded && !disableButton) {
            return {
                id: `audit-request-control-evidence-`,
                label: t`Download`,
                type: 'item',
                disabled: true,
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsRequestPackageController.downloadPreAuditPackage(
                        'CONTROL_EVIDENCE',
                    );
                },
                startIconName: 'Download',
            };
        }

        if (isGenerateButtonVisible && !disableButton) {
            return {
                id: `audit-request-control-evidence-`,
                label: t`Request evidence`,
                type: 'item',
                onClick: () => {
                    openHandleRequestModal();
                },
                startIconName: 'Download',
            };
        }

        return null;
    }

    get auditActions(): Action[] {
        const getAuditButton = () => {
            if (this.auditStatus === 'COMPLETED') {
                return {
                    id: 'audit-mark-as-active',
                    actionType: 'button' as const,
                    typeProps: {
                        label: t`Mark as active`,
                        level: 'secondary' as const,
                        startIconName: 'CheckCircle' as const,
                    },
                };
            }

            return {
                id: 'audit-complete-audit',
                actionType: 'button' as const,
                typeProps: {
                    label: t`Complete audit`,
                    level: 'secondary' as const,
                },
            };
        };

        return [
            {
                id: 'audit-actions-dropdown',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    startIconName: 'HorizontalMenu',
                    isIconOnly: true,
                    items: [
                        {
                            id: `audit-pre-audit-package`,
                            label: t`Pre audit package`,
                            type: 'subMenu',
                            startIconName: 'Download',
                            items: this.subMenuItems,
                        },
                        ...(this.evidenceButton ? [this.evidenceButton] : []),
                        {
                            id: `audit-view-past-downloads`,
                            label: t`View past downloads`,
                            type: 'item',
                            onClick: () => {
                                openHandleViewPastRequestModal();
                            },
                            startIconName: 'Visible',
                        },
                        {
                            id: `change-sample-evidence`,
                            label: t`Change sample evidence`,
                            type: 'item',
                            onClick: () => {
                                const { auditorFrameworkId } =
                                    sharedCustomerRequestDetailsController;

                                if (auditorFrameworkId) {
                                    openChangeSampleEvidenceModal(
                                        auditorFrameworkId,
                                    );
                                }
                            },
                            startIconName: 'Sync',
                        },
                    ],
                },
            },
            getAuditButton(),
        ];
    }

    get actionStack(): React.JSX.Element | undefined {
        return (
            <ActionStack
                isFullWidth
                gap={dimensionSm}
                maxWidth="100%"
                actions={this.auditActions}
            />
        );
    }

    get title(): string {
        return '';
    }

    get slot(): React.JSX.Element | undefined {
        const { auditDatesData } = sharedAuditHubAuditorClientAuditController;
        const { auditSummaryByIdData } = sharedAuditorController;
        const { company } = sharedCurrentCompanyController;

        const companyName = company?.name;
        const relatedFramework =
            auditSummaryByIdData?.auditorFramework.auditorFrameworkType
                .relatedFramework;

        if (!relatedFramework || !companyName) {
            return undefined;
        }

        const { name: frameworkName, tag: frameworkTag } = relatedFramework;

        const label = `${frameworkName} - ${companyName}`;

        return (
            <Box>
                <Grid
                    gap="4x"
                    data-testid="AuditorClientAuditDetailsViewHeader"
                    columns="1fr auto auto"
                >
                    <Stack gap="md" align="center">
                        {!isNil(label) && (
                            <FrameworkBadge
                                badgeName={frameworkTag as FrameworkBadgeName}
                            />
                        )}
                        <Text type="title" size="400">
                            {label}
                        </Text>
                    </Stack>
                </Grid>
                <Grid
                    gap="4x"
                    data-testid="AuditorClientAuditDetailsKeyValuePair"
                    columns="1fr auto auto"
                >
                    <KeyValuePair
                        type="REACT_NODE"
                        label=""
                        value={
                            <Stack direction="column" gap="sm" mt="lg">
                                <Text type="title" size="100">
                                    {t`Audit Period`}
                                </Text>
                                <Text type="body" size="100">
                                    {auditDatesData.join(' - ')}
                                </Text>
                            </Stack>
                        }
                    />
                </Grid>
            </Box>
        );
    }
}
