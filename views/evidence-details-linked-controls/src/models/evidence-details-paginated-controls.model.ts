import { isEmpty, orderBy } from 'lodash-es';
import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import type { EvidenceResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { sharedEvidenceDetailsModel } from '@models/evidence-library-details';

interface EvidenceDetailsPaginatedControls {
    data: EvidenceResponseDto['controls'];
    total: number;
    page: number;
    limit: number;
}

class EvidenceDetailsPaginatedControlsModel {
    controlEvidenceCurrentPage = 1;
    controlEvidencePageSize = DEFAULT_PAGE_SIZE;
    controlEvidenceSortId = '';
    controlEvidenceSortDirection: 'asc' | 'desc' | boolean = false;

    constructor() {
        makeAutoObservable(this);
    }

    get controls(): EvidenceResponseDto['controls'] {
        return sharedEvidenceDetailsModel.controls;
    }

    get paginatedControls(): EvidenceDetailsPaginatedControls {
        const startIndex =
            (this.controlEvidenceCurrentPage - 1) *
            this.controlEvidencePageSize;
        const endIndex = startIndex + this.controlEvidencePageSize;

        if (this.controlEvidenceSortId && this.controlEvidenceSortDirection) {
            const sortedControls = orderBy(
                this.controls,
                [this.controlEvidenceSortId],
                [this.controlEvidenceSortDirection],
            );

            return {
                data: sortedControls.slice(startIndex, endIndex),
                total: this.controls.length,
                page: this.controlEvidenceCurrentPage,
                limit: this.controlEvidencePageSize,
            };
        }

        return {
            data: this.controls.slice(startIndex, endIndex),
            total: this.controls.length,
            page: this.controlEvidenceCurrentPage,
            limit: this.controlEvidencePageSize,
        };
    }

    loadControls = (params: FetchDataResponseParams): void => {
        const { pagination, sorting } = params;
        const { page, pageSize } = pagination;

        this.controlEvidenceCurrentPage = page ?? 1;
        this.controlEvidencePageSize = pageSize;

        if (!isEmpty(sorting)) {
            const sort = sorting[0].id;
            const direction = sorting[0].desc ? 'desc' : 'asc';

            this.controlEvidenceSortId = sort;
            this.controlEvidenceSortDirection = direction;
        }
    };
}

export const sharedEvidenceDetailsPaginatedControlsModel =
    new EvidenceDetailsPaginatedControlsModel();
