import { isNil } from 'lodash-es';
import type { ComplianceCheckResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';

export const buildComplianceCheckStatusDescription = (
    status?: ComplianceCheckResponseDto['status'],
    exclusionEndDate?: string | null,
    lastCheckedAt?: string | null,
): string => {
    switch (status) {
        case 'EXCLUDED': {
            if (isNil(exclusionEndDate)) {
                return t`Indefinitely`;
            }

            const formattedEndDate = formatDate('table', exclusionEndDate);

            return t`Until ${formattedEndDate}`;
        }
        case 'PASS':
        case 'FAIL':
        case 'MISCONFIGURED':
        default: {
            if (isNil(lastCheckedAt)) {
                return t`Last checked unknown`;
            }

            const formattedLastCheckedAt = formatDate('overdue', lastCheckedAt);

            return t`Last checked ${formattedLastCheckedAt}`;
        }
    }
};
