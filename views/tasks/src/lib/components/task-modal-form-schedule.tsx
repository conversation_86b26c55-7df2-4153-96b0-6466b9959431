import { isEmpty } from 'lodash-es';
import { Feedback } from '@cosmos/components/feedback';
import { ToggleField } from '@cosmos/components/toggle-field';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { convertToISO8601String } from '@helpers/date-time';
import { sharedTaskForm } from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';
import { TaskModalFormScheduleDetails } from './task-modal-form-schedule-details';
import { TaskScheduleLabel } from './task-schedule-label';

export const TaskModalFormSchedule = observer(() => {
    const { isRecurringTask, scheduleDate, schedule } =
        sharedTaskForm.currentValues;

    const today = convertToISO8601String(new Date());
    const disableSchedule =
        !isEmpty(schedule) && scheduleDate && scheduleDate < today;

    if (disableSchedule) {
        return (
            <>
                <TaskScheduleLabel schedule={schedule} />
                <Feedback
                    title={t`The recurring schedule of this past-due task cannot be edited.`}
                    description={t`To make changes, please update a task in the series that is not past due.`}
                    severity="warning"
                />
            </>
        );
    }

    return (
        <>
            <ToggleField
                formId={TASKS_FORM_ID}
                name="isRecurringTask"
                checked={isRecurringTask}
                label={t`Make this a recurring task`}
                helpText={t`You can eliminate the need to manually schedule this in the future with a recurring task if it's something you plan on doing again.`}
                onChange={sharedTaskForm.update('isRecurringTask')}
            />
            {isRecurringTask && <TaskModalFormScheduleDetails />}
        </>
    );
});
