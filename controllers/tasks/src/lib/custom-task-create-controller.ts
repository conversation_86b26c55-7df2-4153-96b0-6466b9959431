import { snackbarController } from '@controllers/snackbar';
import { customTasksControllerPostCreateCustomTaskMutation } from '@globals/api-sdk/queries';
import type { CustomTaskBaseRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedCustomTasksController } from './custom-tasks-controller';

export class CustomTaskCreateController {
    constructor() {
        makeAutoObservable(this);
    }

    createTaskMutation = new ObservedMutation(
        customTasksControllerPostCreateCustomTaskMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'task-create-success',
                    props: {
                        title: t`Task created successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                sharedCustomTasksController.loadAll();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'task-create-api-error',
                    props: {
                        title: t`Failed to create task`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    async saveTask(body: CustomTaskBaseRequestDto): Promise<void> {
        const { currentWorkspace } = sharedWorkspacesController;
        const xWorkspaceId = currentWorkspace?.id;

        if (!xWorkspaceId) {
            snackbarController.addSnackbar({
                id: 'task-create-error-workspace',
                props: {
                    title: t`Failed to create task`,
                    description: t`No workspace selected`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        return this.createTaskMutation.mutateAsync({
            path: { xWorkspaceId },
            body,
        });
    }
}
