import { isNil } from 'lodash-es';
import {
    AccessReviewApplicationCell,
    AccessReviewApplicationSourceCell,
    AccessReviewApplicationStatusCell,
    CompletedReviewActionCell,
    WarningsCell,
} from '@components/access-review';
import type { DatatableProps } from '@cosmos/components/datatable';
import type {
    AccessReviewPeriodApplicationResponseDto,
    ClientTypeEnum,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    PROVIDER_FILTERS,
    type ProviderFilter,
} from '../constants/provider-filters.constants';

interface ApplicationWarnings {
    levelChange: number | null | undefined;
    formerPersonnel: number | null | undefined;
    missingMfa: number | null | undefined;
    unlinkedIdentities: number | null | undefined;
    serviceAccounts: number | null | undefined;
}

class CompletedDatatableModel {
    constructor() {
        makeAutoObservable(this);
    }

    executeOverCustomFilterValidations(
        clientType: ClientTypeEnum,
        source: AccessReviewPeriodApplicationResponseDto['source'],
        executableFunction: (
            client: ProviderFilter,
            warnings: ApplicationWarnings,
        ) => number,
        warnings: ApplicationWarnings,
    ): number {
        if (source === 'MANUALLY_ADDED') {
            return executableFunction(
                PROVIDER_FILTERS.MANUAL_APPLICATION,
                warnings,
            );
        }

        if (clientType === 'MICROSOFT_365') {
            return source === 'DIRECT_CONNECTION'
                ? executableFunction(PROVIDER_FILTERS.MICROSOFT_365, warnings)
                : executableFunction(
                      PROVIDER_FILTERS.MICROSOFT_365_PARTNER,
                      warnings,
                  );
        }
        if (clientType === 'OKTA_IDENTITY') {
            return source === 'DIRECT_CONNECTION'
                ? executableFunction(PROVIDER_FILTERS.OKTA_IDENTITY, warnings)
                : executableFunction(
                      PROVIDER_FILTERS.OKTA_IDENTITY_PARTNER,
                      warnings,
                  );
        }

        if (!isNil(PROVIDER_FILTERS[clientType])) {
            return executableFunction(PROVIDER_FILTERS[clientType], warnings);
        }
        if (clientType.startsWith('STACKONE')) {
            return executableFunction(PROVIDER_FILTERS.STACKONE, warnings);
        }
        if (clientType.startsWith('MERGEDEV')) {
            return executableFunction(PROVIDER_FILTERS.MERGEDEV, warnings);
        }

        return executableFunction(PROVIDER_FILTERS.DEFAULT, warnings);
    }

    getTotalWarningsByCustomFilters(
        clientType: ClientTypeEnum | null | undefined,
        source: AccessReviewPeriodApplicationResponseDto['source'],
        application: AccessReviewPeriodApplicationResponseDto,
    ): number {
        if (isNil(clientType) || isNil(source) || isNil(application)) {
            return 0;
        }

        const warnings: ApplicationWarnings = {
            levelChange: application.levelChange,
            formerPersonnel: application.formerPersonnel,
            missingMfa: application.missingMfa,
            unlinkedIdentities: application.unlinkedIdentity,
            serviceAccounts: application.serviceAccount,
        };

        return this.executeOverCustomFilterValidations(
            clientType,
            source,
            (
                providerFilters: ProviderFilter,
                providerWarnings: ApplicationWarnings,
            ) => {
                let totalWarnings = 0;

                if (providerFilters.warningFilters.formerPersonnelWithAccess) {
                    totalWarnings =
                        totalWarnings + (providerWarnings.formerPersonnel ?? 0);
                }

                if (providerFilters.warningFilters.missingMFA) {
                    totalWarnings =
                        totalWarnings + (providerWarnings.missingMfa ?? 0);
                }

                if (providerFilters.warningFilters.unlinkUsers) {
                    totalWarnings =
                        totalWarnings +
                        (providerWarnings.unlinkedIdentities ?? 0);
                }

                if (providerFilters.warningFilters.accessLevelChange) {
                    totalWarnings =
                        totalWarnings + (providerWarnings.levelChange ?? 0);
                }

                if (providerFilters.warningFilters.serviceAccount) {
                    totalWarnings =
                        totalWarnings + (providerWarnings.serviceAccounts ?? 0);
                }

                return totalWarnings;
            },
            warnings,
        );
    }
    getLabels(label: AccessReviewPeriodApplicationResponseDto['source']) {
        switch (label) {
            case 'DIRECT_CONNECTION': {
                return t`Direct connection`;
            }
            case 'MANUALLY_ADDED': {
                return t`Manually added`;
            }
            case 'PARTNER_CONNECTION': {
                return t`Partner connection`;
            }
            default: {
                return '-';
            }
        }
    }

    get datatableColumns(): DatatableProps<AccessReviewPeriodApplicationResponseDto>['columns'] {
        return [
            {
                accessorKey: 'id',
                header: '',
                id: 'actions',
                enableSorting: false,
                size: 50,
                cell: CompletedReviewActionCell,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
            {
                accessorKey: 'name',
                header: t`Application`,
                id: 'Application',
                enableSorting: false,
                size: 200,
                cell: AccessReviewApplicationCell,
            },
            {
                accessorKey: 'status',
                header: t`Review status`,
                id: 'status',
                enableSorting: false,
                size: 200,
                cell: AccessReviewApplicationStatusCell,
            },
            {
                accessorKey: 'clientType',
                header: t`Warnings`,
                id: 'totalWarnings',
                enableSorting: false,
                size: 150,
                cell: WarningsCell,
            },
            {
                accessorKey: 'source',
                header: t`Type`,
                id: 'source',
                enableSorting: false,
                size: 600,
                cell: AccessReviewApplicationSourceCell,
            },
        ];
    }
}

export const sharedDatatable = new CompletedDatatableModel();
