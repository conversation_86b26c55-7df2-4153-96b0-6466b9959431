import { useMemo } from 'react';
import {
    SchemaDropdown,
    type SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

const updateRequest = (requestId: number, newStatus: string) => {
    console.info(
        `Request with ID: ${requestId} should be updated with the following status: ${newStatus}`,
    );
    // update in https://drata.atlassian.net/browse/ENG-70498
};

interface ActionsCellProps {
    row: { original: CustomerRequestListItemResponseDto };
}

export const ActionsCell = ({
    row: { original },
}: ActionsCellProps): React.JSX.Element => {
    const options: SchemaDropdownItems = useMemo(
        () =>
            [
                {
                    id: 'changeToNew',
                    value: 'OUTSTANDING',
                    label: t`Change to new`,
                    onSelect: () => {
                        updateRequest(original.id, 'OUTSTANDING');
                    },
                },
                {
                    id: 'changeToPrepared',
                    value: 'IN_REVIEW',
                    label: t`Change to prepared`,
                    onSelect: () => {
                        updateRequest(original.id, 'IN_REVIEW');
                    },
                },
                {
                    id: 'changeToCompleted',
                    value: 'ACCEPTED',
                    label: t`Change to completed`,
                    onSelect: () => {
                        updateRequest(original.id, 'ACCEPTED');
                    },
                },
            ].filter((option) => option.value !== original.status),
        [original.id, original.status],
    );

    return (
        <SchemaDropdown
            isIconOnly
            label={t`Actions`}
            items={options}
            level="tertiary"
            startIconName="Action"
            data-testid="ActionsCell"
            colorScheme="neutral"
            data-id="i-w3iJ00"
        />
    );
};
