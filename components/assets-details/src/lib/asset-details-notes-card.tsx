import { ViewEditCardComponent } from '@components/view-edit-card';
import { activeAssetController } from '@controllers/asset';
import { EmptyState } from '@cosmos/components/empty-state';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { AssetsDetailsNotesFormComponent } from './assets-details-notes-form-component';

interface AssetDetailsNotesCardProps {
    isEditable?: boolean;
}

export const AssetsDetailNotesCard = observer(
    ({ isEditable = true }: AssetDetailsNotesCardProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const {
            assetDetails,
            updateAssetNotesMethod,
            updateAssetNotesIsPending,
            updateAssetNotesHasError,
        } = activeAssetController;

        const handleOnSubmit = (values: FormValues) => {
            if (!assetDetails?.id) {
                return;
            }

            updateAssetNotesMethod(assetDetails.id, values);
        };

        const NotesCardBody = () =>
            assetDetails?.notes ? (
                <Text
                    data-testid="NotesCardBody"
                    data-id="asset-details-notes-card-content"
                >
                    {assetDetails.notes}
                </Text>
            ) : (
                <Stack align="start" justify="start">
                    <EmptyState
                        isInline
                        isStacked
                        data-id="asset-details-notes-card-empty-state"
                        title={t`No notes available`}
                        description={t`Add notes to track feedback, questions or important details.`}
                    />
                </Stack>
            );

        return (
            <ViewEditCardComponent
                title={t`Notes`}
                readOnlyComponent={<NotesCardBody />}
                isMutationPending={updateAssetNotesIsPending}
                hasMutationError={updateAssetNotesHasError}
                data-testid="AssetsDetailNotesCard"
                data-id="gsYGYdma"
                editComponent={
                    isEditable ? (
                        <AssetsDetailsNotesFormComponent
                            formRef={formRef}
                            state={assetDetails}
                            onSubmit={handleOnSubmit}
                        />
                    ) : null
                }
                onSave={triggerSubmit}
            />
        );
    },
);
