export interface ProviderWarningFilters {
    formerPersonnelWithAccess: boolean;
    missingMFA: boolean;
    unlinkUsers: boolean;
    accessLevelChange: boolean;
    serviceAccount: boolean;
}

export interface ProviderFilter {
    personnel: boolean;
    warnings: boolean;
    permissions: boolean;
    warningFilters: ProviderWarningFilters;
    connection: boolean;
    employeeStatus: boolean;
    group: boolean;
}

interface ProviderFilters {
    [key: string]: ProviderFilter;
}

export const PROVIDER_FILTERS: ProviderFilters = {
    AUTH0: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    GITHUB: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    GITLAB: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    GITLAB_ON_PREM: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },

    // INFRASTRUCTURE
    AWS: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    GCP: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    AZURE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    AZURE_ORG_UNITS: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    HEROKU: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    CLOUDFLARE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false, // The API doesn't mentioned service accounts in the endpoint: https://api.cloudflare.com/client/v4/accounts/{account_id}/members
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },

    MICROSOFT_365: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false, // // MS365 & Azure /users endpoint doesn't return service accounts.
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    // MS third party Apps
    MICROSOFT_365_PARTNER: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    OKTA_IDENTITY: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    // OKTA third party apps
    OKTA_IDENTITY_PARTNER: {
        personnel: true,
        warnings: true,
        permissions: false,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    STACKONE_IRONCLAD: {
        personnel: true,
        warnings: true,
        permissions: false,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    STACKONE_NETLIFY: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    STACKONE_TWILIO: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    STACKONE_OPENAI: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    STACKONE_BOX: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    STACKONE_DROPBOX: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    STACKONE_DROPBOX_SIGN: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    STACKONE_HARVEST: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    STACKONE_KAMELEOON: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    STACKONE_MAKE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    STACKONE_RETOOL: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    STACKONE_TOGGL: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    STACKONE_ANTHROPIC: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    // DEFAULT FOR MERGEDEV APPS
    MERGEDEV: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    // DEFAULT FOR STACKONE APPS
    STACKONE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    DATADOG: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    SLACK: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    ZOOM: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    HUBSPOT: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MIRO: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    PAGER_DUTY: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    SEGMENT: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    DOCUSIGN: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    SNOWFLAKE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: true,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    MERGEDEV_AHA: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MERGEDEV_BITBUCKET: {
        personnel: true,
        warnings: false,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: false,
            missingMFA: false,
            unlinkUsers: false,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MERGEDEV_FRESHDESK: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MERGEDEV_FRESHSERVICE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MERGEDEV_HIVE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MERGEDEV_WRIKE: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MERGEDEV_FRONT: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MERGEDEV_ZOHO_DESK: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    XERO: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: false,
    },
    AZURE_DEVOPS: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: false,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
    MANUAL_APPLICATION: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: false,
        employeeStatus: true,
        group: false,
    },
    DEFAULT: {
        personnel: true,
        warnings: true,
        permissions: true,
        warningFilters: {
            formerPersonnelWithAccess: true,
            missingMFA: false,
            unlinkUsers: true,
            accessLevelChange: false,
            serviceAccount: true,
        },
        connection: true,
        employeeStatus: true,
        group: true,
    },
};
