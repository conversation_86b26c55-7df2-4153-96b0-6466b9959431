import { Accordion } from '@cosmos/components/accordion';
import { RiskDetailsBodyComponent } from './components/risk-details-body-component';

interface RiskDetailsAccordionProps {
    id: string;
    title: string;
    description: string;
    owners?: {
        firstName: string;
        lastName: string;
        avatarUrl?: string;
    }[];
    onRemoveRisk?: () => void;
    'data-id'?: string;
}

export const RiskDetailsAccordionComponent = ({
    id,
    title,
    description,
    owners,
    onRemoveRisk,
}: RiskDetailsAccordionProps): React.JSX.Element => {
    return (
        <Accordion
            title={title}
            data-testid="RiskDetailsAccordionComponent"
            data-id="PzxPvTaZ"
            body={
                <RiskDetailsBodyComponent
                    id={id}
                    description={description}
                    owners={owners}
                    data-id={`risk-details-body-${id}`}
                    onRemoveRisk={onRemoveRisk}
                />
            }
        />
    );
};
