import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import {
    vendorsControllerCreateVendorSecurityReviewMutation,
    vendorsControllerGetVendorsSecurityReviewsOptions,
} from '@globals/api-sdk/queries';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import type { NavigateFunction } from '@remix-run/react';
import { sharedVendorsDetailsController } from './vendors-details-controller';

class VendorsProfileSecurityReviewsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorId: number | null = null;

    paginatedSecurityReviews = new ObservedQuery(
        vendorsControllerGetVendorsSecurityReviewsOptions,
    );
    createVendorSecurityReviewMutation = new ObservedMutation(
        vendorsControllerCreateVendorSecurityReviewMutation,
    );

    setVendorId = (vendorId: number) => {
        this.vendorId = vendorId;
    };

    loadPaginatedSecurityReviews = (params: FetchDataResponseParams) => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id) {
            return;
        }

        const { pagination, sorting } = params;
        const { page, pageSize } = pagination;

        const query: Required<
            Parameters<typeof vendorsControllerGetVendorsSecurityReviewsOptions>
        >[0]['query'] = {
            page,
            limit: pageSize,
        };

        if (!isEmpty(sorting)) {
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.paginatedSecurityReviews.load({
            query,
            path: { vendorId: vendorDetails.id },
        });
    };

    createNewSecurityReview = (navigate: NavigateFunction) => {
        if (!this.vendorId) {
            return;
        }
        const { user } = sharedCurrentUserController;
        const reviewDeadline = new Date().toISOString().split('T')[0];

        this.createVendorSecurityReviewMutation.mutate({
            body: {
                requesterUserId: Number(user?.id),
                reviewDeadlineAt: reviewDeadline,
                securityReviewStatus: 'IN_PROGRESS',
                securityReviewType: 'SECURITY',
            },
            path: { vendorId: this.vendorId },
        });

        when(
            () => !this.createVendorSecurityReviewMutation.isPending,
            () => {
                const {
                    response: responseSecurityReview,
                    hasError: hasErrorSecurityReview,
                } = this.createVendorSecurityReviewMutation;

                if (hasErrorSecurityReview) {
                    snackbarController.addSnackbar({
                        id: 'create-security-review-error',
                        props: {
                            title: t`Error while creating the security review`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (responseSecurityReview?.id) {
                    navigate(`${responseSecurityReview.id}`);
                } else {
                    console.error(
                        'Failed to get security review ID from response',
                    );
                }
            },
        );
    };
}

export const sharedVendorsProfileSecurityReviewsController =
    new VendorsProfileSecurityReviewsController();
