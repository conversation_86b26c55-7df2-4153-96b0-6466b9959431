// import { Banner } from '@cosmos/components/banner';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const VendorsSettingsSsoReadOnlyComponent = observer(
    (): React.JSX.Element => {
        const { vendorSettings } = sharedVendorsSettingsController;
        const isSsoEnabled = vendorSettings?.isSsoIntegration ?? false;

        return (
            <Stack
                direction="column"
                gap="lg"
                data-testid="VendorsSettingsSsoReadOnlyComponent"
                data-id="sso-settings-read-only"
            >
                <Stack direction="column" gap="sm">
                    <Text allowBold type="body" size="100">
                        {t`Single sign-on suggestions from Okta`}
                    </Text>
                </Stack>

                {isSsoEnabled ? (
                    <Text type="body" size="200" colorScheme="faded">
                        {t`Enabled`}
                    </Text>
                ) : (
                    <Text type="body" size="200" colorScheme="faded">
                        {t`Disabled`}
                    </Text>
                )}
            </Stack>
        );
    },
);
