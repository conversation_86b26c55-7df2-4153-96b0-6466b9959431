import type { FrameworkResponseDto } from '@globals/api-sdk/types';

export const FrameworkTagMap = {
    9999: 'CUSTOM' as FrameworkResponseDto['tag'],
    0: 'NONE',
    1: 'SOC_2',
    2: 'ISO27001',
    3: 'CCPA',
    4: 'GDPR',
    5: 'HIPAA',
    6: 'PCI',
    7: 'SCF',
    8: 'NIST80053',
    9: 'NISTCSF',
    10: 'CMMC',
    11: 'NIST800171',
    12: 'MSSSPA',
    13: 'FFIEC',
    14: 'ISO27701',
    15: 'COBIT',
    16: 'SOX_ITGC',
    17: 'ISO270012022',
    18: 'CCM',
    19: 'CYBER_ESSENTIALS',
    20: 'ISO270172015',
    21: 'ISO270182019',
    22: 'FEDRAMP',
    23: 'NISTAI',
    24: 'PCI4',
    25: 'NISTCSF2',
    26: 'NIS2',
    27: 'DORA',
    28: 'ISO420012023',
    29: 'DRATA_ESSENTIALS',
    30: 'NIST800171R3',
    31: 'CIS8',
    32: 'CYBER_ESSENTIALS_32',
    33: 'FEDRAMP20X',
    34: 'HITRUST',
} as const satisfies Record<number, FrameworkResponseDto['tag']>;
