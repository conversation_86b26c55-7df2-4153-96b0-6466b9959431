import { isEmpty } from 'lodash-es';
import { sharedCompanyArchiveStatusQueryController } from '@controllers/audit-hub-auditor-client-actions';
import type { User } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction, when } from '@globals/mobx';

export interface FormattedTableData {
    signedUrl?: string;
    requester?: User;
    packageType: PackageType;
    requestedOn?: Date;
}

const getPackageTypeEnum = () =>
    ({
        PRE_AUDIT: t`Pre-audit package`,
        CONTROL_EVIDENCE: t`Control evidence`,
    }) as const;

export type PackageType = ReturnType<
    typeof getPackageTypeEnum
>[keyof ReturnType<typeof getPackageTypeEnum>];

class AuditHubAuditorClientEvidencePackageTableController {
    tableData: FormattedTableData[] = [];
    isDataFetched = false;

    constructor() {
        makeAutoObservable(this);
    }

    fetchEvidencePackageTableData(): void {
        // Prevent multiple calls
        if (this.isDataFetched) {
            return;
        }

        this.isDataFetched = true;

        sharedCompanyArchiveStatusQueryController.loadLatestCompanyArchiveStatus(
            'PRE_AUDIT',
            'SUCCESS',
        );
        sharedCompanyArchiveStatusQueryController.loadLatestCompanyArchiveStatus(
            'CONTROL_EVIDENCE',
            'SUCCESS',
        );

        when(
            () =>
                !sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusIsLoading &&
                !sharedCompanyArchiveStatusQueryController.controlEvidenceArchiveStatusIsLoading,
            () => {
                const preAuditData =
                    sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusData;
                const controlEvidenceData =
                    sharedCompanyArchiveStatusQueryController.controlEvidenceArchiveStatusData;

                runInAction(() => {
                    // Clear existing data before adding new data
                    this.tableData = [];

                    if (!isEmpty(preAuditData)) {
                        this.tableData.push({
                            signedUrl: preAuditData.signedUrl,
                            requester: preAuditData.companyArchiveRequestor,
                            packageType: getPackageTypeEnum().PRE_AUDIT,
                            requestedOn: preAuditData.companyArchiveUpdatedAt
                                ? new Date(preAuditData.companyArchiveUpdatedAt)
                                : undefined,
                        });
                    }

                    if (!isEmpty(controlEvidenceData)) {
                        this.tableData.push({
                            signedUrl: controlEvidenceData.signedUrl,
                            requester:
                                controlEvidenceData.companyArchiveRequestor,
                            packageType: getPackageTypeEnum().CONTROL_EVIDENCE,
                            requestedOn:
                                controlEvidenceData.companyArchiveUpdatedAt
                                    ? new Date(
                                          controlEvidenceData.companyArchiveUpdatedAt,
                                      )
                                    : undefined,
                        });
                    }
                });
            },
        );
    }

    get isLoading(): boolean {
        return (
            sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusIsLoading ||
            sharedCompanyArchiveStatusQueryController.controlEvidenceArchiveStatusIsLoading
        );
    }

    resetDataFetchedFlag(): void {
        this.isDataFetched = false;
        this.tableData = [];
    }
}

export const sharedAuditHubAuditorClientEvidencePackageTableController =
    new AuditHubAuditorClientEvidencePackageTableController();
