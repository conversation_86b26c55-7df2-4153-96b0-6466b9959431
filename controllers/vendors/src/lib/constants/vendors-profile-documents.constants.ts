import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';

export const VENDORS_DETAILS_TABS_DEFAULT_PARAMS = {
    pagination: {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    },
    globalFilter: {
        search: '',
        filters: {},
    },
    sorting: [],
} as const satisfies FetchDataResponseParams;

export const STUB_VENDORS_SECURITY_REVIEW_DOCUMENTS_DATA = {
    isLoading: false,
    pdfDownloadUrl: {
        title: 'Document title',
        // This is an example to use pdf file
        // signedUrl: 'https://pdfobject.com/pdf/sample.pdf',
        // This is an example to use image instead of pdf - Uncomment the line below to use it and comment line above
        // signedUrl:
        //     'https://www.researchgate.net/profile/Rohit-<PERSON>hul-2/publication/333650058/figure/fig4/AS:766997583364098@1559877882442/Sample-document-images.ppm',
        // This is an example to use office document instead of pdf - Uncomment the line below to use it and comment line above
        signedUrl: 'https://filesamples.com/samples/document/docx/sample4.docx',
    },
};
