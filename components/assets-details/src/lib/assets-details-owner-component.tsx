import { ViewEditCardComponent } from '@components/view-edit-card';
import { activeAssetController } from '@controllers/asset';
import {
    KeyValuePair,
    type KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { getUserRolesLabels } from './asset-details.constants';
import { AssetsDetailsOwnerFormComponent } from './assets-details-owner-form-component';

const assetOwnerRolesToKVCValueAdaptor = (
    roles?: UserResponseDto['roles'],
): KeyValuePairProps['value'] => {
    return (
        (roles ?? [])
            .map((role: string) => getUserRolesLabels(role))
            .join(', ') || '-'
    );
};

interface AssetDetailsOwnerComponentProps {
    isEditable?: boolean;
}

export const AssetsDetailsOwnerComponent = observer(
    ({
        isEditable = true,
    }: AssetDetailsOwnerComponentProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const {
            assetDetails,
            updateAssetOwnerMethod,
            updateAssetOwnerMutation,
        } = activeAssetController;

        const handleOnSubmit = action((values: FormValues) => {
            if (!assetDetails?.id || updateAssetOwnerMutation.isPending) {
                return;
            }
            updateAssetOwnerMethod(assetDetails.id, values);
        });

        const ownerFullName = getFullName(
            assetDetails?.owner.firstName,
            assetDetails?.owner.lastName,
        );

        const OwnerCardBody = () => (
            <Stack
                gap="4x"
                direction="column"
                data-testid="OwnerCardBody"
                data-id="-PbVYnsO"
            >
                <AvatarIdentity
                    primaryLabel={ownerFullName}
                    secondaryLabel={assetDetails?.owner.email || ''}
                    imgSrc={assetDetails?.owner.avatarUrl || ''}
                    fallbackText={getInitials(ownerFullName)}
                />
                <KeyValuePair
                    label={t`Owner status`}
                    value={assetOwnerRolesToKVCValueAdaptor(
                        assetDetails?.owner.roles,
                    )}
                />
            </Stack>
        );

        return (
            <ViewEditCardComponent
                title={t`Owner`}
                readOnlyComponent={<OwnerCardBody />}
                data-testid="AssetsDetailsOwnerComponent"
                data-id="RZzkzsTd"
                editComponent={
                    isEditable && assetDetails?.assetType === 'VIRTUAL' ? (
                        <AssetsDetailsOwnerFormComponent
                            formRef={formRef}
                            state={assetDetails}
                            onSubmit={handleOnSubmit}
                        />
                    ) : null
                }
                onSave={triggerSubmit}
            />
        );
    },
);
