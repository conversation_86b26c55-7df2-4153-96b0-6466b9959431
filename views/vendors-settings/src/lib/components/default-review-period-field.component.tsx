import { isString } from 'lodash-es';
import type React from 'react';
import { Box } from '@cosmos/components/box';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import {
    type CustomFieldRenderProps,
    useUniversalFieldController,
} from '@ui/forms';

export const DefaultReviewPeriodField = ({
    'data-id': dataId,
    name,
    formId,
    label,
}: CustomFieldRenderProps): React.JSX.Element => {
    const [field] = useUniversalFieldController(name);

    return (
        <Stack
            direction="column"
            gap="sm"
            data-testid="DefaultReviewPeriodField"
            data-id="k9J0ch-3"
        >
            <Text type="title" size="300" colorScheme="neutral">
                {label}
            </Text>
            <Text type="body" size="200" colorScheme="faded">
                {t`Scheduled questionnaires will be sent on this date.`}
            </Text>
            <FormField
                data-id={dataId}
                label=""
                formId={formId}
                name={name}
                data-testid="DefaultReviewPeriodField"
                feedback={{
                    type: 'error',
                    message: field.error?.message,
                }}
                renderInput={({ inputTestId }) => {
                    return (
                        <Stack
                            direction="row"
                            gap="sm"
                            align="center"
                            data-id="KDr4d7iu"
                        >
                            <Box width="40px">
                                <TextField
                                    required
                                    shouldHideLabel
                                    label={t`days before the deadline`}
                                    data-id={inputTestId}
                                    formId={formId}
                                    disabled={field.disabled}
                                    name={name}
                                    aria-label={t`Enter the number of days before the deadline`}
                                    value={
                                        isString(field.value) ? field.value : ''
                                    }
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                />
                            </Box>
                            <Text type="body" size="200" colorScheme="neutral">
                                {t`days before the deadline`}
                            </Text>
                        </Stack>
                    );
                }}
            />
        </Stack>
    );
};
