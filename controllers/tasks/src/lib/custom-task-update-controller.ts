import { snackbarController } from '@controllers/snackbar';
import {
    customTasksControllerGetCustomTaskOptions,
    customTasksControllerPutUpdateCustomTaskMutation,
} from '@globals/api-sdk/queries';
import type { CustomTaskBaseRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { queryClient } from '@globals/query-client';
import { sharedTaskModalState } from '@models/tasks';
import { sharedCustomTasksController } from './custom-tasks-controller';
import type { TaskGetParams } from './types/task-get-params.types';

export class CustomTaskUpdateController {
    constructor() {
        makeAutoObservable(this);
    }

    updateTaskMutation = new ObservedMutation(
        customTasksControllerPutUpdateCustomTaskMutation,
        {
            onSuccess: ({ id: taskId }) => {
                snackbarController.addSnackbar({
                    id: 'task-update-success',
                    props: {
                        title: t`Task updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Remove the specific task data from cache completely
                const { queryKey } = customTasksControllerGetCustomTaskOptions({
                    path: { taskId },
                });

                // First try to remove the query completely
                queryClient.removeQueries({ queryKey });

                // Then invalidate any remaining queries as a fallback
                queryClient.invalidateQueries({ queryKey }).catch((error) => {
                    console.error('Error invalidating task data query', {
                        error,
                        taskId,
                    });
                });

                sharedCustomTasksController.loadAll();
                sharedTaskModalState.closeModal();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'task-update-api-error',
                    props: {
                        title: t`Failed to update task`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    async saveTask(
        params: TaskGetParams,
        data: CustomTaskBaseRequestDto,
    ): Promise<void> {
        const { taskId } = params;

        if (!taskId) {
            snackbarController.addSnackbar({
                id: 'task-update-error',
                props: {
                    title: t`Invalid task id`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        return this.updateTaskMutation.mutateAsync({
            path: { taskId },
            body: { ...data, id: taskId },
        });
    }
}
