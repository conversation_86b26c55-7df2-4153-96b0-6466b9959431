import { isEmpty, isNil } from 'lodash-es';
import {
    type RecurringScheduleSpec,
    RecurringType,
    Weekday,
} from '@drata/recurring-schedule';
import type { CustomTaskFullResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getFullName, getInitials } from '@helpers/formatters';
import {
    getDayOptions,
    getTaskCardinalOptions,
    getTaskEndsOptions,
    getTaskFrequencyOptions,
    getTasksWeekdaysOptions,
} from '@models/tasks';
import type {
    RecurringScheduleSpecValues,
    TaskDataValues,
    TaskFormValues,
} from '../types/task-form-values.type';

export const mapAssignee = (
    assignee: CustomTaskFullResponseDto['assignee'],
): TaskFormValues['assignee'] => {
    if (isNil(assignee)) {
        return assignee;
    }
    const userFullName = getFullName(assignee.firstName, assignee.lastName);

    const label = userFullName;

    const avatar = {
        imgSrc: assignee.avatarUrl,
        imgAlt: t`User avatar`,
        fallbackText: getInitials(userFullName),
    };

    return Object.assign(assignee, {
        id: `${assignee.id}`,
        label,
        avatar,
        avatarUrl: assignee.avatarUrl ?? '',
    });
};

export const mapControlData = (
    control: CustomTaskFullResponseDto['control'],
): TaskFormValues['control'] => {
    if (isNil(control)) {
        return control;
    }

    return {
        id: `${control.id}`,
        value: `${control.id}`,
        title: control.name,
        code: control.code,
        isReady: control.isReady,
        label: control.name,
        description: control.description ?? '',
        taskId: control.taskId ?? undefined,
        configurationId: control.configurationId ?? undefined,
    };
};

export const mapRiskData = (
    risk: CustomTaskFullResponseDto['risk'],
): TaskFormValues['risk'] => {
    if (isNil(risk)) {
        return risk;
    }

    return {
        id: `${risk.id}`,
        title: risk.title,
        riskId: risk.riskId,
        label: risk.title,
        description: risk.description,
        taskId: risk.taskId ?? undefined,
        configurationId: risk.configurationId ?? undefined,
    };
};

export const sortDaysOfWeek = (daysOfWeek: Weekday[]): Weekday[] => {
    const daysOfWeekIndex = Object.values(Weekday);

    return daysOfWeek.sort((a, b) => {
        return daysOfWeekIndex.indexOf(a) - daysOfWeekIndex.indexOf(b);
    });
};

export const mapWeeklySchedule = (
    schedule: RecurringScheduleSpec,
): RecurringScheduleSpecValues | undefined => {
    if (isNil(schedule) || schedule.type !== RecurringType.WEEKLY) {
        return undefined;
    }

    const endsOnOptions = getTaskEndsOptions();
    const ENDS_NEVER = endsOnOptions[0].value;
    const ENDS_ON = endsOnOptions[1].value;

    return {
        scheduleType: getTaskFrequencyOptions()[0],
        scheduleInterval: schedule.interval,
        scheduleDaysOfWeek: sortDaysOfWeek(schedule.daysOfWeek),
        scheduleEndsOn: schedule.end ? ENDS_ON : ENDS_NEVER,
        scheduleEnd: schedule.end,
    };
};

export const mapMonthlySchedule = (
    schedule: RecurringScheduleSpec,
): RecurringScheduleSpecValues | undefined => {
    if (isNil(schedule) || schedule.type !== RecurringType.MONTHLY) {
        return undefined;
    }

    const dayOptions = getDayOptions();
    const weekdayOptions = getTasksWeekdaysOptions();
    const weekNumberOptions = getTaskCardinalOptions();

    return {
        scheduleType: getTaskFrequencyOptions()[1],
        scheduleInterval: schedule.interval,
        scheduleMonthlyType: schedule.dayOfMonth
            ? 'specificDayOfMonth'
            : 'specificDayOfWeek',
        scheduleDayOfMonth: dayOptions.find(
            (day) => day.id === `${schedule.dayOfMonth}`,
        ),
        scheduleWeekNumber: weekNumberOptions.find(
            (weekNumber) => weekNumber.id === `${schedule.weekNumber}`,
        ),
        scheduleWeekday: weekdayOptions.find(
            (week) => week.id === `${schedule.weekday}`,
        ),
    };
};

export const mapYearlySchedule = (
    schedule: RecurringScheduleSpec,
): RecurringScheduleSpecValues | undefined => {
    if (isNil(schedule) || schedule.type !== RecurringType.YEARLY) {
        return undefined;
    }

    const [, month, day] = schedule.start.split('-');
    const scheduleMonth = Number(month);
    const scheduleDay = Number(day);

    if (Number.isNaN(scheduleMonth) || Number.isNaN(scheduleDay)) {
        return undefined;
    }

    return {
        scheduleType: getTaskFrequencyOptions()[2],
        scheduleInterval: schedule.interval,
        scheduleDay,
        scheduleMonth,
    };
};

export const mapRecurringSchedule = (
    schedule: RecurringScheduleSpec,
): RecurringScheduleSpecValues | undefined => {
    if (isNil(schedule)) {
        return undefined;
    }
    if (schedule.type === RecurringType.WEEKLY) {
        return mapWeeklySchedule(schedule);
    }
    if (schedule.type === RecurringType.MONTHLY) {
        return mapMonthlySchedule(schedule);
    }

    return mapYearlySchedule(schedule);
};

export const mapTaskData = (
    taskData: CustomTaskFullResponseDto,
): TaskFormValues => {
    const isRecurringTask = !isEmpty(taskData.schedule);

    return {
        taskId: taskData.id,
        title: taskData.title,
        description: taskData.description ?? '',
        dueDate: taskData.dueDate,
        assignee: mapAssignee(taskData.assignee),
        isRecurringTask,
        scheduleDate: isRecurringTask ? taskData.dueDate : null,
        completedAt: taskData.completedAt ?? null,
        taskType: taskData.taskType,
        control: mapControlData(taskData.control),
        risk: mapRiskData(taskData.risk),
        schedule: taskData.schedule as RecurringScheduleSpec,
    };
};

export const mapTaskDataToFormValues = (
    taskData: CustomTaskFullResponseDto,
): TaskDataValues => {
    return {
        ...mapTaskData(taskData),
        ...mapRecurringSchedule(taskData.schedule as RecurringScheduleSpec),
    };
};
