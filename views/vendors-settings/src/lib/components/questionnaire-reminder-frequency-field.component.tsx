import { isString } from 'lodash-es';
import type React from 'react';
import { Box } from '@cosmos/components/box';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import {
    type CustomFieldRenderProps,
    useUniversalFieldController,
} from '@ui/forms';

export const QuestionnaireReminderFrequencyField = ({
    'data-id': dataId,
    name,
    formId,
    label,
}: CustomFieldRenderProps): React.JSX.Element => {
    const [field] = useUniversalFieldController(name);

    return (
        <Stack
            direction="column"
            gap="sm"
            data-testid="QuestionnaireReminderFrequencyField"
            data-id="questionnaire-reminder-frequency-field"
        >
            <Text type="title" size="300" colorScheme="neutral">
                {label}
            </Text>
            <Text type="body" size="200" colorScheme="faded">
                {t`Send reminders at this interval until the vendor completes the questionnaire`}
            </Text>
            <FormField
                data-id={dataId}
                label=""
                formId={formId}
                name={name}
                data-testid="QuestionnaireReminderFrequencyField"
                feedback={{
                    type: 'error',
                    message: field.error?.message,
                }}
                renderInput={({ inputTestId }) => {
                    return (
                        <Stack
                            direction="row"
                            gap="sm"
                            align="center"
                            data-id="reminder-frequency-input"
                        >
                            <Box width="40px">
                                <TextField
                                    required
                                    shouldHideLabel
                                    data-id={inputTestId}
                                    formId={formId}
                                    name={name}
                                    aria-label={t`Enter the number of days for reminder frequency`}
                                    disabled={field.disabled}
                                    label={t`days after initial request`}
                                    value={
                                        isString(field.value) ? field.value : ''
                                    }
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                />
                            </Box>
                            <Text type="body" size="200" colorScheme="neutral">
                                {t`days after initial request`}
                            </Text>
                        </Stack>
                    );
                }}
            />
        </Stack>
    );
};
