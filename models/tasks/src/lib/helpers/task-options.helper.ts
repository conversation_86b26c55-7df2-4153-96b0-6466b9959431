import type { CheckboxOption } from '@cosmos/components/checkbox-field-group';
import type { ListBoxItems } from '@cosmos/components/list-box';
import type { RadioOption } from '@cosmos/components/radio-field-group';
import { RecurringType } from '@drata/recurring-schedule';
import { t } from '@globals/i18n/macro';

export const getTaskTypeOptions = () =>
    [
        {
            id: 'CONTROL',
            label: t`Controls`,
            value: 'CONTROL',
        },
        {
            id: 'EVIDENCE',
            label: t`Evidence`,
            value: 'EVIDENCE',
        },
        {
            id: 'GENERAL',
            label: t`General`,
            value: 'GENERAL',
        },
        {
            id: 'POLICY_RENEWALS',
            label: t`Policies`,
            value: 'POLICY_RENEWALS',
        },
        {
            id: 'VENDOR',
            label: t`Vendors`,
            value: 'VENDOR',
        },
        {
            id: 'CONTROL_APPROVALS',
            label: t`Required control approvals`,
            value: 'CONTROL_APPROVALS',
        },
        {
            id: 'POLICY_APPROVALS',
            label: t`Required policy approvals`,
            value: 'POLICY_APPROVALS',
        },
    ] as const satisfies ListBoxItems;

export const getTaskOwnerOptions = () =>
    [
        {
            id: 'drata',
            label: t`Drata`,
            value: 'drata',
        },
        {
            id: 'agent',
            label: t`Agent`,
            value: 'agent',
        },
        {
            id: 'workspace',
            label: t`Workspace`,
            value: 'workspace',
        },
    ] as const satisfies ListBoxItems;

export const getTaskFrequencyOptions = () =>
    [
        { id: 'WEEK', label: t`Week`, value: RecurringType.WEEKLY },
        { id: 'MONTH', label: t`Month`, value: RecurringType.MONTHLY },
        { id: 'YEAR', label: t`Year`, value: RecurringType.YEARLY },
    ] as const satisfies ListBoxItems;

export const getTaskRepeatOnOptions = () =>
    [
        { label: t`Specific day of the month`, value: 'specificDayOfMonth' },
        { label: t`Specific day of the week`, value: 'specificDayOfWeek' },
    ] as const satisfies RadioOption[];

export const getTaskShortWeekdaysOptions = () =>
    [
        { label: t`Mon`, value: 'MONDAY' },
        { label: t`Tue`, value: 'TUESDAY' },
        { label: t`Wed`, value: 'WEDNESDAY' },
        { label: t`Thu`, value: 'THURSDAY' },
        { label: t`Fri`, value: 'FRIDAY' },
        { label: t`Sat`, value: 'SATURDAY' },
        { label: t`Sun`, value: 'SUNDAY' },
    ] as const satisfies CheckboxOption[];

export const getTaskCardinalOptions = () =>
    [
        { id: '1', label: t`First`, value: '1' },
        { id: '2', label: t`Second`, value: '2' },
        { id: '3', label: t`Third`, value: '3' },
        { id: '4', label: t`Fourth`, value: '4' },
        { id: '5', label: t`Last`, value: '5' },
    ] as const satisfies ListBoxItems;

export const getTaskEndsOptions = () =>
    [
        { label: t`Never`, value: 'NEVER' },
        { label: t`On`, value: 'ON' },
    ] as const satisfies RadioOption[];

export const getTasksObjectTypeOptions = () =>
    [
        { label: t`None`, value: 'NONE' },
        { label: t`Control`, value: 'CONTROL' },
        { label: t`Risk`, value: 'RISK' },
    ] as const satisfies RadioOption[];

export const getTasksWeekdaysOptions = () =>
    [
        { id: 'MONDAY', label: t`Monday`, value: 'MONDAY' },
        { id: 'TUESDAY', label: t`Tuesday`, value: 'TUESDAY' },
        { id: 'WEDNESDAY', label: t`Wednesday`, value: 'WEDNESDAY' },
        { id: 'THURSDAY', label: t`Thursday`, value: 'THURSDAY' },
        { id: 'FRIDAY', label: t`Friday`, value: 'FRIDAY' },
        { id: 'SATURDAY', label: t`Saturday`, value: 'SATURDAY' },
        { id: 'SUNDAY', label: t`Sunday`, value: 'SUNDAY' },
    ] as const satisfies ListBoxItems;
