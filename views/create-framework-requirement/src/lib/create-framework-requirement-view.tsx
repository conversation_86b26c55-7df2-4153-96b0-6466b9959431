import { useCallback, useEffect, useMemo } from 'react';
import { z } from 'zod';
import { CategoryFieldWithAddButton } from '@components/frameworks';
import { sharedRequirementCreateController } from '@controllers/requirements';
import { snackbarController } from '@controllers/snackbar';
import { ActionStack } from '@cosmos/components/action-stack';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { useLingui } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate, useParams } from '@remix-run/react';
import {
    type CustomFieldRenderProps,
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';

const FORM_ID = 'create-framework-requirement-form';

export const CreateFrameworkRequirementView = observer(
    (): React.JSX.Element => {
        const { t } = useLingui();
        const { formRef, triggerSubmit } = useFormSubmit();

        const location = useLocation();
        const { pathname } = location;

        const parentRoute = getParentRoute(pathname);
        const navigate = useNavigate();
        const { workspaceId, frameworkId } = useParams();

        const { isCustomFrameworksEnabled } = sharedEntitlementFlagController;

        useEffect(() => {
            if (!isCustomFrameworksEnabled) {
                navigate(parentRoute);
            }
        }, [navigate, parentRoute, isCustomFrameworksEnabled]);

        const { isRequirementCategoriesLoading } =
            sharedRequirementCreateController;

        useEffect(() => {
            sharedRequirementCreateController.reset();

            if (frameworkId) {
                sharedRequirementCreateController.setFrameworkId(
                    Number(frameworkId),
                );
            }
        }, [frameworkId]);

        const handleNavigateToRequirements = useCallback(() => {
            const targetUrl = `/workspaces/${workspaceId}/compliance/frameworks/all/current/${frameworkId}/requirements`;

            navigate(targetUrl);
        }, [navigate, workspaceId, frameworkId]);

        const handleCancel = useCallback(() => {
            navigate(parentRoute);
        }, [navigate, parentRoute]);

        const handleSave = useCallback(() => {
            triggerSubmit().catch(() => {
                console.error('Form submission failed');

                snackbarController.addSnackbar({
                    id: 'requirement-save-error',
                    props: {
                        title: t`Unable to save requirement`,
                        description: t`An error occurred while saving the requirement. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
        }, [triggerSubmit, t]);

        const renderCategoryField = useCallback(
            (fieldProps: CustomFieldRenderProps) => {
                return (
                    <CategoryFieldWithAddButton
                        {...fieldProps}
                        data-id="B96Kdqj1"
                    />
                );
            },
            [],
        );

        const formSchema = useMemo(
            (): FormSchema => ({
                requirementCode: {
                    type: 'text',
                    label: t`Requirement code`,
                    helpText: t`This code should uniquely identify the requirement`,
                    validator: z.string({
                        required_error: t`Requirement code is required`,
                    }),
                },
                requirementName: {
                    type: 'text',
                    label: t`Requirement name`,
                    helpText: t`This name should succinctly describe the requirement`,
                    validator: z.string({
                        required_error: t`Requirement name is required`,
                    }),
                },
                requirementCategory: {
                    type: 'custom',
                    label: t`Category`,
                    render: renderCategoryField,
                    isOptional: true,
                },
                description: {
                    type: 'textarea',
                    label: t`Description`,
                    helpText: t`This description should explain the requirement standards`,
                    isOptional: true,
                },
                additionalInfo: {
                    type: 'textarea',
                    label: t`Additional info`,
                    helpText: t`Any additional information you need to capture should go here`,
                    isOptional: true,
                },
            }),
            [t, renderCategoryField],
        );

        const handleSubmit = useCallback(
            (values: FormValues) => {
                sharedRequirementCreateController.saveRequirement(
                    values,
                    handleNavigateToRequirements,
                );
            },
            [handleNavigateToRequirements],
        );

        const actionStacks = useMemo(
            () => [
                {
                    id: 'action-buttons',
                    actions: [
                        {
                            id: 'save-button',
                            actionType: 'button' as const,
                            typeProps: {
                                label: t`Save`,
                                level: 'primary' as const,
                                onClick: handleSave,
                            },
                        },
                        {
                            id: 'cancel-button',
                            actionType: 'button' as const,
                            typeProps: {
                                label: t`Cancel`,
                                level: 'secondary' as const,
                                onClick: handleCancel,
                            },
                        },
                    ],
                    alignment: 'start' as const,
                },
            ],
            [handleSave, handleCancel, t],
        );

        if (isRequirementCategoriesLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        return (
            <Stack
                direction="column"
                gap="2xl"
                height="100%"
                data-testid="CreateFrameworkRequirementView"
                data-id="pf4kAiKi"
            >
                <Stack direction="column" gap="6x" flexGrow="1">
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId={FORM_ID}
                        schema={formSchema}
                        data-id="create-framework-requirement-form"
                        onSubmit={handleSubmit}
                    />
                </Stack>
                <Stack direction="column" gap="2xl" height="100%" justify="end">
                    <Divider />
                    <ActionStack
                        data-id="form-footer-actions"
                        stacks={actionStacks}
                    />
                </Stack>
            </Stack>
        );
    },
);
