import { sharedVendorsSettingsController } from '@controllers/vendors';
import { type AllowedBackgroundToken, Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Organization } from '@cosmos-lab/components/organization';
import { observer } from '@globals/mobx';

interface CompanyPreviewComponentProps {
    backgroundColor?: AllowedBackgroundToken;
    'data-id'?: string;
}

export const CompanyPreviewComponent = observer(
    ({
        backgroundColor,
        'data-id': dataId = 'company-preview',
    }: CompanyPreviewComponentProps): React.JSX.Element => {
        const { companyDetails } = sharedVendorsSettingsController;

        return (
            <Stack
                display="flex"
                direction="row"
                align="center"
                width="100%"
                data-id={dataId}
            >
                <Box
                    borderWidth="borderWidth1"
                    borderColor="neutralBorderFaded"
                    borderRadius="borderRadiusMd"
                    backgroundColor={backgroundColor}
                    width="100%"
                    p="3x"
                >
                    <Stack
                        direction="row"
                        align="center"
                        data-id={`${dataId}-content`}
                        width="100%"
                        gap="2x"
                    >
                        {companyDetails.logoUrl ? (
                            <Organization
                                size="lg"
                                imgSrc={companyDetails.logoUrl}
                                imgAlt={companyDetails.name}
                                fallbackText={companyDetails.fallbackText}
                                data-id={`${dataId}-logo`}
                            />
                        ) : (
                            <Organization
                                size="lg"
                                fallbackText={companyDetails.fallbackText}
                                data-id={`${dataId}-logo`}
                            />
                        )}
                        <Text type="title" size="400">
                            {companyDetails.name}
                        </Text>
                    </Stack>
                </Box>
            </Stack>
        );
    },
);
