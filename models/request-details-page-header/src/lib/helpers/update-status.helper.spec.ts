import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { getStatusOptions } from '../constants/status.map.constants';
import { updateStatus } from './update-status.helper';

vi.mock('@controllers/customer-request-details', () => ({
    sharedCustomerRequestDetailsController: {
        updateCustomerRequestStatus: vi.fn().mockResolvedValue(undefined),
        deleteCustomerRequest: vi.fn().mockResolvedValue(undefined),
    },
}));

vi.mock('@controllers/snackbar', () => ({
    snackbarController: {
        addSnackbar: vi.fn(),
    },
}));

describe('updateStatus', () => {
    const statusOptions = getStatusOptions();

    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    test('should do nothing when requestId is falsy', () => {
        updateStatus('some-id', 0);
        const result = undefined;

        expect(
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus,
        ).not.toHaveBeenCalled();
        expect(result).toBeUndefined();
    });

    test('should update status to IN_REVIEW when that status is selected', () => {
        const requestId = 123;

        updateStatus(statusOptions.IN_REVIEW.id, requestId);
        const result = undefined;

        expect(
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus,
        ).toHaveBeenCalledWith({
            requestIds: [requestId],
            status: statusOptions.IN_REVIEW.id,
        });
        expect(result).toBeUndefined();
    });

    test('should update status to OUTSTANDING when that status is selected', () => {
        const requestId = 456;

        updateStatus(statusOptions.OUTSTANDING.id, requestId);
        const result = undefined;

        expect(
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus,
        ).toHaveBeenCalledWith({
            requestIds: [requestId],
            status: statusOptions.OUTSTANDING.id,
        });
        expect(result).toBeUndefined();
    });

    test('should call deleteCustomerRequest when DELETE status is selected', () => {
        const requestId = 789;

        sharedCustomerRequestDetailsController.auditorId = '123';

        updateStatus(statusOptions.DELETE.id, requestId);

        expect(
            sharedCustomerRequestDetailsController.deleteCustomerRequests,
        ).toHaveBeenCalledWith({
            requestIdList: [requestId],
        });
    });

    test('should show error snackbar when invalid status is provided', () => {
        const requestId = 101;
        const invalidStatusId = 'invalid-status';

        updateStatus(invalidStatusId, requestId);
        const result = undefined;

        expect(
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus,
        ).not.toHaveBeenCalled();
        expect(result).toBeUndefined();
    });

    test('should update status to ACCEPTED when that status is selected', () => {
        const requestId = 101;

        updateStatus(statusOptions.ACCEPTED.id, requestId);
        const result = undefined;

        expect(
            sharedCustomerRequestDetailsController.updateCustomerRequestStatus,
        ).toHaveBeenCalledWith({
            requestIds: [requestId],
            status: statusOptions.ACCEPTED.id,
        });
        expect(result).toBeUndefined();
    });
});
