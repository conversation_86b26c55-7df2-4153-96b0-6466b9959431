import { describe, expect, test } from 'vitest';
import type { TaskAccordionType } from '@controllers/tasks';
import { getAccordionConfig } from './get-accordion-config.helper';
import {
    getRequiredApprovalsTableColumns,
    getTaskTableColumns,
} from './get-task-list-columns.helper';

describe('getAccordionConfig', () => {
    describe('getAccordionConfig', () => {
        const TASKS_TABLE_COLUMNS = getTaskTableColumns();
        const REQUIRED_APPROVALS_TABLE_COLUMNS =
            getRequiredApprovalsTableColumns();

        const testCases: {
            type: TaskAccordionType;
            expected: {
                title: string;
                columns:
                    | typeof TASKS_TABLE_COLUMNS
                    | typeof REQUIRED_APPROVALS_TABLE_COLUMNS;
            };
        }[] = [
            {
                type: 'evidence',
                expected: {
                    title: 'Evidence renewals',
                    columns: TASKS_TABLE_COLUMNS,
                },
            },
            {
                type: 'policyRenewals',
                expected: {
                    title: 'Policy renewals',
                    columns: TASKS_TABLE_COLUMNS,
                },
            },
            {
                type: 'vendor',
                expected: {
                    title: 'Vendors reminders',
                    columns: TASKS_TABLE_COLUMNS,
                },
            },
            {
                type: 'general',
                expected: {
                    title: 'General',
                    columns: TASKS_TABLE_COLUMNS,
                },
            },
            {
                type: 'control',
                expected: {
                    title: 'Controls',
                    columns: TASKS_TABLE_COLUMNS,
                },
            },
            {
                type: 'policyApprovals',
                expected: {
                    title: 'Required policy approvals',
                    columns: REQUIRED_APPROVALS_TABLE_COLUMNS,
                },
            },
            {
                type: 'controlApprovals',
                expected: {
                    title: 'Required control approvals',
                    columns: REQUIRED_APPROVALS_TABLE_COLUMNS,
                },
            },
            {
                type: 'risk',
                expected: {
                    title: 'Risk management',
                    columns: TASKS_TABLE_COLUMNS,
                },
            },
        ];

        test.each(testCases)(
            'should return correct config for $type type',
            ({ type, expected }) => {
                const result = getAccordionConfig(type);

                expect(result).toStrictEqual({
                    title: expected.title,
                    columns: expected.columns,
                });
            },
        );

        describe('return type validation', () => {
            test('should return an object with required properties', () => {
                const result = getAccordionConfig('control');

                expect(result).toHaveProperty('title');
                expect(result).toHaveProperty('columns');
                expect(typeof result.title).toBe('string');
                expect(Array.isArray(result.columns)).toBeTruthy();
            });
        });

        describe('columns validation', () => {
            test('should return REQUIRED_APPROVALS_TABLE_COLUMNS for REQUIRED_APPROVALS type', () => {
                const types: TaskAccordionType[] = [
                    'policyApprovals',
                    'controlApprovals',
                ];

                types.forEach((type) => {
                    const result = getAccordionConfig(type);

                    expect(result.columns).toStrictEqual(
                        REQUIRED_APPROVALS_TABLE_COLUMNS,
                    );
                });
            });

            test('should return TASKS_TABLE_COLUMNS for all other types', () => {
                const types: TaskAccordionType[] = [
                    'evidence',
                    'policyRenewals',
                    'vendor',
                    'general',
                    'control',
                    'risk',
                ];

                types.forEach((type) => {
                    const result = getAccordionConfig(type);

                    expect(result.columns).toStrictEqual(TASKS_TABLE_COLUMNS);
                });
            });
        });

        describe('type safety', () => {
            test('should handle all possible TaskAccordionType values', () => {
                // This test will fail at compile time if we miss any TaskAccordionType
                const types: TaskAccordionType[] = [
                    'evidence',
                    'policyRenewals',
                    'vendor',
                    'general',
                    'control',
                    'policyApprovals',
                    'controlApprovals',
                    'risk',
                ];

                types.forEach((type) => {
                    expect(() => getAccordionConfig(type)).not.toThrow();
                });
            });

            test('should not accept invalid types', () => {
                expect(() =>
                    getAccordionConfig('INVALID_TYPE' as TaskAccordionType),
                ).toThrow('Invalid type: INVALID_TYPE');
            });
        });
    });
});
