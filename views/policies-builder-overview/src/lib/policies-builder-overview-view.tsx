import { isNil } from 'lodash-es';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { PoliciesBuilderOverviewDetailsComponent } from './components/policies-builder-overview-details.component';
import { PoliciesBuilderOverviewOwnerComponent } from './components/policies-builder-overview-owner.component';
import { PoliciesBuilderOverviewReviewComponent } from './components/policies-builder-overview-review.component';
import { PoliciesBuilderOverviewSkeletonComponent } from './components/policies-builder-overview-skeleton.component';

export const PoliciesBuilderOverviewView = observer((): React.JSX.Element => {
    const { isInitialLoading } = sharedPolicyBuilderController;
    const { latestPolicyVersion } = sharedPolicyBuilderModel;

    if (isInitialLoading) {
        return <PoliciesBuilderOverviewSkeletonComponent />;
    }

    if (isNil(latestPolicyVersion)) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    return (
        <Grid
            gap="4x"
            columns="repeat(2, 1fr)"
            align="start"
            data-testid="PoliciesBuilderOverviewView"
            data-id="iLfzXsxd"
        >
            <Box gridColumn="1">
                <PoliciesBuilderOverviewDetailsComponent />
            </Box>
            <Box gridColumn="2">
                <Grid gap="4x" rows="max-content max-content">
                    <PoliciesBuilderOverviewReviewComponent />
                    <PoliciesBuilderOverviewOwnerComponent />
                </Grid>
            </Box>
        </Grid>
    );
});
