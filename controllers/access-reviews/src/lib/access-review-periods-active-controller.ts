import { isEmpty, isNil } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { accessReviewPeriodControllerListActiveAccessReviewPeriodsOptions } from '@globals/api-sdk/queries';
import type {
    AccessReviewPeriodApplicationResponseDto,
    AccessReviewPeriodResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { providers } from '@globals/providers';

export type AccessReviewApplicationWithProvider =
    AccessReviewPeriodApplicationResponseDto & {
        provider: (typeof providers)[keyof typeof providers] | null;
    };

class AccessReviewPeriodsActiveController {
    currentPage = 1;
    pageSize = 10;
    hasLoadedData = false;

    static readonly PROVIDER_LOOKUP = Object.freeze(
        Object.fromEntries(
            Object.values(providers).map((provider) => [provider.id, provider]),
        ) as Record<string, (typeof providers)[keyof typeof providers]>,
    );

    constructor() {
        makeAutoObservable(this);
    }

    activeAccessReviewPeriods = new ObservedQuery(
        accessReviewPeriodControllerListActiveAccessReviewPeriodsOptions,
    );

    loadActiveAccessReviewPeriods = (params: FetchDataResponseParams) => {
        // Store pagination parameters for frontend pagination
        this.currentPage = params.pagination.page ?? 1;
        this.pageSize = params.pagination.pageSize;

        // Only load data from backend once, then just update pagination state
        if (this.hasLoadedData) {
            return;
        }

        // Load all data since backend doesn't respect pagination
        this.activeAccessReviewPeriods.load({
            query: {
                // Remove pagination parameters since backend ignores them
            },
        });
        this.hasLoadedData = true;
    };

    get activeAccessReviewPeriodsList(): AccessReviewPeriodResponseDto[] {
        return this.activeAccessReviewPeriods.data?.data ?? [];
    }

    get activeAccessReviewPeriod(): AccessReviewPeriodResponseDto | null {
        // We only have one active period at a time
        return this.activeAccessReviewPeriodsList[0] ?? null;
    }

    get hasActivePeriod(): boolean | undefined {
        if (this.activeAccessReviewPeriods.isLoading) {
            return undefined;
        }

        return !isEmpty(this.activeAccessReviewPeriods.data?.data);
    }

    get applications(): AccessReviewPeriodApplicationResponseDto[] {
        return (this.activeAccessReviewPeriod?.applications ?? []).map(
            (application) => ({
                ...application,
                clientType: this.getClientTypeFromApplication(application),
            }),
        );
    }

    get applicationsWithProviderInfo(): AccessReviewApplicationWithProvider[] {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;

        const paginatedApplications = this.applications.slice(
            startIndex,
            endIndex,
        );

        return paginatedApplications.map((application) =>
            this.setProviderInformationToApplication(application),
        );
    }

    get activeAccessReviewPeriodRange(): {
        startDate?: string;
        endDate?: string;
    } {
        return {
            startDate: this.activeAccessReviewPeriod?.startDate,
            endDate: this.activeAccessReviewPeriod?.endDate,
        };
    }

    get totalApplications(): number {
        return this.activeAccessReviewPeriod?.applications.length ?? 0;
    }

    get canCreateReviewPeriod(): boolean {
        return isNil(this.activeAccessReviewPeriod);
    }

    get isLoading(): boolean {
        return this.activeAccessReviewPeriods.isLoading;
    }

    private getClientTypeFromApplication(
        application: AccessReviewPeriodApplicationResponseDto,
    ): AccessReviewPeriodApplicationResponseDto['clientType'] | null {
        return application.clientType || application.connections[0]?.clientType;
    }

    private getProviderByClientType(
        clientType: AccessReviewPeriodApplicationResponseDto['clientType'],
    ): (typeof providers)[keyof typeof providers] | null {
        if (isNil(clientType)) {
            return null;
        }

        return (
            AccessReviewPeriodsActiveController.PROVIDER_LOOKUP[clientType] ??
            null
        );
    }

    private setProviderInformationToApplication(
        application: AccessReviewPeriodApplicationResponseDto,
    ): AccessReviewApplicationWithProvider {
        if (application.source !== 'DIRECT_CONNECTION') {
            return {
                ...application,
                provider: null,
            };
        }

        const provider = this.getProviderByClientType(application.clientType);

        return {
            ...application,
            logo: provider?.logo ?? application.logo,
            name: provider?.name ?? application.name,
            provider,
        };
    }
}

export const sharedActiveAccessReviewPeriodsController =
    new AccessReviewPeriodsActiveController();
