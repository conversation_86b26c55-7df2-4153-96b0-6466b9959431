import type { ColumnDef } from '@cosmos-lab/components/simple-table';
import { TaskActionsCell } from '../components/task-actions-cell';
import { TaskApproversCell } from '../components/task-approvers-cell';
import { TaskDueDateCell } from '../components/task-due-date-cell';
import { TaskOwnerCell } from '../components/task-owner-cell';
import { TaskTitleCell } from '../components/task-title-cell';

export const getTaskTableColumns = () =>
    [
        {
            id: 'title',
            header: 'Title',
            isHidden: true,
            size: '30%',
            cell: TaskTitleCell,
        },
        {
            id: 'dueDate',
            header: 'Due date',
            size: '25%',
            isHidden: true,
            cell: TaskDueDateCell,
        },
        {
            id: 'owner',
            header: 'Owner',
            isHidden: true,
            cell: TaskOwnerCell,
        },
        {
            id: 'actions',
            header: 'Actions',
            isHidden: true,
            cell: TaskActionsCell,
        },
    ] as const satisfies ColumnDef[];

export const getRequiredApprovalsTableColumns = () =>
    [
        {
            id: 'title',
            header: 'Title',
            isHidden: true,
            size: '40%',
            cell: TaskTitleCell,
        },
        {
            id: 'dueDate',
            header: 'Due date',
            isHidden: true,
            size: '30%',
            cell: TaskDueDateCell,
        },
        {
            id: 'owner',
            header: 'Owner',
            isHidden: true,
            cell: TaskApproversCell,
        },
        {
            id: 'actions',
            header: 'Actions',
            isHidden: true,
            cell: TaskActionsCell,
        },
    ] as const satisfies ColumnDef[];
