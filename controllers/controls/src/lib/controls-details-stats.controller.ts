import { filter, isNil } from 'lodash-es';
import { grcControllerGetControlDetailsByIdOptions } from '@globals/api-sdk/queries';
import type { ControlReadyDetailsResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { CheckResultStatus } from '../constants/check-result-status.constant';
import { CheckStatusNumber } from '../constants/check-status-number.constant';
import { TestSource } from '../constants/test-source.constant';

interface ReadinessSummary {
    ready: number;
    notReady: number;
    total: number;
}

export type ControlPolicySummary = ReadinessSummary;

export interface ControlMonitorSummary extends ReadinessSummary {
    testsInErrorState: number;
    inactiveTests: number;
    nonProductionTests: number;
    readyTests: number;
}

export interface ControlEvidenceSummary extends ReadinessSummary {
    testEvidence: number;
}

const TEST_RESULT_TYPE = 10;

class ControlsDetailsStatsController {
    constructor() {
        makeAutoObservable(this);
    }

    controlDetailsStatsQuery = new ObservedQuery(
        grcControllerGetControlDetailsByIdOptions,
    );

    invalidate = (): void => {
        this.controlDetailsStatsQuery.invalidate();
    };

    load = (controlId: number): void => {
        when(
            () =>
                sharedWorkspacesController.isLoaded &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                this.controlDetailsStatsQuery.load({
                    path: {
                        controlId,
                        xProductId: currentWorkspace?.id as number,
                    },
                });
            },
        );
    };

    get isLoading(): boolean {
        return this.controlDetailsStatsQuery.isLoading;
    }

    get controlId(): number {
        return this.controlDetailsStatsQuery.data?.controlId ?? 0;
    }

    get controlStats(): ControlReadyDetailsResponseDto | null {
        return this.controlDetailsStatsQuery.data ?? null;
    }

    get controlPoliciesStats(): ControlPolicySummary {
        const { policies } = this.controlStats ?? {};

        if (!policies) {
            return {
                ready: 0,
                notReady: 0,
                total: 0,
            };
        }

        const ready = policies.ready.length;
        const notReady = policies.notReady.length;

        return {
            ready,
            notReady,
            total: ready + notReady,
        };
    }

    get controlMonitorsStats(): ControlMonitorSummary {
        const { tests } = this.controlStats ?? {};

        if (!tests) {
            return {
                ready: 0,
                notReady: 0,
                testsInErrorState: 0,
                nonProductionTests: 0,
                total: 0,
                inactiveTests: 0,
                readyTests: 0,
            };
        }

        const ready = tests.ready.length;
        const notReady = tests.notReady.length;

        const testCheckResultStatuses = tests.checkResultStatuses ?? [];
        const testCheckSources = tests.checkSources ?? [];
        const testCheckStatuses = tests.checkStatuses ?? [];

        const testsInErrorState = filter(
            testCheckResultStatuses,
            CheckResultStatus.ERROR,
        ).length;

        const nonProductionTests = testCheckSources.filter(
            (t) => t === TestSource.ACORN,
        ).length;

        const readyTests = testCheckResultStatuses.filter((status, index) => {
            return (
                status === CheckResultStatus.READY &&
                testCheckSources[index] !== TestSource.ACORN &&
                testCheckStatuses[index] !== CheckStatusNumber.DISABLED
            );
        }).length;

        const inactiveTests = testCheckStatuses.filter((status, index) => {
            return (
                status === CheckStatusNumber.DISABLED &&
                testCheckSources[index] !== TestSource.ACORN
            );
        }).length;

        return {
            ready,
            notReady,
            total: testCheckStatuses.length,
            testsInErrorState,
            inactiveTests,
            nonProductionTests,
            readyTests,
        };
    }

    get controlEvidenceStats(): ControlEvidenceSummary {
        const { evidence } = this.controlStats ?? {};

        if (!evidence) {
            return {
                ready: 0,
                notReady: 0,
                testEvidence: 0,
                total: 0,
            };
        }

        const { library, external } = evidence;
        const ready = library.ready.length + external.ready.length;
        const notReady = library.notReady.length + external.notReady.length;

        const testEvidence =
            library.types?.filter((type) => type === TEST_RESULT_TYPE).length ??
            0;

        return {
            ready,
            notReady,
            testEvidence,
            total: ready + notReady + testEvidence,
        };
    }
}

export const sharedControlsDetailsStatsController =
    new ControlsDetailsStatsController();
