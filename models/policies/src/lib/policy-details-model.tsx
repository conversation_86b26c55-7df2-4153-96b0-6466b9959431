import { isEmpty } from 'lodash-es';
import { sharedControlPoliciesController } from '@controllers/controls';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import type {
    PolicyVersionResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';

// TODO: remove this model and start using PolicyBuilderModel

export class PolicyDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get lastVersionCreatedAt(): string | undefined {
        return this.#lastestPolicyVersion?.createdAt;
    }

    get lastVersionPublishedAt(): string | undefined {
        if (!this.#lastestPolicyVersion?.publishedAt) {
            return undefined;
        }

        return this.#lastestPolicyVersion.publishedAt;
    }

    get lastVersionApprovedAt(): string | undefined {
        if (!this.#lastestPolicyVersion?.approvedAt) {
            return undefined;
        }

        return this.#lastestPolicyVersion.approvedAt;
    }

    get versionNumber(): number | undefined {
        return this.#lastestPolicyVersion?.version;
    }

    get versionStatus():
        | PolicyVersionResponseDto['policyVersionStatus']
        | undefined {
        return this.#lastestPolicyVersion?.policyVersionStatus;
    }

    get renewalDate(): string | undefined {
        const { policy: policyDetails } = sharedPolicyBuilderController;

        if (!policyDetails?.latestPolicyVersion) {
            return undefined;
        }

        return policyDetails.latestPolicyVersion.renewalDate;
    }

    get currentOwner(): UserResponseDto | undefined {
        const { policy: policyDetails } = sharedPolicyBuilderController;

        if (!policyDetails) {
            return undefined;
        }

        return policyDetails.currentOwner;
    }

    get ownerLastVersion(): UserResponseDto | undefined {
        if (!this.#lastestPolicyVersion?.owner) {
            return undefined;
        }

        return this.#lastestPolicyVersion.owner;
    }

    get description(): string | undefined {
        const { policy: policyDetails } = sharedPolicyBuilderController;

        if (!policyDetails) {
            return undefined;
        }

        return policyDetails.currentDescription;
    }

    get shouldDisplaySLA(): boolean {
        const { policy: policyDetails } = sharedPolicyBuilderController;

        if (!policyDetails) {
            return false;
        }

        return (
            !isEmpty(policyDetails.policyGracePeriodSLAs) ||
            !isEmpty(policyDetails.policyP3MatrixSLAs) ||
            !isEmpty(policyDetails.policyWeekTimeFrameSLAs)
        );
    }

    get name(): string {
        const { policy: policyDetails } = sharedPolicyBuilderController;

        if (!policyDetails) {
            return '';
        }

        return policyDetails.name;
    }

    get currentIndex(): number {
        const { policy: policyDetails } = sharedPolicyBuilderController;

        return sharedControlPoliciesController.controlPolicies.findIndex(
            (controlPolicy) => controlPolicy.policy.id === policyDetails?.id,
        );
    }

    get totalItems(): number {
        return sharedControlPoliciesController.controlPolicies.length;
    }

    onPrevPageClick = (): void => {
        if (this.currentIndex === 0) {
            return;
        }

        const controlPolicyFound =
            sharedControlPoliciesController.controlPolicies[
                this.currentIndex - 1
            ];

        sharedPolicyBuilderController.loadPolicyWithAllData(
            controlPolicyFound.policy.id,
        );
    };

    onNextPageClick = (): void => {
        if (
            this.currentIndex ===
            sharedControlPoliciesController.controlPolicies.length - 1
        ) {
            return;
        }

        const controlPolicyFound =
            sharedControlPoliciesController.controlPolicies[
                this.currentIndex + 1
            ];

        sharedPolicyBuilderController.loadPolicyWithAllData(
            controlPolicyFound.policy.id,
        );
    };

    get #lastestPolicyVersion(): PolicyVersionResponseDto | undefined {
        const { policy: policyDetails } = sharedPolicyBuilderController;

        if (!policyDetails?.latestPolicyVersion) {
            return undefined;
        }

        return policyDetails.latestPolicyVersion;
    }
}

export const sharedPolicyDetailsModel = new PolicyDetailsModel();
