import { z } from 'zod';
import { sharedAssetCustomFieldsController } from '@controllers/assets';
import { sharedUsersInfiniteController } from '@controllers/users';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { adapterCustomFieldsToFormSchema } from '@helpers/custom-fields';
import { getFullName, getInitials } from '@helpers/formatters';
import type { FieldSchema, FormSchema } from '@ui/forms';
import {
    getAssetsClassFilterOptions,
    getAssetsTypeFilterOptions,
} from './constants/filters.constants';

export class AssetFormModel {
    assetClassCurrentSearch = '';

    constructor() {
        makeAutoObservable(this);
    }

    get currentUserAsOwner(): ListBoxItemData[] {
        const { user } = sharedCurrentUserController;

        if (!user) {
            return [];
        }

        const fullName = getFullName(user.firstName, user.lastName);

        return [
            {
                id: String(user.id),
                label: fullName,
                value: String(user.id),
                description: user.email,
                avatar: {
                    fallbackText: getInitials(fullName),
                    imgSrc: user.avatarUrl,
                    imgAlt: fullName,
                },
            },
        ];
    }

    nameFieldConfig(initialValue = ''): FieldSchema {
        return {
            type: 'text',
            label: t`Name`,
            validator: z.string().min(1, t`Name is required`),
            initialValue,
        };
    }

    descriptionFieldConfig(initialValue = ''): FieldSchema {
        return {
            type: 'text',
            label: t`Description`,
            validator: z.string().min(1, t`Description is required`),
            initialValue,
        };
    }

    notesFieldConfig(initialValue = ''): FieldSchema {
        return {
            type: 'textarea',
            label: t`Notes`,
            helpText: t`Additional notes about this asset`,
            maxCharacters: 192,
            isOptional: true,
            initialValue,
        };
    }

    get classOptions(): ListBoxItemData[] {
        const allOptions = getAssetsClassFilterOptions().filter(
            (option) => option.id !== 'ALL',
        );

        if (!this.assetClassCurrentSearch) {
            return allOptions;
        }

        return allOptions.filter(({ label }) =>
            label
                .toLowerCase()
                .includes(this.assetClassCurrentSearch.toLowerCase()),
        );
    }

    ownerFieldConfig(initialValue?: ListBoxItemData): FieldSchema {
        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;

        return {
            type: 'combobox',
            label: t`Owner`,
            isMultiSelect: false,
            validator: z.object({
                id: z.string(),
                label: z.string(),
                value: z.string(),
            }),
            initialValue: initialValue ?? this.currentUserAsOwner,
            options,
            hasMore: hasNextPage,
            isLoading: isFetching && isLoading,
            onFetchOptions: (params: {
                search?: string;
                increasePage?: boolean;
            }) => {
                onFetchUsers({
                    ...params,
                    withAllUsers: true,
                });
            },
            loaderLabel: t`Loading...`,
            removeAllSelectedItemsLabel: t`Remove all owners`,
            getSearchEmptyState: () => t`No results found`,
        };
    }

    classFieldConfig(initialValue: ListBoxItemData[] = []): FieldSchema {
        return {
            type: 'combobox',
            label: t`Class`,
            isMultiSelect: true,
            validator: z
                .array(
                    z.object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    }),
                )
                .min(1, t`At least one class is required`),
            initialValue,
            options: this.classOptions,
            getSearchEmptyState: () => t`No classes found`,
            loaderLabel: t`Loading classes...`,
            onFetchOptions: (params: { search?: string }) => {
                this.assetClassCurrentSearch = params.search ?? '';
            },
            removeAllSelectedItemsLabel: t`Clear all`,
            placeholder: t`Search by class`,
        };
    }

    typeFieldConfig(initialValue = 'VIRTUAL'): FieldSchema {
        return {
            type: 'radioGroup',
            label: t`Type`,
            validator: z.string().min(1, t`Type is required`),
            initialValue,
            options: getAssetsTypeFilterOptions().filter(
                (option) => option.id !== 'ALL',
            ),
            cosmosUseWithCaution_forceOptionOrientation: 'vertical',
        };
    }

    uniqueIdFieldConfig(initialValue = ''): FieldSchema {
        return {
            type: 'text',
            label: t`Unique ID`,
            helpText: t`The unique ID should be a serial number or hardware ID`,
            isOptional: true,
            initialValue,
            shownIf: {
                operator: 'or',
                conditions: [
                    {
                        fieldName: 'class',
                        operator: 'in',
                        value: ['HARDWARE'],
                    },
                    {
                        fieldName: 'class',
                        operator: 'in',
                        value: ['SOFTWARE'],
                    },
                ],
            },
        };
    }

    accountNameFieldConfig(initialValue = ''): FieldSchema {
        return {
            type: 'text',
            label: t`Account Name`,
            initialValue,
            disabled: true,
        };
    }

    assetIdFieldConfig(initialValue = ''): FieldSchema {
        return {
            type: 'text',
            label: t`AssetID`,
            initialValue,
            disabled: true,
        };
    }

    get customFields(): FormSchema {
        const { assetCustomFieldsList } = sharedAssetCustomFieldsController;

        return adapterCustomFieldsToFormSchema(assetCustomFieldsList);
    }

    get schema(): FormSchema {
        return {
            name: this.nameFieldConfig(),
            description: this.descriptionFieldConfig(),
            notes: this.notesFieldConfig(),
            class: this.classFieldConfig(),
            type: this.typeFieldConfig(),
            uniqueId: this.uniqueIdFieldConfig(),
            owner: this.ownerFieldConfig(),
            ...this.customFields,
        };
    }
}

export const sharedAssetFormModel = new AssetFormModel();
