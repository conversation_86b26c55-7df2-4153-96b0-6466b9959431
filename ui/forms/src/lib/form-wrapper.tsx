import { forwardRef, use<PERSON><PERSON>back, useImperative<PERSON><PERSON><PERSON>, useRef } from 'react';
import {
    type SubmitError<PERSON><PERSON><PERSON>,
    type SubmitHandler,
    useForm,
} from 'react-hook-form';
import { Stack } from '@cosmos/components/stack';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider } from './components/form-provider.component';
import { filterHiddenFields } from './helpers/filter-hidden-fields.helper';
import { useDefaultValues, useValidators } from './hooks';
import type { FormSchema } from './types/form-schema.type';
import type { FormValues } from './types/form-values.type';

export interface FormWrapperProps {
    /**
     * Unique testing ID for this element.
     * Used for automated testing and analytics tracking.
     */
    'data-id': string;

    /**
     * Unique identifier for the form.
     * Used to associate form fields with this specific form instance.
     */
    formId: string;

    /**
     * The content of the form.
     * Typically includes form fields, buttons, and other UI elements.
     * Can be any valid React node or a function that returns React nodes.
     */
    children: React.ReactNode;

    /**
     * The schema that defines the structure and validation rules of the form.
     * Contains field definitions, their types, validation rules, and initial values.
     */
    schema: FormSchema;
    /**
     * Handler function called when the form is successfully submitted.
     * Receives the validated form values as its argument.
     */
    onSubmit: SubmitHandler<FormValues>;

    /**
     * Optional handler function called when form submission fails due to validation errors.
     * Receives the validation errors and form values as its arguments.
     */
    onError?: SubmitErrorHandler<FormValues>;
    isReadOnly?: boolean;
}

const FormWrapperImpl = (
    {
        'data-id': dataId,
        formId,
        children,
        onSubmit,
        schema,
        isReadOnly = false,
    }: FormWrapperProps,
    ref: React.ForwardedRef<HTMLFormElement>,
): React.JSX.Element => {
    const hiddenFields = useRef(new Set<string>());
    const validators = useValidators(schema);
    const defaultValues = useDefaultValues(schema);
    const form = useForm<FormValues>({
        resolver: zodResolver(validators),
        defaultValues,
        mode: 'onSubmit',
        disabled: isReadOnly,
    });

    const handleOnSubmit = useCallback(
        async (values: FormValues) => {
            const filteredValues = filterHiddenFields(
                values,
                hiddenFields.current,
            );

            await onSubmit(filteredValues as FormValues);
        },
        [onSubmit],
    );

    useImperativeHandle(ref, () => {
        return {
            submitForm: async () => {
                const isValid = await form.trigger();

                if (isValid) {
                    await handleOnSubmit(form.getValues());
                } else {
                    const {
                        formState: { errors },
                    } = form;

                    console.error(errors, form.getValues());
                }

                return isValid;
            },
            clearForm: () => {
                form.reset();
            },
            getValues: () => {
                return form.getValues();
            },
        } as unknown as HTMLFormElement;
    }, [form, handleOnSubmit]);

    return (
        /* eslint-disable-next-line custom/enforce-data-id --
         * provider doesn't need these at all */
        <FormProvider
            methods={form}
            schema={schema}
            hiddenFields={hiddenFields}
            data-testid="FormWrapperImpl"
        >
            <form
                noValidate
                data-id={dataId}
                data-testid="dynamicForm"
                id={formId}
                ref={ref}
                onSubmit={form.handleSubmit(handleOnSubmit)}
            >
                <Stack direction="column" gap="lg">
                    {children}
                </Stack>
            </form>
        </FormProvider>
    );
};

export const FormWrapper = forwardRef(FormWrapperImpl);
