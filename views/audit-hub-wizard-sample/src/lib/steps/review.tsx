import { isString } from 'lodash-es';
import { sharedAuditHubAuditController } from '@controllers/audit-hub';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const Review = observer((): React.JSX.Element => {
    const { sampleWizardData } = sharedAuditHubAuditController;

    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="Review"
            data-id="gvqSzqOA"
        >
            <Text type="subheadline" size="400" as="p">
                {t`Review`}
            </Text>

            <Stack direction="column" gap="lg">
                <Stack direction="column" gap="xs">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Operating system`}
                    </Text>
                    <Text type="body" size="200">
                        {sampleWizardData.platform?.label}
                    </Text>
                </Stack>

                <Stack direction="column" gap="xs">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Audit period`}
                    </Text>
                    <Text type="body" size="200">
                        {isString(sampleWizardData.date)
                            ? sampleWizardData.date
                            : sampleWizardData.date?.join(' - ')}
                    </Text>
                </Stack>

                <Stack direction="column" gap="xs">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Personnel hired`}
                    </Text>

                    <Metadata
                        label={String(sampleWizardData.hiredPersonnel?.length)}
                        type="number"
                    />
                </Stack>

                <Stack direction="column" gap="xs">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Personnel terminated`}
                    </Text>
                    <Metadata
                        label={String(sampleWizardData.formerPersonnel?.length)}
                        type="number"
                    />
                </Stack>

                <Stack direction="column" gap="xs">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Personnel employed`}
                    </Text>

                    <Metadata
                        type="number"
                        label={String(
                            sampleWizardData.currentPersonnel?.length,
                        )}
                    />
                </Stack>

                <Stack direction="column" gap="xs">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Customize evidence`}
                    </Text>
                    <Text type="body" size="200">
                        {sampleWizardData.evidenceOption === 'dont-use-custom'
                            ? t`Don't use custom evidence request list`
                            : t`Use custom evidence request list`}
                    </Text>
                </Stack>
            </Stack>
        </Stack>
    );
});
