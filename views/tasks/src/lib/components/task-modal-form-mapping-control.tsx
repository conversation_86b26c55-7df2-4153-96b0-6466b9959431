import { ControlDetailsAccordionComponent } from '@components/control-details-accordion';
import { sharedControlsInfiniteListController } from '@controllers/controls';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { CustomTaskFullResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    mapControlData,
    sharedTaskForm,
    type TaskControlData,
} from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';

export const TaskModalFormMappingControl = observer(() => {
    const {
        currentValues: { taskType, control },
    } = sharedTaskForm;

    const {
        controlsComboboxOptions,
        controlsInfiniteList,
        hasNextPage,
        isLoading,
        onFetchControls,
    } = sharedControlsInfiniteListController;

    const setControl = (value: ListBoxItemData | undefined): void => {
        if (!value) {
            sharedTaskForm.update('control')({} as TaskControlData);

            return;
        }

        const controlData = controlsInfiniteList.find(
            (c) => c.id === Number(value.id),
        );

        const mappedControl = mapControlData(
            controlData as unknown as CustomTaskFullResponseDto['control'],
        );

        sharedTaskForm.update('control')(mappedControl);
    };

    const onRemoveControl = () => {
        sharedTaskForm.update('control')(null);
        sharedTaskForm.update('taskType')('GENERAL');
    };

    if (taskType !== 'CONTROL') {
        return null;
    }

    return (
        <>
            <ComboboxField
                formId={TASKS_FORM_ID}
                name="control"
                label={t`Select control`}
                helpText={t`Tasks can only be mapped to a single control`}
                loaderLabel={t`Loading controls...`}
                defaultValue={control ?? undefined}
                options={controlsComboboxOptions}
                hasMore={hasNextPage}
                isLoading={isLoading}
                onFetchOptions={onFetchControls}
                onChange={setControl}
            />
            {control && (
                <ControlDetailsAccordionComponent
                    id={control.id}
                    data-id={`task-modal-form-mapping-control-${control.id}`}
                    name={control.title}
                    code={control.code}
                    description={control.description}
                    isReady={control.isReady}
                    onRemoveControl={onRemoveControl}
                />
            )}
        </>
    );
});
