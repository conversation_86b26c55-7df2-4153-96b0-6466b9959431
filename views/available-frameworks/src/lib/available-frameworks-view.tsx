import { FrameworksReadinessToggleComponent } from '@components/frameworks';
import {
    type DisabledFrameworkResponseDto,
    sharedDisabledFrameworksController,
} from '@controllers/frameworks';
import { Datatable, type DatatableProps } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { DisabledFrameworkGalleryCard } from '../components/disabled-framework-gallery-card';

const COLUMNS =
    [] as const satisfies DatatableProps<DisabledFrameworkResponseDto>['columns'];

const TABLE_SEARCH_PROPS = {
    hideSearch: true,
};

export const AvailableFrameworksView = observer((): React.JSX.Element => {
    const { disabledFrameworks, isLoading, loadDisableFrameworks } =
        sharedDisabledFrameworksController;

    return (
        <Stack direction="column" gap="4x" data-id="hFE6A90F">
            <FrameworksReadinessToggleComponent />

            <Datatable
                isFullPageTable
                viewMode="gallery"
                columns={COLUMNS}
                isLoading={isLoading}
                tableId="datatable-frameworks"
                total={disabledFrameworks.total}
                data={disabledFrameworks.data}
                galleryCard={DisabledFrameworkGalleryCard}
                tableSearchProps={TABLE_SEARCH_PROPS}
                emptyStateProps={{
                    illustrationName: 'Warning',
                    title: t`Frameworks`,
                    description: t`no frameworks were found`,
                }}
                onFetchData={loadDisableFrameworks}
            />
        </Stack>
    );
});
