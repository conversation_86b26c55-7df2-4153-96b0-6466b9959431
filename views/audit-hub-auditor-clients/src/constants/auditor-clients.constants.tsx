import {
    type DatatableProps,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import type { AuditorClientResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ClientCell } from '../components/client-cell.component';
import { ContactCell } from '../components/contact-cell.component';
import { FrameworksCell } from '../components/frameworks-cell.component';

export const AUDITOR_CLIENTS_PAGINATION_OPTIONS = {
    defaultPaginationOptions: {
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
    },
};

export function getAuditorTableColumns(): DatatableProps<AuditorClientResponseDto>['columns'] {
    return [
        {
            accessorFn: (row) => row,
            header: t`Client`,
            id: 'CLIENT_NAME',
            cell: ClientCell,
        },
        {
            accessorFn: (row) => row,
            header: t`Contact`,
            id: 'CONTACT_NAME',
            cell: ContactCell,
        },
        {
            accessorKey: 'frameworks',
            header: t`Frameworks`,
            id: 'frameworks',
            cell: FrameworksCell,
            enableSorting: false,
        },
    ];
}
