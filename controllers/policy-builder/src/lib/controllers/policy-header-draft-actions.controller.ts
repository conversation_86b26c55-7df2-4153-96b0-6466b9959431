import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import { policiesControllerDeletePolicyVersionMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { sharedPolicyBuilderController } from '../policy-builder.controller';

export class PolicyHeaderDraftActions {
    deleteDraftMutation = new ObservedMutation(
        policiesControllerDeletePolicyVersionMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isDeletingDraft(): boolean {
        return this.deleteDraftMutation.isPending;
    }

    get hasDeleteDraftError(): boolean {
        return this.deleteDraftMutation.hasError;
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        if (this.shouldShowFinalizeDraftButton) {
            actions.push(this.finalizeDraftAction);
        }

        const dropdownActions = this.getDropdownActions();

        if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'draft-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    items: dropdownActions,
                },
            });
        }

        return actions;
    }

    private get finalizeDraftAction(): Action {
        return {
            id: 'finalize-draft-button',
            actionType: 'button',
            typeProps: {
                label: t`Finalize draft`,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handleFinalizeDraft,
            },
        };
    }

    private getDropdownActions(): SchemaDropdownItemData[] {
        const actions: SchemaDropdownItemData[] = [];

        if (this.shouldShowAuthorPolicy) {
            actions.push({
                id: 'author-policy-action',
                type: 'item',
                label: t`Author policy`,
                value: 'author-policy',
                onSelect: this.handleAuthorPolicy,
            });
        }

        if (this.shouldShowDeleteDraft) {
            actions.push({
                id: 'delete-draft-action',
                type: 'item',
                label: t`Delete draft`,
                value: 'delete-draft',
                onSelect: this.handleDeleteDraft,
                disabled: this.deleteDraftMutation.isPending,
            });
        }

        return actions;
    }

    private get shouldShowFinalizeDraftButton(): boolean {
        // TODO: Implement business logic for showing finalize draft button
        // Based on: !isPolicyExternalStatusUnacceptable
        return true;
    }

    private get shouldShowAuthorPolicy(): boolean {
        return sharedPolicyBuilderModel.isUploadedPolicy;
    }

    private get shouldShowDeleteDraft(): boolean {
        if (sharedPolicyBuilderModel.isNewDrataTemplatePolicy) {
            return false;
        }

        return (
            sharedPolicyBuilderModel.isAuthoredPolicy ||
            sharedPolicyBuilderModel.isUploadedPolicy ||
            sharedPolicyBuilderModel.hasNotionOrConfluenceConnection
        );
    }

    /**
     * Action handlers.
     */
    handleFinalizeDraft = (): void => {
        // TODO: Implement finalize draft mutation
        // This should call the API to change status from DRAFT to NEEDS_APPROVAL
        // Example: this.finalizeDraftMutation.mutate({ path: { policyId, versionId: currentVersionId } });
    };

    handleAuthorPolicy = (): void => {
        // TODO: Implement author policy logic
        // This should navigate to the policy authoring interface
        // Example: window.location.href = `/workspaces/1/governance/policies/${policyId}/author`;
    };

    handleDeleteDraft = (): void => {
        openConfirmationModal({
            title: t`Delete draft`,
            body: t`Are you sure you want to delete this draft? This action cannot be undone.`,
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'sm',
            disableClickOutsideToClose: true,
            onConfirm: this.confirmDeleteDraft,
            onCancel: closeConfirmationModal,
            isLoading: () => this.isDeletingDraft,
        });
    };

    confirmDeleteDraft = (): void => {
        const { currentVersionId } = sharedPolicyBuilderController;

        if (!currentVersionId) {
            return;
        }

        this.deleteDraftMutation.mutate({
            path: { id: currentVersionId },
        });

        when(
            () => !this.isDeletingDraft,
            () => {
                if (this.hasDeleteDraftError) {
                    snackbarController.addSnackbar({
                        id: 'delete-draft-error',
                        props: {
                            title: t`Failed to delete draft`,
                            description: t`An error occurred while deleting the draft`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'delete-draft-success',
                    hasTimeout: true,
                    props: {
                        title: t`Draft deleted successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closeConfirmationModal();

                this.handlePostDeleteNavigation();
            },
        );
    };

    handlePostDeleteNavigation = (): void => {
        const { hasPublishedVersion, hasDraftVersion } =
            sharedPolicyBuilderModel;

        if (!hasPublishedVersion && !hasDraftVersion) {
            this.navigateToPoliciesList();
        } else {
            this.loadRemainingVersion();
        }
    };

    navigateToPoliciesList = (): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;

        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${workspaceId}/governance/policies/active`,
        );
    };

    loadRemainingVersion = (): void => {
        const { publishedVersionId, policyId } = sharedPolicyBuilderModel;

        if (publishedVersionId) {
            sharedPolicyBuilderController.loadPolicyWithAllData(
                policyId,
                publishedVersionId,
            );
        }
    };
}
