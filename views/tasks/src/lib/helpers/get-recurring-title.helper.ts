import {
    type RecurringScheduleSpec,
    RecurringType,
    Weekday,
    WeekNumber,
} from '@drata/recurring-schedule';
import { t } from '@globals/i18n/macro';
import { sortDaysOfWeek } from '@models/tasks';

export const getRecurringTitle = (
    schedule: RecurringScheduleSpec | undefined,
): string => {
    let recurringTitle = '';

    if (!schedule) {
        return recurringTitle;
    }

    const interval = schedule.interval || 1;
    const intervalPrefix = interval === 1 ? t`Every ` : t`Every ${interval}`;

    if (schedule.type === RecurringType.MONTHLY) {
        const monthLabel = interval === 1 ? t`month` : t`months`;

        if (schedule.dayOfMonth) {
            if (schedule.dayOfMonth === -1) {
                recurringTitle = t`${intervalPrefix}${monthLabel} on the last day of the month`;
            } else {
                recurringTitle = t`${intervalPrefix}${monthLabel} on day {dayOfMonth}`;
            }
        }
        if (schedule.weekNumber && schedule.weekday) {
            const monthlyDayOfWeek =
                Object.keys(Weekday)[
                    Object.values(Weekday).indexOf(schedule.weekday)
                ];
            const weekNumberKey =
                Object.keys(WeekNumber)[
                    Object.values(WeekNumber).indexOf(schedule.weekNumber)
                ];

            const weekNumberFormatted = weekNumberKey
                ? weekNumberKey.split('_')[0]?.toLowerCase()
                : '';

            recurringTitle = t`${intervalPrefix}${monthLabel} on the ${weekNumberFormatted} ${monthlyDayOfWeek}`;
        }
    }

    if (schedule.type === RecurringType.WEEKLY) {
        const weekLabel = interval === 1 ? t`week` : t`weeks`;

        const weekDay = sortDaysOfWeek(schedule.daysOfWeek).map(
            (day: Weekday) =>
                Object.keys(Weekday)[Object.values(Weekday).indexOf(day)],
        );
        const formattedDays = weekDay.join(', ');

        recurringTitle = t`${intervalPrefix}${weekLabel} on ${formattedDays}`;
    }
    if (schedule.type === RecurringType.YEARLY) {
        const yearLabel = interval === 1 ? t`year` : t`years`;

        const formattedDate = new Date(
            schedule.month - 1,
            schedule.day,
        ).toLocaleString('default', {
            month: 'long',
            day: 'numeric',
        });

        recurringTitle = t`${intervalPrefix}${yearLabel} on ${formattedDate}`;
    }

    return recurringTitle;
};
