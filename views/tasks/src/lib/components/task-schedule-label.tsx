import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { RecurringScheduleSpec } from '@drata/recurring-schedule';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getRecurringTitle } from '../helpers/get-recurring-title.helper';

export const TaskScheduleLabel = observer(
    ({ schedule }: { schedule: RecurringScheduleSpec }) => {
        const recurringTitle = getRecurringTitle(schedule);

        return (
            <Stack data-id="_50-Ab8_" direction="row" gap="sm" align="center">
                <Metadata
                    colorScheme="primary"
                    iconName="CalendarSchedule"
                    label={t`Recurring`}
                    type="status"
                />
                <Text
                    shouldWrap
                    align="left"
                    as="span"
                    colorScheme="neutral"
                    size="100"
                    type="subheadline"
                >
                    {recurringTitle}
                </Text>
            </Stack>
        );
    },
);
