import { isEmpty } from 'lodash-es';
import { useState } from 'react';
import { sharedAccessReviewApplicationEvidenceController } from '@controllers/access-reviews';
import type {
    CosmosFileObject,
    FileUploadProps,
} from '@cosmos/components/file-upload';
import { FileUploadField } from '@cosmos/components/file-upload-field';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

const FORM_ID = 'access-review-upload-evidence-modal-form';

const ACCEPTED_FILE_FORMATS = [
    'pdf',
    'docx',
    'odt',
    'xlsx',
    'ods',
    'pptx',
    'odp',
    'gif',
    'jpeg',
    'png',
] as const satisfies FileUploadProps['acceptedFormats'];

interface AccessReviewUploadEvidenceModalProps {
    onClose: () => void;
}

export const AccessReviewUploadEvidenceModal = observer(
    ({ onClose }: AccessReviewUploadEvidenceModalProps): React.JSX.Element => {
        const [files, setFiles] = useState<File[]>([]);
        const [feedback, setFeedback] = useState<
            { type: 'error'; message: string } | undefined
        >(undefined);

        const { uploadEvidence, isUploading } =
            sharedAccessReviewApplicationEvidenceController;

        const handleFileUpdate = (data: CosmosFileObject[]): void => {
            setFeedback(undefined);
            const updatedFiles = data.map((fileObj) => fileObj.file);

            setFiles(updatedFiles);
        };

        const handleUploadFilesSave = () => {
            if (isEmpty(files)) {
                setFeedback({
                    type: 'error',
                    message: t`You must upload at least 1 file`,
                });

                return;
            }

            const formFiles = files.map((file) => {
                const formData = new FormData();

                formData.append('file', file);

                return formData;
            });

            uploadEvidence(formFiles, onClose);
        };

        return (
            <>
                <Modal.Header
                    size="md"
                    title={t`Add evidence`}
                    closeButtonAriaLabel={t`Close add evidence modal`}
                    onClose={onClose}
                />
                <Modal.Body size="lg">
                    <FileUploadField
                        isMulti
                        feedback={feedback}
                        required={false}
                        innerLabel={t`Or drop files here`}
                        label={t`Upload file`}
                        labelStyleOverrides={{ size: 'md' }}
                        name="uploadEvidence"
                        removeButtonText={t`Remove file`}
                        selectButtonText={t`Upload files`}
                        formId={FORM_ID}
                        acceptedFormats={ACCEPTED_FILE_FORMATS}
                        errorCodeMessages={{
                            'file-invalid-type': t`Not a valid file type.`,
                            'file-too-large': t`File size is too large.`,
                            'file-too-small': t`File size is too small.`,
                            'too-many-files': t`Contains too many files.`,
                        }}
                        onUpdate={handleFileUpdate}
                    />
                </Modal.Body>
                <Modal.Footer
                    size="md"
                    rightActionStack={[
                        {
                            label: t`Complete`,
                            onClick: handleUploadFilesSave,
                            isLoading: isUploading,
                        },
                    ]}
                />
            </>
        );
    },
);
