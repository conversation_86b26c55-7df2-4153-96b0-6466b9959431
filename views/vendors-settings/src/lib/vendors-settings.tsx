import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import type {
    VendorEmailContentRequestDto,
    VendorSettingsRequestDto,
} from '@globals/api-sdk/types';
import {
    sharedFeatureAccessModel,
    sharedFeatureFlagsController,
} from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { VendorsSettingsEmailAppearanceReadOnlyComponent } from './components/vendors-settings-email-appearance-read-only.component';
import { VendorsSettingsEmailContentEditComponent } from './components/vendors-settings-email-content-edit.component';
import { VendorsSettingsQuestionnaireRemindersEditComponent } from './components/vendors-settings-questionnaire-reminders-edit.component';
import { VendorsSettingsQuestionnaireRemindersReadOnlyComponent } from './components/vendors-settings-questionnaire-reminders-read-only.component';
import { VendorsSettingsRecurringReviewsEditComponent } from './components/vendors-settings-recurring-reviews-edit.component';
import { VendorsSettingsRecurringReviewsReadOnlyComponent } from './components/vendors-settings-recurring-reviews-read-only.component';
import { VendorsSettingsSsoEditComponent } from './components/vendors-settings-sso-edit.component';
import { VendorsSettingsSsoReadOnlyComponent } from './components/vendors-settings-sso-read-only.component';

export const VendorsSettingsView = observer((): React.JSX.Element => {
    const {
        vendorSettings,
        isLoading,
        isUpdatingSsoSettings,
        isUpdatingReviewPeriod,
        isUpdatingEmailContent,
        hasErrorSsoSettingsUpdate,
        hasErrorReviewPeriodUpdate,
        hasErrorEmailContentUpdate,
        updateSsoSettings,
        updateReviewPeriod,
        updateEmailContent,
        isUpdatingQuestionnaireReminders,
        hasErrorQuestionnaireRemindersUpdate,
        updateQuestionnaireReminders,
    } = sharedVendorsSettingsController;

    const { isVendorRiskManagementProEnabled } = sharedFeatureAccessModel;

    // Feature flag for SSO suggestions - TODO: Review what the behavior should be in multiverse with this feature flag, since messages change in web.
    const enableSuggestionsViaConnections =
        sharedFeatureFlagsController.isReleaseEnablingSuggestionsViaConnectionsForEssential;

    // Form submission hooks
    const { formRef: ssoFormRef, triggerSubmit: triggerSsoSubmit } =
        useFormSubmit();
    const {
        formRef: recurringReviewsFormRef,
        triggerSubmit: triggerRecurringReviewsSubmit,
    } = useFormSubmit();
    const {
        formRef: questionnaireRemindersFormRef,
        triggerSubmit: triggerQuestionnaireRemindersSubmit,
    } = useFormSubmit();
    const {
        formRef: emailContentFormRef,
        triggerSubmit: triggerEmailContentSubmit,
    } = useFormSubmit();

    const handleSsoSubmit = (values: VendorSettingsRequestDto) => {
        updateSsoSettings(values);
    };

    const handleRecurringReviewsSubmit = (values: {
        defaultReviewPeriod: number;
    }): void => {
        updateReviewPeriod(values);
    };

    const handleQuestionnaireRemindersSubmit = (values: {
        enableFollowUpReminders: boolean;
        daysToSendReminder?: number;
    }): void => {
        updateQuestionnaireReminders(values);
    };

    const handleEmailContentSubmit = (
        values: VendorEmailContentRequestDto,
    ): void => {
        updateEmailContent(values);
    };

    const shouldShowSsoSuggestions =
        isVendorRiskManagementProEnabled || enableSuggestionsViaConnections;

    if (isLoading) {
        return <Loader label="Loading..." size="lg" colorScheme="primary" />;
    }

    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="VendorsSettingsView"
            data-id="7CmDs0Hs"
        >
            {shouldShowSsoSuggestions && (
                <ViewEditCardComponent
                    isMutationPending={isUpdatingSsoSettings}
                    hasMutationError={hasErrorSsoSettingsUpdate}
                    readOnlyComponent={<VendorsSettingsSsoReadOnlyComponent />}
                    title={t`Single sign-on suggestions`}
                    editComponent={
                        <VendorsSettingsSsoEditComponent
                            isUpdating={isUpdatingSsoSettings}
                            formRef={ssoFormRef}
                            onSubmit={handleSsoSubmit}
                        />
                    }
                    onSave={triggerSsoSubmit}
                />
            )}

            <ViewEditCardComponent
                title={t`Recurring reviews`}
                isMutationPending={isUpdatingReviewPeriod}
                hasMutationError={hasErrorReviewPeriodUpdate}
                readOnlyComponent={
                    <VendorsSettingsRecurringReviewsReadOnlyComponent
                        vendorSettings={vendorSettings}
                    />
                }
                editComponent={
                    <VendorsSettingsRecurringReviewsEditComponent
                        isUpdating={isUpdatingReviewPeriod}
                        formRef={recurringReviewsFormRef}
                        onSubmit={handleRecurringReviewsSubmit}
                    />
                }
                onSave={triggerRecurringReviewsSubmit}
            />

            <ViewEditCardComponent
                isMutationPending={isUpdatingQuestionnaireReminders}
                hasMutationError={hasErrorQuestionnaireRemindersUpdate}
                title={t`Questionnaire reminders`}
                readOnlyComponent={
                    <VendorsSettingsQuestionnaireRemindersReadOnlyComponent />
                }
                editComponent={
                    <VendorsSettingsQuestionnaireRemindersEditComponent
                        isUpdating={isUpdatingQuestionnaireReminders}
                        formRef={questionnaireRemindersFormRef}
                        onSubmit={handleQuestionnaireRemindersSubmit}
                    />
                }
                onSave={triggerQuestionnaireRemindersSubmit}
            />

            <ViewEditCardComponent
                title={t`Email and questionnaire appearance`}
                isMutationPending={isUpdatingEmailContent}
                hasMutationError={hasErrorEmailContentUpdate}
                readOnlyComponent={
                    <VendorsSettingsEmailAppearanceReadOnlyComponent
                        vendorSettings={vendorSettings}
                    />
                }
                editComponent={
                    <VendorsSettingsEmailContentEditComponent
                        formRef={emailContentFormRef}
                        onSubmit={handleEmailContentSubmit}
                    />
                }
                onSave={triggerEmailContentSubmit}
            />
        </Stack>
    );
});
