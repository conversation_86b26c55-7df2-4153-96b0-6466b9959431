import type { ButtonColorScheme } from '@cosmos/components/button';
import type { CustomerRequestDetailsWithFrameworkResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export function getStatusLabel(
    status: CustomerRequestDetailsWithFrameworkResponseDto['status'],
): string {
    switch (status) {
        case 'OUTSTANDING': {
            return t`Outstanding`;
        }
        case 'ACCEPTED': {
            return t`Accepted`;
        }
        case 'IN_REVIEW':
        default: {
            return t`In review`;
        }
    }
}

export function getStatusColor(
    status: CustomerRequestDetailsWithFrameworkResponseDto['status'],
): ButtonColorScheme {
    switch (status) {
        case 'OUTSTANDING': {
            return 'neutral';
        }
        case 'ACCEPTED': {
            return 'primary';
        }
        case 'IN_REVIEW':
        default: {
            return 'neutral';
        }
    }
}
