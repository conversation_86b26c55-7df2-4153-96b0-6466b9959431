import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';

export const getDayOptions = (): ListBoxItemData[] => {
    const dayOptions: ListBoxItemData[] = Array.from({ length: 31 }, (_, i) => {
        const day = String(i + 1);

        return {
            id: day,
            label: t`Day ${day}`,
            value: day,
        };
    });

    dayOptions.push({
        id: 'LAST',
        label: t`Last day`,
        value: 'LAST',
    });

    return dayOptions;
};
