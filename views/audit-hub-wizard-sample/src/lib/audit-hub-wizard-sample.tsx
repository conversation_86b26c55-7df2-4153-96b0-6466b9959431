import { useCallback, useMemo } from 'react';
import { sharedAuditHubAuditController } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import { Wizard, type WizardProps } from '@cosmos-lab/components/wizard';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate, useParams } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { CustomizeEvidence } from './steps/customize-evidence';
import { Review } from './steps/review';
import { SetAuditSamples } from './steps/set-audit-samples';

export const AuditHubWizardSample = observer((): JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { startAudit } = sharedAuditHubAuditController;
    const { clientId } = sharedCustomerRequestDetailsController;
    const navigate = useNavigate();
    const { auditId } = useParams();

    const handleComplete = useCallback(async () => {
        try {
            await startAudit();
            navigate(
                `/audit-hub/clients/${clientId}/audits/${auditId}/details`,
            );
        } catch {
            snackbarController.addSnackbar({
                id: 'audit-start-error',
                props: {
                    title: t`Failed to start audit`,
                    description: t`An error occurred while starting the audit. Try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    }, [startAudit, navigate, clientId, auditId]);

    const wizardProps: WizardProps = useMemo(
        () => ({
            steps: [
                {
                    component: () => (
                        <SetAuditSamples formRef={formRef} data-id="DLmrUeWs" />
                    ),
                    stepTitle: t`Set samples`,
                    isStepSkippable: false,
                    onStepChange: triggerSubmit,
                },
                {
                    component: () => (
                        <CustomizeEvidence
                            formRef={formRef}
                            data-id="Ichw_dP3"
                        />
                    ),
                    stepTitle: t`Customize evidence`,
                    isStepSkippable: false,
                    onStepChange: triggerSubmit,
                },
                {
                    component: () => <Review data-id="QfVBhD7Z" />,
                    stepTitle: t`Review`,
                    isStepSkippable: false,
                    onStepChange: triggerSubmit,
                },
            ],
            onCancel: () => {
                navigate(`/audit-hub/clients/${clientId}/audits`);
            },
            onComplete: handleComplete,
        }),
        [clientId, formRef, navigate, triggerSubmit, handleComplete],
    );

    return (
        <Stack
            data-id="EUSbJW_8"
            justify="center"
            data-testid="AuditHubWizardSample"
            height="100%"
        >
            <Box width={breakpointMd}>
                <Wizard
                    steps={wizardProps.steps}
                    completeButtonLabel={t`Finish`}
                    nextButtonLabel={t`Continue`}
                    onCancel={wizardProps.onCancel}
                    onComplete={wizardProps.onComplete}
                />
            </Box>
        </Stack>
    );
});
