export const ShowSidebarMenu = (): React.JSX.Element => (
    <svg
        data-id="cosmos-svg-Help"
        viewBox="0 0 16 16"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        data-testid="ShowSidebarMenu"
    >
        <path
            fill="currentColor"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.281 4.222a.5.5 0 0 1 .5-.5h6.25a.5.5 0 0 1 0 1h-6.25a.5.5 0 0 1-.5-.5M1.486 5.419a.5.5 0 0 1 .692-.148L5.37 7.337c.276.184.535.508.535.916 0 .194-.06.389-.154.549a.96.96 0 0 1-.431.399l-3.15 1.976a.5.5 0 1 1-.53-.847l3.187-2a1 1 0 0 1 .061-.033.2.2 0 0 0 .015-.038l-.006-.011a.3.3 0 0 0-.08-.077L1.636 6.11a.5.5 0 0 1-.149-.692m6.795 2.803a.5.5 0 0 1 .5-.5h6.25a.5.5 0 0 1 0 1h-6.25a.5.5 0 0 1-.5-.5m0 4a.5.5 0 0 1 .5-.5h6.25a.5.5 0 0 1 0 1h-6.25a.5.5 0 0 1-.5-.5"
        />
    </svg>
);
