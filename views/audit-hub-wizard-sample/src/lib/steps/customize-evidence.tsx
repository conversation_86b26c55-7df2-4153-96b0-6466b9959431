import { sharedAuditHubAuditController } from '@controllers/audit-hub';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AuditHubWizardEvidenceModel } from '@models/audit-hub-wizard-sample';
import { Form } from '@ui/forms';

const FORM_ID = 'customize-evidence-form';

export const CustomizeEvidence = observer(
    ({
        formRef,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
    }): React.JSX.Element => {
        const { addSampleWizardData } = sharedAuditHubAuditController;
        const { evidenceFormSchema } = new AuditHubWizardEvidenceModel();

        return (
            <Stack
                direction="column"
                gap="2xl"
                data-testid="CustomizeEvidence"
                data-id="gvqSzqOA"
            >
                <Stack direction="column" gap="md">
                    <Text type="subheadline" size="400" as="p">
                        {t`Customize evidence`}
                    </Text>
                    <Text>
                        <Trans>
                            Choose what evidence you want to see and how its
                            organized. Either way you&apos;ll be able to
                            download the complete audit package and request
                            additional custom evidence later.
                        </Trans>
                    </Text>
                </Stack>

                <Form
                    hasExternalSubmitButton
                    formId={FORM_ID}
                    schema={evidenceFormSchema}
                    ref={formRef}
                    data-id="customize-evidence-form"
                    onSubmit={addSampleWizardData}
                />
            </Stack>
        );
    },
);
