import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { VendorsSettingsResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getDefaultEmailContent } from '../helpers/default-email-content.constant';
import { CompanyPreviewComponent } from './company-preview.component';

interface VendorsSettingsEmailAppearanceReadOnlyComponentProps {
    vendorSettings: VendorsSettingsResponseDto | null;
}

export const VendorsSettingsEmailAppearanceReadOnlyComponent = observer(
    ({
        vendorSettings,
    }: VendorsSettingsEmailAppearanceReadOnlyComponentProps): React.JSX.Element => {
        return (
            <Stack direction="column" gap="lg" data-id="6KLiTkjk">
                <Stack display="flex" direction="column" gap="lg">
                    <Text type="body" colorScheme="faded" size="100">
                        {t`Header preview`}
                    </Text>
                    <Text type="body" size="100" colorScheme="faded">
                        <Trans>
                            Drata uses your primary workspace&apos;s logo and
                            company name from settings.
                        </Trans>
                    </Text>
                    <CompanyPreviewComponent data-id="company-preview" />
                </Stack>
                <Stack display="flex" direction="column" gap="lg">
                    <Text type="body" colorScheme="faded" size="100">
                        {t`Email content`}
                    </Text>
                    <Text type="body" size="100" colorScheme="faded">
                        {t`Customize the email vendors receive when you send a questionnaire.`}
                    </Text>
                    <Box
                        p="3x"
                        borderWidth="borderWidth1"
                        borderColor="neutralBorderFaded"
                        data-id="email-content-preview"
                        borderRadius="borderRadiusMd"
                        overflowY="auto"
                        style={{
                            whiteSpace: 'pre-wrap',
                        }}
                    >
                        <Text type="body">
                            {vendorSettings?.emailContent ||
                                getDefaultEmailContent()}
                        </Text>
                    </Box>
                </Stack>
            </Stack>
        );
    },
);
