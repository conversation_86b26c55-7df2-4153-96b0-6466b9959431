import { isNil, noop } from 'lodash-es';
import { activeLibraryTestController } from '@controllers/library-test';
import { ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import { Button } from '@cosmos/components/button';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t, Trans } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';
import { getMonitorCheckTypeLabel } from './helpers/get-monitor-check-type-label-helper';
import { getLibraryTestRatingLabel } from './helpers/get-monitor-rating-label.helper';

export class LibraryTestPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'library-test-page';

    get backLink(): React.JSX.Element {
        const { currentWorkspace } = sharedWorkspacesController;

        return (
            <Stack gap="md" align="center">
                <AppLink
                    href={`/workspaces/${currentWorkspace?.id}/library/tests`}
                    label={t`Back to Test Library`}
                    size="sm"
                />
            </Stack>
        );
    }

    get actionStack(): React.JSX.Element {
        const instance = activeLibraryTestController.monitoringControlInstance;

        if (isNil(instance)) {
            return (
                <ActionStack
                    isFullWidth
                    data-id="library-test-action-stack-empty"
                    data-testid="LibraryTestAddToProgram"
                    actions={[]}
                />
            );
        }

        const importConditions = instance.importConditions as unknown as {
            isAP2orAI: boolean;
            hasConnections: boolean;
            missingConnections: string[];
        };

        if (!importConditions.isAP2orAI) {
            return (
                <ActionStack
                    isFullWidth
                    data-id="library-test-action-stack-disabled"
                    data-testid="LibraryTestAddToProgram"
                    actions={[
                        {
                            actionType: 'tooltipButton',
                            id: 'add-to-program',
                            typeProps: {
                                tooltip: {
                                    text: t`Test cannot be managed in Drata Library`,
                                    isInteractive: true,
                                },
                                button: {
                                    label: t`Add to program`,
                                    level: 'primary',
                                    colorScheme: 'primary',
                                    onClick: noop,
                                },
                            },
                        },
                    ]}
                />
            );
        }

        if (!importConditions.hasConnections) {
            return (
                <ActionStack
                    isFullWidth
                    data-id="library-test-action-stack-no-connection"
                    data-testid="LibraryTestAddToProgram"
                    actions={[
                        {
                            actionType: 'tooltipButton',
                            id: 'add-to-program',
                            typeProps: {
                                tooltip: {
                                    text: t`New connection required to add to program`,
                                    isInteractive: true,
                                },
                                button: {
                                    label: t`Add to program`,
                                    level: 'primary',
                                    colorScheme: 'primary',
                                    onClick: noop,
                                },
                            },
                        },
                    ]}
                />
            );
        }

        const { currentWorkspace } = sharedWorkspacesController;
        const { testId } = instance;

        return (
            <ActionStack
                isFullWidth
                data-id="library-test-action-stack-enabled"
                data-testid="LibraryTestAddToProgram"
                actions={[
                    {
                        actionType: 'button',
                        id: 'add-to-program',
                        typeProps: {
                            label: t`Add to program`,
                            colorScheme: 'primary',
                            href: `/workspaces/${currentWorkspace?.id}/library/tests/${testId}/add-to-program`,
                        },
                    },
                ]}
            />
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const {
            categories = [],
            rating = '',
            updatedAt = '',
        } = activeLibraryTestController.monitoringControlInstance ?? {};
        const testCategoryLabels = categories.map((category) => {
            return getMonitorCheckTypeLabel(category);
        });

        return [
            {
                id: 'library-test-categories-kv',
                label: t`Categories`,
                type: 'TEXT',
                value: testCategoryLabels.join(', '),
            },
            {
                id: 'library-test-rating-kv',
                label: t`Rating`,
                type: 'TEXT',
                value: getLibraryTestRatingLabel(rating),
            },
            {
                id: 'library-test-updated-at-kv',
                label: t`Last updated`,
                type: 'REACT_NODE',
                value: (
                    <DateTime
                        date={updatedAt}
                        format="field"
                        data-id="library-test-updated-at-datetime"
                    />
                ),
            },
        ];
    }

    get title(): string {
        return (
            activeLibraryTestController.monitoringControlInstance?.name ?? ''
        );
    }

    get banner(): React.JSX.Element | undefined {
        const instance = activeLibraryTestController.monitoringControlInstance;

        if (isNil(instance)) {
            return undefined;
        }

        const importConditions = instance.importConditions as unknown as {
            isAP2orAI: boolean;
            hasConnections: boolean;
            missingConnections: string[];
        };

        if (!importConditions.isAP2orAI) {
            const { currentWorkspace } = sharedWorkspacesController;

            return (
                <Banner
                    severity="primary"
                    title={t`This test cannot be managed in Drata Library`}
                    body={
                        <Stack direction="column" gap="2x">
                            <Text colorScheme="primary">
                                <Trans>
                                    Browse the library to find the right test
                                    for your compliance
                                </Trans>
                            </Text>
                            <Stack gap="3x">
                                <Button
                                    level="secondary"
                                    label={t`Browse`}
                                    href={`/workspaces/${currentWorkspace?.id}/library/tests`}
                                />
                            </Stack>
                        </Stack>
                    }
                />
            );
        }

        if (
            !importConditions.hasConnections &&
            importConditions.missingConnections[0]
        ) {
            const categoryName = importConditions.missingConnections[0];
            const { currentWorkspace } = sharedWorkspacesController;

            if (sharedEntitlementFlagController.isMultipleWorkspacesEnabled) {
                return (
                    <Banner
                        severity="primary"
                        title={t`This test does not have a required connection in one or more of your workspaces`}
                        body={
                            <Stack direction="column" gap="2x">
                                <Text colorScheme="primary">
                                    <Trans>
                                        Setup a connection in {categoryName} to
                                        automate your compliance
                                    </Trans>
                                </Text>
                                <Stack gap="3x">
                                    <Button
                                        level="secondary"
                                        label={t`View connections`}
                                        href={`/workspaces/${currentWorkspace?.id}/connections/all/active`}
                                    />
                                </Stack>
                            </Stack>
                        }
                    />
                );
            }

            return (
                <Banner
                    severity="primary"
                    title={t`This test requires a connection to run`}
                    body={
                        <Stack direction="column" gap="2x">
                            <Text colorScheme="primary">
                                <Trans>
                                    Setup a connection in {categoryName} to
                                    automate your compliance
                                </Trans>
                            </Text>
                            <Stack gap="3x">
                                <Button
                                    level="secondary"
                                    label={t`View connections`}
                                    href={`/workspaces/${currentWorkspace?.id}/connections/all/active`}
                                />
                            </Stack>
                        </Stack>
                    }
                />
            );
        }

        return undefined;
    }
}
