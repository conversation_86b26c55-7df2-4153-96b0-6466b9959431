import { z } from 'zod';
import { DEFAULT_REVIEW_PERIOD_DAYS } from '@components/vendors-settings';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { DefaultReviewPeriodField } from '../components/default-review-period-field.component';

class VendorsSettingsRecurringReviewsFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    get vendorSettings() {
        return sharedVendorsSettingsController.vendorSettings;
    }

    getSchema(): FormSchema {
        return {
            defaultReviewPeriod: {
                type: 'custom',
                label: t`Set review start date for all vendors with recurring reviews`,
                render: DefaultReviewPeriodField,
                initialValue: String(
                    this.vendorSettings?.defaultReviewPeriod ??
                        DEFAULT_REVIEW_PERIOD_DAYS,
                ),
                validator: z
                    .string()
                    .min(1, t`Enter number of days`)
                    .regex(/^\d+$/, t`Invalid number of days`)
                    .refine((value) => {
                        const num = parseInt(value, 10);

                        return num >= 1 && num <= 120;
                    }, t`Number of days should be less than or equal to 120 days`),
            },
        };
    }
}

export const sharedVendorsSettingsRecurringReviewsFormModel =
    new VendorsSettingsRecurringReviewsFormModel();
