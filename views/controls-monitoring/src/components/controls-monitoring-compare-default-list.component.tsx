import { sharedMonitoringControlTestComparisonController } from '@controllers/monitoring-details';
import { Banner } from '@cosmos/components/banner';
import { Tabs } from '@cosmos/components/tabs';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ControlsMonitoringCompareDefaultTestsComponent } from './controls-monitoring-compare-default-tests.component';

export const ControlsMonitoringCompareDefaultListComponent = observer(
    (): React.JSX.Element => {
        const { userMappedTests, templateMappedTests } =
            sharedMonitoringControlTestComparisonController;
        const userMappedTestsCount = userMappedTests.length;
        const templateMappedTestsCount = templateMappedTests.length;

        const defaultTabId =
            templateMappedTestsCount > 0
                ? 'controls-monitoring-compare-default-modal-template-tab'
                : 'controls-monitoring-compare-default-modal-users-tab';

        return (
            <>
                <Text type="body" size="200" colorScheme="neutral" as="p">
                    {t`Here are the tests that would be added and removed if you choose to apply <PERSON><PERSON>'s default.`}
                </Text>
                <Banner
                    displayMode="section"
                    severity="primary"
                    body={''}
                    title={t`Any custom frameworks you have won't be affected.`}
                />
                <Tabs
                    overflowLeftLabel={t`Scroll tabs to the left`}
                    overflowRightLabel={t`Scroll tabs to the right`}
                    defaultTabId={defaultTabId}
                    tabs={[
                        {
                            tabId: 'controls-monitoring-compare-default-modal-template-tab',
                            label: t`Default would add (${templateMappedTestsCount})`,
                            content: (
                                <ControlsMonitoringCompareDefaultTestsComponent
                                    tests={templateMappedTests}
                                />
                            ),
                        },
                        {
                            tabId: 'controls-monitoring-compare-default-modal-users-tab',
                            label: t`Default would remove (${userMappedTestsCount})`,
                            content: (
                                <ControlsMonitoringCompareDefaultTestsComponent
                                    tests={userMappedTests}
                                />
                            ),
                        },
                    ]}
                />
            </>
        );
    },
);
