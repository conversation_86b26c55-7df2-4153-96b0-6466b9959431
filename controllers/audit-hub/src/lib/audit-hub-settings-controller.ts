import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { TDateISOTime } from '@cosmos/components/date-picker-field';
import {
    auditorApiKeyMgmtControllerCreateAuditorApiKeyMutation,
    auditorApiKeyMgmtControllerDeleteAuditorApiKeyMutation,
    auditorApiKeyMgmtControllerGetAuditorApiKeysOptions,
    auditorApiKeyMgmtControllerUpdateAuditorApiKeyMutation,
    auditorsControllerGetMyUserOptions,
    auditorsControllerUpdateAuditorMutation,
} from '@globals/api-sdk/queries';
import type {
    AuditorApiKeyResponseDto,
    AuditorMeResponseDto,
    OldAuditorRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { openApiKeySuccessModal } from './helpers/open-api-key-success-modal.helper';

class AuditHubSettingsController {
    auditorGetMyUserQuery = new ObservedQuery(
        auditorsControllerGetMyUserOptions,
    );

    auditorApiKeysQuery = new ObservedQuery(
        auditorApiKeyMgmtControllerGetAuditorApiKeysOptions,
    );

    updateProfileMutation = new ObservedMutation(
        auditorsControllerUpdateAuditorMutation,
    );

    deleteApiKeyMutation = new ObservedMutation(
        auditorApiKeyMgmtControllerDeleteAuditorApiKeyMutation,
    );

    updateApiKeyMutation = new ObservedMutation(
        auditorApiKeyMgmtControllerUpdateAuditorApiKeyMutation,
    );

    createApiKeyMutation = new ObservedMutation(
        auditorApiKeyMgmtControllerCreateAuditorApiKeyMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get auditorProfile(): AuditorMeResponseDto | null {
        return this.auditorGetMyUserQuery.data;
    }

    get auditorApiKeysIsLoading(): boolean {
        return this.auditorApiKeysQuery.isLoading;
    }

    get auditorApiKeysTotal(): number {
        return this.auditorApiKeysQuery.data?.total ?? 0;
    }

    get auditorApiKeys(): AuditorApiKeyResponseDto[] {
        return (this.auditorApiKeysQuery.data?.data ??
            []) as AuditorApiKeyResponseDto[];
    }

    get isLoading(): boolean {
        return this.auditorGetMyUserQuery.isLoading;
    }

    loadAuditorProfile = (): void => {
        this.auditorGetMyUserQuery.load();
    };

    updateAuditorSettings = (body: OldAuditorRequestDto) => {
        this.updateProfileMutation
            .mutateAsync({
                body,
            })
            .then(() => {
                this.auditorGetMyUserQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'auditor-settings-updated',
                    props: {
                        title: t`Auditor settings updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'auditor-settings-update-error',
                    props: {
                        title: t`Update Failed`,
                        description: t`An error occurred while updating the auditor settings. Try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    loadApiKeys = (params: FetchDataResponseParams): void => {
        const { pagination, globalFilter, sorting } = params;
        const { pageSize, page = 1 } = pagination;
        const { search } = globalFilter;

        type Query = Required<
            Parameters<
                typeof auditorApiKeyMgmtControllerGetAuditorApiKeysOptions
            >
        >[0]['query'];

        const query: Query = {
            page,
            limit: pageSize,
            q: search ?? undefined,
        };

        if (!isEmpty(sorting)) {
            query.sort = sorting[0].id as 'NAME';
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.auditorApiKeysQuery.load({ query });
    };

    revokeApiKey = (apiKeyId: string) => {
        this.deleteApiKeyMutation
            .mutateAsync({
                path: { id: apiKeyId },
            })
            .then(() => {
                this.auditorApiKeysQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'api-key-revoked',
                    hasTimeout: true,
                    props: {
                        title: t`API key revoked successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'api-key-revoke-error',
                    props: {
                        title: t`Failed to revoke API key`,
                        description: t`An error occurred while revoking the API key.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    renewApiKey = (
        apiKey: AuditorApiKeyResponseDto,
        newExpirationDate: TDateISOTime,
    ) => {
        this.updateApiKeyMutation
            .mutateAsync({
                path: { id: apiKey.id },
                body: {
                    name: apiKey.name,
                    expiresAt: newExpirationDate,
                },
            })
            .then(() => {
                this.auditorApiKeysQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'api-key-renewed',
                    hasTimeout: true,
                    props: {
                        title: t`API key updated.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'api-key-renew-error',
                    props: {
                        title: t`Failed to renew API key`,
                        description: t`An error occurred while renewing the API key.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    createApiKey = (name: string, expiresAt: TDateISOTime) => {
        this.createApiKeyMutation
            .mutateAsync({
                body: {
                    name,
                    expiresAt,
                },
            })
            .then(() => {
                this.auditorApiKeysQuery.invalidate();

                // Wait for the response to be available using MobX when
                when(
                    () => Boolean(this.createApiKeyMutation.response),
                    () => {
                        const response = this.createApiKeyMutation
                            .response as AuditorApiKeyResponseDto | null;

                        if (!response) {
                            return;
                        }

                        this.openApiKeyModal(response.auditKey);
                        snackbarController.addSnackbar({
                            id: 'api-key-create-success',
                            props: {
                                title: t`API key created successfully`,
                                description: t`The new API key has been created and is ready to use.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'api-key-create-error',
                    props: {
                        title: t`Failed to create API key`,
                        description: t`An error occurred while creating the API key.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    openApiKeyModal = (apiKey: string): void => {
        openApiKeySuccessModal(apiKey);
    };
}

export const sharedAuditHubSettingsController =
    new AuditHubSettingsController();
