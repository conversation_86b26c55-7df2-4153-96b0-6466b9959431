import { isNil } from 'lodash-es';
import { useEffect } from 'react';
import { z } from 'zod';
import { sharedAuditHubSettingsController } from '@controllers/audit-hub';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import {
    type FormSchema,
    type FormValues,
    FormWrapper,
    UniversalRenderFields,
    useFormSubmit,
    useUniversalFieldController,
} from '@ui/forms';
import {
    DATES_UNAVAILABLE,
    DEFAULT_EXPIRATION_DATE,
    getDateUnavailableText,
    getExpirationOptions,
    getOption12Months,
    getOptionCustom,
} from '../constants/setting-api-keys.constants';
import { convertToEndOfDay } from '../helpers';
import type { ApiKeyFormValues, CreateApiKeyModalProps } from '../types';

const CREATE_API_KEY_MODAL_ID = 'create-api-key-modal';
const FORM_ID = 'create-api-key-form';
const MAX_NAME_LENGTH = 191;

const handleClose = () => {
    modalController.closeModal(CREATE_API_KEY_MODAL_ID);
};

const ExpirationFieldHandler = (): null => {
    // Get field controllers for nested expiration fields
    const [expirationTypeField] = useUniversalFieldController('expirationType');
    const [expirationDateField] = useUniversalFieldController('expirationDate');

    useEffect(() => {
        const typeValue = expirationTypeField.value as
            | ApiKeyFormValues['expirationType']
            | undefined;
        const is12MonthsType =
            !isNil(typeValue) && typeValue.value === getOption12Months().value;
        const needsDefaultDate =
            is12MonthsType &&
            expirationDateField.value !== DEFAULT_EXPIRATION_DATE;

        if (needsDefaultDate) {
            expirationDateField.onChange(DEFAULT_EXPIRATION_DATE);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps -- Only run when expiration type changes
    }, [expirationTypeField.value]);

    useEffect(() => {
        const typeValue = expirationTypeField.value as
            | ApiKeyFormValues['expirationType']
            | undefined;
        const dateValue = expirationDateField.value as TDateISODate | undefined;
        const isDefaultDate =
            !isNil(dateValue) && dateValue === DEFAULT_EXPIRATION_DATE;

        if (isDefaultDate) {
            if (typeValue?.value !== getOption12Months().value) {
                expirationTypeField.onChange(getOption12Months());
            }
        } else {
            if (typeValue?.value !== getOptionCustom().value) {
                expirationTypeField.onChange(getOptionCustom());
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps -- Only run when date changes
    }, [expirationDateField.value]);

    return null;
};

export const CreateApiKeyModal = observer(
    ({ onClose }: CreateApiKeyModalProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const formSchema = {
            name: {
                type: 'text',
                label: t`Name`,
                validator: z
                    .string()
                    .min(1, t`Name is required`)
                    .max(
                        MAX_NAME_LENGTH,
                        t`Name must be ${MAX_NAME_LENGTH} characters or less`,
                    ),
                initialValue: '',
            },
            expirationType: {
                type: 'select',
                initialValue: getOption12Months(),
                label: t`Expiration`,
                options: getExpirationOptions(),
            },
            expirationDate: {
                type: 'date',
                initialValue: DEFAULT_EXPIRATION_DATE as TDateISODate,
                getIsDateUnavailable: DATES_UNAVAILABLE,
                dateUnavailableText: getDateUnavailableText(),
                label: t`Date`,
            },
        } as const satisfies FormSchema;

        const handleSubmit = (values: FormValues) => {
            try {
                const name = values.name as string;
                const expirationDate = values.expirationDate as TDateISODate;

                // Create a timestamp at the end of the selected day (23:59:59.999Z)
                const fullISODate = convertToEndOfDay(expirationDate);

                sharedAuditHubSettingsController.createApiKey(
                    name,
                    fullISODate,
                );

                onClose();
            } catch (error) {
                logger.error({
                    message: t`Failed to create API key`,
                    additionalInfo: { error },
                });

                snackbarController.addSnackbar({
                    id: 'api-key-create-error',
                    props: {
                        title: t`Failed to create API key`,
                        description: t`An error occurred while creating the API key.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            }
        };

        return (
            <>
                <Modal.Header
                    title={t`Create API key`}
                    closeButtonAriaLabel={t`Close`}
                    onClose={handleClose}
                />
                <Modal.Body>
                    <FormWrapper
                        ref={formRef}
                        data-id="create-api-key-form"
                        formId={FORM_ID}
                        schema={formSchema}
                        onSubmit={handleSubmit}
                    >
                        <UniversalRenderFields
                            fields={formSchema}
                            formId={FORM_ID}
                            data-id="create-api-key-form-fields"
                        />
                        <ExpirationFieldHandler />
                        <button
                            hidden
                            type="submit"
                            aria-hidden="true"
                            tabIndex={-1}
                        />
                    </FormWrapper>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onClose,
                        },
                        {
                            label: t`Create`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit().catch(() => {
                                    snackbarController.addSnackbar({
                                        id: 'api-key-create-error',
                                        props: {
                                            title: t`Failed to create API key`,
                                            description: t`An error occurred while creating the API key.`,
                                            severity: 'critical',
                                            closeButtonAriaLabel: t`Close`,
                                        },
                                    });
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
