import { sharedControlsDetailsStatsController } from '@controllers/controls';
import { sharedMonitorsController } from '@controllers/monitors';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerResetControlTestMappingsMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedMonitoringControlTestComparisonController } from './monitoring-control-test-comparsion-controller';

class MonitoringResetTestsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    resetTestsMutation = new ObservedMutation(
        grcControllerResetControlTestMappingsMutation,
    );

    get isResetting(): boolean {
        return this.resetTestsMutation.isPending;
    }

    get hasResetError(): boolean {
        return this.resetTestsMutation.hasError;
    }

    resetTests = (controlId: number): void => {
        this.resetTestsMutation.mutate({
            path: { controlId },
        });

        when(
            () => !this.isResetting,
            () => {
                if (this.hasResetError) {
                    snackbarController.addSnackbar({
                        id: 'reset-tests-error',
                        props: {
                            title: t`Failed to reset tests`,
                            description: t`An error occurred while resetting the tests. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    sharedMonitorsController.invalidate();
                    sharedMonitoringControlTestComparisonController.invalidate();
                    sharedControlsDetailsStatsController.invalidate();

                    snackbarController.addSnackbar({
                        id: 'reset-tests-success',
                        props: {
                            title: t`Tests reset successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };
}

export const sharedMonitoringResetTestsMutationController =
    new MonitoringResetTestsMutationController();
