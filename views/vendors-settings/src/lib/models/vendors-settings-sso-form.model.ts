import { z } from 'zod';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';

class VendorsSettingsSsoFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    get vendorSettings() {
        return sharedVendorsSettingsController.vendorSettings;
    }

    getSchema(): FormSchema {
        return {
            isSsoIntegration: {
                type: 'checkbox',
                label: t`Show Okta single sign-on suggestions`,
                helpText: t`View applications from your Okta single sign-on as vendor suggestions in your current vendors tab. Review and add them as vendors.`,
                validator: z.boolean(),
                initialValue: this.vendorSettings?.isSsoIntegration ?? false,
            },
        };
    }
}

export const sharedVendorsSettingsSsoFormModel =
    new VendorsSettingsSsoFormModel();
