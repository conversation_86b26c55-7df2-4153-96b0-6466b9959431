import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    accessReviewPeriodControllerGetAccessReviewApplicationsWithWarningsOptions,
    accessReviewPeriodControllerListAccessReviewPeriodsOptions,
} from '@globals/api-sdk/queries';
import type {
    AccessApplicationSummaryResponseDto,
    AccessReviewPeriodResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class AccessReviewController {
    /**
     * Backend is NOT respecting pagination parameters, implementing frontend pagination workaround.
     */
    currentPage = 1;
    pageSize = DEFAULT_PAGE_SIZE;
    hasLoadedData = false;

    constructor() {
        makeAutoObservable(this);
    }

    accessReview = new ObservedQuery(
        accessReviewPeriodControllerGetAccessReviewApplicationsWithWarningsOptions,
    );

    accessReviewComplete = new ObservedQuery(
        accessReviewPeriodControllerListAccessReviewPeriodsOptions,
    );

    get accessReviewList(): AccessApplicationSummaryResponseDto[] {
        const allData = this.accessReview.data?.data ?? [];

        // Frontend pagination workaround since backend returns all data
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;

        return allData.slice(startIndex, endIndex);
    }

    get accessReviewListTotal(): number {
        // Return total count of all data for proper pagination controls
        return this.accessReview.data?.data.length ?? 0;
    }

    get isLoading(): boolean {
        return this.accessReview.isLoading;
    }

    loadAccessReview = (params: FetchDataResponseParams): void => {
        // Store pagination parameters for frontend pagination
        this.currentPage = params.pagination.page ?? 1;
        this.pageSize = params.pagination.pageSize;

        // Only load data from backend once, then just update pagination state
        if (this.hasLoadedData) {
            return;
        }

        // Load all data since backend doesn't respect pagination
        this.accessReview.load({
            query: {
                // Remove pagination parameters since backend ignores them
            },
        });
        this.hasLoadedData = true;
    };

    get accessReviewCompletedList(): AccessReviewPeriodResponseDto[] {
        return this.accessReviewComplete.data?.data ?? [];
    }

    get isLoadingCompleted(): boolean {
        return this.accessReviewComplete.isLoading;
    }
}

export const sharedAccessReviewController = new AccessReviewController();
