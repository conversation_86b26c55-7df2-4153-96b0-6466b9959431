import { isEmpty } from 'lodash-es';
import type { ClientLoader } from '@app/types';
import { sharedVendorsSettingsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { MetaFunction } from '@remix-run/node';
import { VendorsSettingsView } from '@views/vendors-settings';

export const meta: MetaFunction = () => [{ title: t`Vendors Settings` }];

export const clientLoader = action((): ClientLoader => {
    when(
        () => !isEmpty(sharedWorkspacesController.currentWorkspace),
        () => {
            sharedVendorsSettingsController.loadVendorsSettings();
            sharedVendorsSettingsController.loadQuestionnaireReminders();
        },
    );

    return {
        pageHeader: {
            title: t`Vendors Settings`,
            pageId: 'vendors-settings-page',
        },
    };
});

const VendorsSettings = (): React.JSX.Element => {
    return (
        <VendorsSettingsView data-testid="VendorsSettings" data-id="jP_-SXBm" />
    );
};

export default VendorsSettings;
